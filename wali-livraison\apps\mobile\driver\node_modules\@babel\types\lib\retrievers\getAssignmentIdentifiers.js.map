{"version": 3, "names": ["getAssignmentIdentifiers", "node", "search", "concat", "ids", "Object", "create", "length", "id", "pop", "type", "push", "elements", "left", "properties", "value", "argument", "operator", "name"], "sources": ["../../src/retrievers/getAssignmentIdentifiers.ts"], "sourcesContent": ["import type * as t from \"../index.ts\";\n\n/**\n * For the given node, generate a map from assignment id names to the identifier node.\n * Unlike getBindingIdentifiers, this function does not handle declarations and imports.\n * @param node the assignment expression or forXstatement\n * @returns an object map\n * @see getBindingIdentifiers\n */\nexport default function getAssignmentIdentifiers(\n  node: t.Node | t.Node[],\n): Record<string, t.Identifier> {\n  // null represents holes in an array pattern\n  const search: (t.Node | null)[] = [].concat(node);\n  const ids = Object.create(null);\n\n  while (search.length) {\n    const id = search.pop();\n    if (!id) continue;\n\n    switch (id.type) {\n      case \"ArrayPattern\":\n        search.push(...id.elements);\n        break;\n\n      case \"AssignmentExpression\":\n      case \"AssignmentPattern\":\n      case \"ForInStatement\":\n      case \"ForOfStatement\":\n        search.push(id.left);\n        break;\n\n      case \"ObjectPattern\":\n        search.push(...id.properties);\n        break;\n\n      case \"ObjectProperty\":\n        search.push(id.value);\n        break;\n\n      case \"RestElement\":\n      case \"UpdateExpression\":\n        search.push(id.argument);\n        break;\n\n      case \"UnaryExpression\":\n        if (id.operator === \"delete\") {\n          search.push(id.argument);\n        }\n        break;\n\n      case \"Identifier\":\n        ids[id.name] = id;\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  return ids;\n}\n"], "mappings": ";;;;;;AASe,SAASA,wBAAwBA,CAC9CC,IAAuB,EACO;EAE9B,MAAMC,MAAyB,GAAG,EAAE,CAACC,MAAM,CAACF,IAAI,CAAC;EACjD,MAAMG,GAAG,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAE/B,OAAOJ,MAAM,CAACK,MAAM,EAAE;IACpB,MAAMC,EAAE,GAAGN,MAAM,CAACO,GAAG,CAAC,CAAC;IACvB,IAAI,CAACD,EAAE,EAAE;IAET,QAAQA,EAAE,CAACE,IAAI;MACb,KAAK,cAAc;QACjBR,MAAM,CAACS,IAAI,CAAC,GAAGH,EAAE,CAACI,QAAQ,CAAC;QAC3B;MAEF,KAAK,sBAAsB;MAC3B,KAAK,mBAAmB;MACxB,KAAK,gBAAgB;MACrB,KAAK,gBAAgB;QACnBV,MAAM,CAACS,IAAI,CAACH,EAAE,CAACK,IAAI,CAAC;QACpB;MAEF,KAAK,eAAe;QAClBX,MAAM,CAACS,IAAI,CAAC,GAAGH,EAAE,CAACM,UAAU,CAAC;QAC7B;MAEF,KAAK,gBAAgB;QACnBZ,MAAM,CAACS,IAAI,CAACH,EAAE,CAACO,KAAK,CAAC;QACrB;MAEF,KAAK,aAAa;MAClB,KAAK,kBAAkB;QACrBb,MAAM,CAACS,IAAI,CAACH,EAAE,CAACQ,QAAQ,CAAC;QACxB;MAEF,KAAK,iBAAiB;QACpB,IAAIR,EAAE,CAACS,QAAQ,KAAK,QAAQ,EAAE;UAC5Bf,MAAM,CAACS,IAAI,CAACH,EAAE,CAACQ,QAAQ,CAAC;QAC1B;QACA;MAEF,KAAK,YAAY;QACfZ,GAAG,CAACI,EAAE,CAACU,IAAI,CAAC,GAAGV,EAAE;QACjB;MAEF;QACE;IACJ;EACF;EAEA,OAAOJ,GAAG;AACZ", "ignoreList": []}