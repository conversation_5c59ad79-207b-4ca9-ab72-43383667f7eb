{"version": 3, "sources": ["../../../../../src/start/doctor/typescript/updateTSConfig.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport chalk from 'chalk';\nimport fs from 'fs';\n\nimport * as Log from '../../../log';\n\nexport const baseTSConfigName = 'expo/tsconfig.base';\n\nexport async function updateTSConfigAsync({\n  tsConfigPath,\n}: {\n  tsConfigPath: string;\n}): Promise<void> {\n  const shouldGenerate = !fs.existsSync(tsConfigPath) || fs.statSync(tsConfigPath).size === 0;\n  if (shouldGenerate) {\n    await JsonFile.writeAsync(tsConfigPath, { compilerOptions: {} });\n  }\n\n  const projectTSConfig = JsonFile.read(tsConfigPath, {\n    // Some tsconfig.json files have a generated comment in the file.\n    json5: true,\n  });\n\n  projectTSConfig.compilerOptions ??= {};\n\n  const modifications: [string, string][] = [];\n\n  // If the extends field isn't defined, set it to the expo default\n  if (!projectTSConfig.extends) {\n    // if (projectTSConfig.extends !== baseTSConfigName) {\n    projectTSConfig.extends = baseTSConfigName;\n    modifications.push(['extends', baseTSConfigName]);\n  }\n\n  // If no changes, then quietly bail out\n  if (!modifications.length) {\n    return;\n  }\n\n  // Write changes and log out a summary of what changed\n  await JsonFile.writeAsync(tsConfigPath, projectTSConfig);\n\n  // If no changes, then quietly bail out\n  if (modifications.length === 0) {\n    return;\n  }\n\n  Log.log();\n\n  if (shouldGenerate) {\n    Log.log(chalk`{bold TypeScript}: A {cyan tsconfig.json} has been auto-generated`);\n  } else {\n    Log.log(\n      chalk`{bold TypeScript}: The {cyan tsconfig.json} has been updated {dim (Use EXPO_NO_TYPESCRIPT_SETUP to skip)}`\n    );\n    logModifications(modifications);\n  }\n  Log.log();\n}\n\nfunction logModifications(modifications: string[][]) {\n  Log.log();\n\n  Log.log(chalk`\\u203A {bold Required} modifications made to the {cyan tsconfig.json}:`);\n\n  Log.log();\n\n  // Sort the items based on key name length\n  printTable(modifications.sort((a, b) => a[0].length - b[0].length));\n\n  Log.log();\n}\n\nfunction printTable(items: string[][]) {\n  const tableFormat = (name: string, msg: string) =>\n    `  ${chalk.bold`${name}`} is now ${chalk.cyan(msg)}`;\n  for (const [key, value] of items) {\n    Log.log(tableFormat(key, value));\n  }\n}\n"], "names": ["baseTSConfigName", "updateTSConfigAsync", "tsConfigPath", "shouldGenerate", "fs", "existsSync", "statSync", "size", "JsonFile", "writeAsync", "compilerOptions", "projectTSConfig", "read", "json5", "modifications", "extends", "push", "length", "Log", "log", "chalk", "logModifications", "printTable", "sort", "a", "b", "items", "tableFormat", "name", "msg", "bold", "cyan", "key", "value"], "mappings": ";;;;;;;;;;;IAMaA,gBAAgB;eAAhBA;;IAESC,mBAAmB;eAAnBA;;;;gEARD;;;;;;;gEACH;;;;;;;gEACH;;;;;;6DAEM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,MAAMD,mBAAmB;AAEzB,eAAeC,oBAAoB,EACxCC,YAAY,EAGb;IACC,MAAMC,iBAAiB,CAACC,aAAE,CAACC,UAAU,CAACH,iBAAiBE,aAAE,CAACE,QAAQ,CAACJ,cAAcK,IAAI,KAAK;IAC1F,IAAIJ,gBAAgB;QAClB,MAAMK,mBAAQ,CAACC,UAAU,CAACP,cAAc;YAAEQ,iBAAiB,CAAC;QAAE;IAChE;IAEA,MAAMC,kBAAkBH,mBAAQ,CAACI,IAAI,CAACV,cAAc;QAClD,iEAAiE;QACjEW,OAAO;IACT;IAEAF,gBAAgBD,eAAe,KAAK,CAAC;IAErC,MAAMI,gBAAoC,EAAE;IAE5C,iEAAiE;IACjE,IAAI,CAACH,gBAAgBI,OAAO,EAAE;QAC5B,sDAAsD;QACtDJ,gBAAgBI,OAAO,GAAGf;QAC1Bc,cAAcE,IAAI,CAAC;YAAC;YAAWhB;SAAiB;IAClD;IAEA,uCAAuC;IACvC,IAAI,CAACc,cAAcG,MAAM,EAAE;QACzB;IACF;IAEA,sDAAsD;IACtD,MAAMT,mBAAQ,CAACC,UAAU,CAACP,cAAcS;IAExC,uCAAuC;IACvC,IAAIG,cAAcG,MAAM,KAAK,GAAG;QAC9B;IACF;IAEAC,KAAIC,GAAG;IAEP,IAAIhB,gBAAgB;QAClBe,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,iEAAiE,CAAC;IAClF,OAAO;QACLF,KAAIC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,yGAAyG,CAAC;QAElHC,iBAAiBP;IACnB;IACAI,KAAIC,GAAG;AACT;AAEA,SAASE,iBAAiBP,aAAyB;IACjDI,KAAIC,GAAG;IAEPD,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,sEAAsE,CAAC;IAErFF,KAAIC,GAAG;IAEP,0CAA0C;IAC1CG,WAAWR,cAAcS,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACP,MAAM,GAAGQ,CAAC,CAAC,EAAE,CAACR,MAAM;IAEjEC,KAAIC,GAAG;AACT;AAEA,SAASG,WAAWI,KAAiB;IACnC,MAAMC,cAAc,CAACC,MAAcC,MACjC,CAAC,EAAE,EAAET,gBAAK,CAACU,IAAI,CAAC,EAAEF,KAAK,CAAC,CAAC,QAAQ,EAAER,gBAAK,CAACW,IAAI,CAACF,MAAM;IACtD,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIP,MAAO;QAChCR,KAAIC,GAAG,CAACQ,YAAYK,KAAKC;IAC3B;AACF"}