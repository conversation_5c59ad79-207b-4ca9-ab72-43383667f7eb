{"version": 3, "sources": ["../../../src/install/fixPackages.ts"], "sourcesContent": ["import * as PackageManager from '@expo/package-manager';\nimport chalk from 'chalk';\n\nimport { applyPluginsAsync } from './applyPlugins';\nimport { installExpoPackageAsync } from './installExpoPackage';\nimport * as Log from '../log';\nimport { getOperationLog } from '../start/doctor/dependencies/getVersionedPackages';\nimport { getVersionedDependenciesAsync } from '../start/doctor/dependencies/validateDependenciesVersions';\nimport { groupBy } from '../utils/array';\n\n/**\n * Given a list of incompatible packages, installs the correct versions of the packages with the package manager used for the project.\n */\nexport async function fixPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    packageManager,\n    sdkVersion,\n    packageManagerArguments,\n  }: {\n    packages: Awaited<ReturnType<typeof getVersionedDependenciesAsync>>;\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n    /**\n     * SDK to version `packages` for.\n     * @example '44.0.0'\n     */\n    sdkVersion: string;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n  }\n): Promise<void> {\n  if (!packages.length) {\n    return;\n  }\n\n  const { dependencies = [], devDependencies = [] } = groupBy(packages, (dep) => dep.packageType);\n  const versioningMessages = getOperationLog({\n    othersCount: 0, // All fixable packages are versioned\n    nativeModulesCount: packages.length,\n    sdkVersion,\n  });\n\n  // display all packages to update, including expo package\n  Log.log(\n    chalk`\\u203A Installing ${\n      versioningMessages.length ? versioningMessages.join(' and ') + ' ' : ''\n    }using {bold ${packageManager.name}}`\n  );\n\n  // if updating expo package, install this first, then run expo install --fix again under new version\n  const expoDep = dependencies.find((dep) => dep.packageName === 'expo');\n  if (expoDep) {\n    await installExpoPackageAsync(projectRoot, {\n      packageManager,\n      packageManagerArguments,\n      expoPackageToInstall: `expo@${expoDep.expectedVersionOrRange}`,\n      followUpCommandArgs: ['--fix'],\n    });\n    // follow-up commands will be spawned in a detached process, so return immediately\n    return;\n  }\n\n  if (dependencies.length) {\n    const versionedPackages = dependencies.map(\n      (dep) => `${dep.packageName}@${dep.expectedVersionOrRange}`\n    );\n\n    await packageManager.addAsync([...packageManagerArguments, ...versionedPackages]);\n\n    await applyPluginsAsync(projectRoot, versionedPackages);\n  }\n\n  if (devDependencies.length) {\n    await packageManager.addDevAsync([\n      ...packageManagerArguments,\n      ...devDependencies.map((dep) => `${dep.packageName}@${dep.expectedVersionOrRange}`),\n    ]);\n  }\n}\n"], "names": ["fixPackagesAsync", "projectRoot", "packages", "packageManager", "sdkVersion", "packageManagerArguments", "length", "dependencies", "devDependencies", "groupBy", "dep", "packageType", "versioningMessages", "getOperationLog", "othersCount", "nativeModulesCount", "Log", "log", "chalk", "join", "name", "expoDep", "find", "packageName", "installExpoPackageAsync", "expoPackageToInstall", "expectedVersionOrRange", "followUpCommandArgs", "versionedPackages", "map", "addAsync", "applyPluginsAsync", "addDevAsync"], "mappings": ";;;;+BAasBA;;;eAAAA;;;;gEAZJ;;;;;;8BAEgB;oCACM;6DACnB;sCACW;uBAER;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKjB,eAAeA,iBACpBC,WAAmB,EACnB,EACEC,QAAQ,EACRC,cAAc,EACdC,UAAU,EACVC,uBAAuB,EAexB;IAED,IAAI,CAACH,SAASI,MAAM,EAAE;QACpB;IACF;IAEA,MAAM,EAAEC,eAAe,EAAE,EAAEC,kBAAkB,EAAE,EAAE,GAAGC,IAAAA,cAAO,EAACP,UAAU,CAACQ,MAAQA,IAAIC,WAAW;IAC9F,MAAMC,qBAAqBC,IAAAA,qCAAe,EAAC;QACzCC,aAAa;QACbC,oBAAoBb,SAASI,MAAM;QACnCF;IACF;IAEA,yDAAyD;IACzDY,KAAIC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,kBAAkB,EACtBN,mBAAmBN,MAAM,GAAGM,mBAAmBO,IAAI,CAAC,WAAW,MAAM,GACtE,YAAY,EAAEhB,eAAeiB,IAAI,CAAC,CAAC,CAAC;IAGvC,oGAAoG;IACpG,MAAMC,UAAUd,aAAae,IAAI,CAAC,CAACZ,MAAQA,IAAIa,WAAW,KAAK;IAC/D,IAAIF,SAAS;QACX,MAAMG,IAAAA,2CAAuB,EAACvB,aAAa;YACzCE;YACAE;YACAoB,sBAAsB,CAAC,KAAK,EAAEJ,QAAQK,sBAAsB,EAAE;YAC9DC,qBAAqB;gBAAC;aAAQ;QAChC;QACA,kFAAkF;QAClF;IACF;IAEA,IAAIpB,aAAaD,MAAM,EAAE;QACvB,MAAMsB,oBAAoBrB,aAAasB,GAAG,CACxC,CAACnB,MAAQ,GAAGA,IAAIa,WAAW,CAAC,CAAC,EAAEb,IAAIgB,sBAAsB,EAAE;QAG7D,MAAMvB,eAAe2B,QAAQ,CAAC;eAAIzB;eAA4BuB;SAAkB;QAEhF,MAAMG,IAAAA,+BAAiB,EAAC9B,aAAa2B;IACvC;IAEA,IAAIpB,gBAAgBF,MAAM,EAAE;QAC1B,MAAMH,eAAe6B,WAAW,CAAC;eAC5B3B;eACAG,gBAAgBqB,GAAG,CAAC,CAACnB,MAAQ,GAAGA,IAAIa,WAAW,CAAC,CAAC,EAAEb,IAAIgB,sBAAsB,EAAE;SACnF;IACH;AACF"}