import { AuthService, RegisterDto, LoginDto } from './auth.service';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        access_token: string;
        user: {
            id: string;
            phone: string | null;
            email: string | null;
            role: import(".prisma/client").$Enums.UserRole;
        };
    }>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: {
            id: string;
            phone: string | null;
            email: string | null;
            role: import(".prisma/client").$Enums.UserRole;
        };
    }>;
    getProfile(req: any): Promise<any>;
    refresh(req: any): Promise<{
        access_token: string;
        user: any;
    }>;
}
