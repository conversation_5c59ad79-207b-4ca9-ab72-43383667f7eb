{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeDebuggerSetBreakpointByUrl.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { <PERSON><PERSON><PERSON><PERSON> } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest } from '../types';\n\n/**\n * <PERSON><PERSON> and vs<PERSON> have trouble setting breakpoints by `urlRegex` through `Debugger.setBreakpointByUrl`.\n * Vscode adds `file://` to a URL containing `http://`, which confuses <PERSON><PERSON> and sets it to the wrong location.\n * <PERSON><PERSON> needs to create the breakpoint to get the proper ID, but it must be unbounded.\n * Once the sourcemap is loaded, vscode will rebind the unbounded breakpoint to the correct location (using `Debugger.setBreakpoint`).\n */\nexport class VscodeDebuggerSetBreakpointByUrlHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<DebuggerSetBreakpointByUrl>) {\n    if (message.method === 'Debugger.setBreakpointByUrl' && message.params.urlRegex) {\n      // Explicitly force the breakpoint to be unbounded\n      message.params.url = 'file://__invalid_url__';\n      delete message.params.urlRegex;\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Debugger/#method-setBreakpointByUrl */\nexport type DebuggerSetBreakpointByUrl = CdpMessage<\n  'Debugger.setBreakpointByUrl',\n  Protocol.Debugger.SetBreakpointByUrlRequest,\n  Protocol.Debugger.SetBreakpointByUrlResponse\n>;\n"], "names": ["VscodeDebuggerSetBreakpointByUrlHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "params", "urlRegex", "url"], "mappings": ";;;;+BAYaA;;;eAAAA;;;gCAVkB;iCACC;AASzB,MAAMA,gDAAgDC,8BAAc;IACzEC,YAAY;QACV,OAAOC,IAAAA,gCAAe,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,MAAM;IACtD;IAEAC,sBAAsBC,OAAoD,EAAE;QAC1E,IAAIA,QAAQC,MAAM,KAAK,iCAAiCD,QAAQE,MAAM,CAACC,QAAQ,EAAE;YAC/E,kDAAkD;YAClDH,QAAQE,MAAM,CAACE,GAAG,GAAG;YACrB,OAAOJ,QAAQE,MAAM,CAACC,QAAQ;QAChC;QAEA,OAAO;IACT;AACF"}