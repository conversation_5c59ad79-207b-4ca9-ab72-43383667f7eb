import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const SendParcelScreen = () => {
  const navigation = useNavigation();
  const [formData, setFormData] = useState({
    senderName: '',
    senderPhone: '',
    pickupAddress: '',
    recipientName: '',
    recipientPhone: '',
    deliveryAddress: '',
    description: '',
    weight: '',
    value: '',
  });

  const handleSubmit = () => {
    if (!formData.senderName || !formData.senderPhone || !formData.pickupAddress || 
        !formData.recipientName || !formData.recipientPhone || !formData.deliveryAddress) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    // TODO: Calculer le prix et procéder à la commande
    Alert.alert(
      'Confirmation',
      'Votre demande d\'envoi de colis a été enregistrée. Un livreur vous contactera bientôt.',
      [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Sender Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations de l'expéditeur</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom complet *</Text>
            <TextInput
              style={styles.input}
              placeholder="Votre nom complet"
              value={formData.senderName}
              onChangeText={(text) => setFormData({ ...formData, senderName: text })}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Numéro de téléphone *</Text>
            <TextInput
              style={styles.input}
              placeholder="+225 XX XX XX XX XX"
              value={formData.senderPhone}
              onChangeText={(text) => setFormData({ ...formData, senderPhone: text })}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Adresse de collecte *</Text>
            <View style={styles.addressInputContainer}>
              <TextInput
                style={styles.addressInput}
                placeholder="Adresse complète de collecte"
                value={formData.pickupAddress}
                onChangeText={(text) => setFormData({ ...formData, pickupAddress: text })}
                multiline
              />
              <TouchableOpacity style={styles.locationButton}>
                <Ionicons name="location" size={20} color="#3B82F6" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Recipient Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations du destinataire</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nom complet *</Text>
            <TextInput
              style={styles.input}
              placeholder="Nom du destinataire"
              value={formData.recipientName}
              onChangeText={(text) => setFormData({ ...formData, recipientName: text })}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Numéro de téléphone *</Text>
            <TextInput
              style={styles.input}
              placeholder="+225 XX XX XX XX XX"
              value={formData.recipientPhone}
              onChangeText={(text) => setFormData({ ...formData, recipientPhone: text })}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Adresse de livraison *</Text>
            <View style={styles.addressInputContainer}>
              <TextInput
                style={styles.addressInput}
                placeholder="Adresse complète de livraison"
                value={formData.deliveryAddress}
                onChangeText={(text) => setFormData({ ...formData, deliveryAddress: text })}
                multiline
              />
              <TouchableOpacity style={styles.locationButton}>
                <Ionicons name="location" size={20} color="#3B82F6" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Package Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informations du colis</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Description du contenu</Text>
            <TextInput
              style={styles.textArea}
              placeholder="Décrivez brièvement le contenu du colis"
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.label}>Poids approximatif (kg)</Text>
              <TextInput
                style={styles.input}
                placeholder="Ex: 2.5"
                value={formData.weight}
                onChangeText={(text) => setFormData({ ...formData, weight: text })}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputContainer, styles.halfWidth]}>
              <Text style={styles.label}>Valeur (FCFA)</Text>
              <TextInput
                style={styles.input}
                placeholder="Ex: 50000"
                value={formData.value}
                onChangeText={(text) => setFormData({ ...formData, value: text })}
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Price Estimation */}
        <View style={styles.priceSection}>
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Prix estimé :</Text>
            <Text style={styles.priceValue}>2 500 FCFA</Text>
          </View>
          <Text style={styles.priceNote}>
            Le prix final sera confirmé par le livreur selon la distance exacte
          </Text>
        </View>

        {/* Submit Button */}
        <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
          <Text style={styles.submitButtonText}>Confirmer l'envoi</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollContent: {
    padding: 20,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
    height: 80,
    textAlignVertical: 'top',
  },
  addressInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  addressInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
    marginRight: 8,
    minHeight: 48,
  },
  locationButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#EBF4FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  priceSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  priceValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#3B82F6',
  },
  priceNote: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic',
  },
  submitButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default SendParcelScreen;
