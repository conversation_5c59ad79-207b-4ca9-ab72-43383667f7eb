{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithOffline.ts"], "sourcesContent": ["import { FetchLike } from './client.types';\nimport { env } from '../../utils/env';\n\nconst debug = require('debug')('expo:api:fetch:offline') as typeof console.log;\n\n/** Wrap fetch with support for `EXPO_OFFLINE` to disable network requests. */\nexport function wrapFetchWithOffline(fetchFunction: FetchLike): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return function fetchWithOffline(url, options = {}) {\n    if (env.EXPO_OFFLINE) {\n      debug('Skipping network request: ' + url);\n      const abortController = new AbortController();\n      abortController.abort();\n      options.signal = abortController.signal;\n    }\n    return fetchFunction(url, options);\n  };\n}\n"], "names": ["wrapFetchWithOffline", "debug", "require", "fetchFunction", "fetchWithOffline", "url", "options", "env", "EXPO_OFFLINE", "abortController", "AbortController", "abort", "signal"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;qBALI;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASF,qBAAqBG,aAAwB;IAC3D,mFAAmF;IACnF,OAAO,SAASC,iBAAiBC,GAAG,EAAEC,UAAU,CAAC,CAAC;QAChD,IAAIC,QAAG,CAACC,YAAY,EAAE;YACpBP,MAAM,+BAA+BI;YACrC,MAAMI,kBAAkB,IAAIC;YAC5BD,gBAAgBE,KAAK;YACrBL,QAAQM,MAAM,GAAGH,gBAAgBG,MAAM;QACzC;QACA,OAAOT,cAAcE,KAAKC;IAC5B;AACF"}