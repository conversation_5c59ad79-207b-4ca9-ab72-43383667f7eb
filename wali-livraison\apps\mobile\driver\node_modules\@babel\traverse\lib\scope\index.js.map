{"version": 3, "names": ["_renamer", "require", "_index", "_binding", "_t", "t", "_cache", "globalsBuiltinLower", "globalsBuiltinUpper", "assignmentExpression", "callExpression", "cloneNode", "getBindingIdentifiers", "identifier", "isArrayExpression", "isBinary", "isCallExpression", "isClass", "isClassBody", "isClassDeclaration", "isExportAllDeclaration", "isExportDefaultDeclaration", "isExportNamedDeclaration", "isFunctionDeclaration", "isIdentifier", "isImportDeclaration", "isLiteral", "isMemberExpression", "isMethod", "isModuleSpecifier", "is<PERSON>ull<PERSON>iteral", "isObjectExpression", "isProperty", "isPureish", "isRegExpLiteral", "is<PERSON><PERSON><PERSON>", "isTaggedTemplateExpression", "isTemplateLiteral", "isThisExpression", "isUnaryExpression", "isVariableDeclaration", "expressionStatement", "matchesPattern", "memberExpression", "numericLiteral", "toIdentifier", "variableDeclaration", "variableDeclarator", "isRecordExpression", "isTupleExpression", "isObjectProperty", "isTopicReference", "isMetaProperty", "isPrivateName", "isExportDeclaration", "buildUndefinedNode", "sequenceExpression", "gatherNodeParts", "node", "parts", "type", "_node$specifiers", "source", "specifiers", "length", "e", "declaration", "local", "push", "value", "object", "property", "name", "callee", "properties", "argument", "key", "left", "id", "expression", "meta", "openingElement", "openingFragment", "namespace", "resetScope", "scope", "references", "Object", "create", "uids", "bindings", "globals", "NOT_LOCAL_BINDING", "Symbol", "for", "collectorVisitor", "ForStatement", "path", "declar", "get", "isVar", "parentScope", "getFunctionParent", "getProgramParent", "registerBinding", "Declaration", "isBlockScoped", "parent", "registerDeclaration", "ImportDeclaration", "getBlockParent", "TSImportEqualsDeclaration", "ReferencedIdentifier", "state", "isTSQualifiedName", "right", "parentPath", "isTSImportEqualsDeclaration", "ForXStatement", "isPattern", "constantViolations", "ExportDeclaration", "exit", "binding", "getBinding", "reference", "decl", "declarations", "keys", "LabeledStatement", "AssignmentExpression", "assignments", "UpdateExpression", "UnaryExpression", "operator", "BlockScoped", "CatchClause", "Function", "params", "param", "isFunctionExpression", "ClassExpression", "TSTypeAnnotation", "skip", "scopeVisitor", "uid", "<PERSON><PERSON>", "constructor", "block", "inited", "labels", "referencesSet", "uidsSet", "data", "crawling", "cached", "scopeCache", "set", "Map", "defineProperties", "enumerable", "configurable", "writable", "_parent", "_path", "shouldSkip", "<PERSON><PERSON><PERSON>", "isScope", "Error", "generateDeclaredUidIdentifier", "generateUidIdentifier", "generateUid", "replace", "i", "<PERSON><PERSON><PERSON><PERSON>", "hasBinding", "hasGlobal", "hasReference", "program", "generateUidBasedOnNode", "defaultName", "join", "slice", "generateUidIdentifierBasedOnNode", "isStatic", "constant", "maybeGenerateMemoised", "dont<PERSON><PERSON>", "checkBlockScopedCollisions", "kind", "duplicate", "hub", "buildError", "TypeError", "rename", "old<PERSON>ame", "newName", "renamer", "Renamer", "arguments", "dump", "sep", "repeat", "console", "log", "violations", "get<PERSON><PERSON><PERSON>", "registerLabel", "label", "isLabeledStatement", "declare", "isTypeDeclaration", "importKind", "specifier", "isTypeSpecifier", "isImportSpecifier", "registerConstantViolation", "ids", "getAssignmentIdentifiers", "_this$getBinding", "reassign", "bindingPath", "ReferenceError", "declarators", "getOuterBindingIdentifiers", "getOwnBinding", "Binding", "addGlobal", "hasUid", "isPure", "constantsOnly", "_node$decorators", "superClass", "decorators", "body", "method", "elem", "elements", "prop", "_node$decorators2", "computed", "_node$decorators3", "static", "expressions", "tag", "noGlobals", "quasi", "isStringLiteral", "setData", "val", "getData", "removeData", "init", "crawl", "isProgram", "programParent", "traverse", "visitors", "merge", "visit", "enter", "call", "typeVisitors", "ref", "opts", "getPatternParent", "isBlockStatement", "isSwitchStatement", "unique", "isFunction", "pushContainer", "isLoop", "isCatchClause", "ensureBlock", "blockHoist", "_blockHoist", "dataKey", "declar<PERSON><PERSON>", "unshiftContainer", "declarator", "len", "isFunctionParent", "isBlockParent", "getAllBindings", "bindingIdentifierEquals", "getBindingIdentifier", "previousPath", "_previousPath", "isArrowFunctionExpression", "_this$getBinding2", "getOwnBindingIdentifier", "hasOwnBinding", "noUids", "upToScope", "includes", "contextVariables", "parentHasBinding", "_this$parent", "moveBindingTo", "info", "removeOwnBinding", "removeBinding", "_this$getBinding3", "hoistVariables", "emit", "seen", "Set", "isVariableDeclarator", "has", "add", "firstId", "isFor", "replaceWith", "remove", "expr", "isForStatement", "exports", "default", "prototype", "_renameFromMap", "map", "_generateUid", "toArray", "arrayLikeIsIterable", "isGenericType", "helper<PERSON><PERSON>", "args", "unshift", "addHelper", "getAllBindingsOfKind", "kinds", "parentBlock"], "sources": ["../../src/scope/index.ts"], "sourcesContent": ["import Renamer from \"./lib/renamer.ts\";\nimport type NodePath from \"../path/index.ts\";\nimport traverse from \"../index.ts\";\nimport Binding from \"./binding.ts\";\nimport type { BindingKind } from \"./binding.ts\";\nimport globalsBuiltinLower from \"@babel/helper-globals/data/builtin-lower.json\" with { type: \"json\" };\nimport globalsBuiltinUpper from \"@babel/helper-globals/data/builtin-upper.json\" with { type: \"json\" };\nimport {\n  assignmentExpression,\n  callExpression,\n  cloneNode,\n  getBindingIdentifiers,\n  identifier,\n  isArrayExpression,\n  isBinary,\n  isCallExpression,\n  isClass,\n  isClassBody,\n  isClassDeclaration,\n  isExportAllDeclaration,\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n  isFunctionDeclaration,\n  isIdentifier,\n  isImportDeclaration,\n  isLiteral,\n  isMemberExpression,\n  isMethod,\n  isModuleSpecifier,\n  isNullLiteral,\n  isObjectExpression,\n  isProperty,\n  isPureish,\n  isRegExpLiteral,\n  isSuper,\n  isTaggedTemplateExpression,\n  isTemplateLiteral,\n  isThisExpression,\n  isUnaryExpression,\n  isVariableDeclaration,\n  expressionStatement,\n  matchesPattern,\n  memberExpression,\n  numericLiteral,\n  toIdentifier,\n  variableDeclaration,\n  variableDeclarator,\n  isRecordExpression,\n  isTupleExpression,\n  isObjectProperty,\n  isTopicReference,\n  isMetaProperty,\n  isPrivateName,\n  isExportDeclaration,\n  buildUndefinedNode,\n  sequenceExpression,\n} from \"@babel/types\";\nimport * as t from \"@babel/types\";\nimport { scope as scopeCache } from \"../cache.ts\";\nimport type { ExplodedVisitor, Visitor } from \"../types.ts\";\n\ntype NodePart = string | number | bigint | boolean;\n// Recursively gathers the identifying names of a node.\nfunction gatherNodeParts(node: t.Node, parts: NodePart[]) {\n  switch (node?.type) {\n    default:\n      if (isImportDeclaration(node) || isExportDeclaration(node)) {\n        if (\n          (isExportAllDeclaration(node) ||\n            isExportNamedDeclaration(node) ||\n            isImportDeclaration(node)) &&\n          node.source\n        ) {\n          gatherNodeParts(node.source, parts);\n        } else if (\n          (isExportNamedDeclaration(node) || isImportDeclaration(node)) &&\n          node.specifiers?.length\n        ) {\n          for (const e of node.specifiers) gatherNodeParts(e, parts);\n        } else if (\n          (isExportDefaultDeclaration(node) ||\n            isExportNamedDeclaration(node)) &&\n          node.declaration\n        ) {\n          gatherNodeParts(node.declaration, parts);\n        }\n      } else if (isModuleSpecifier(node)) {\n        // todo(flow->ts): should condition instead be:\n        //    ```\n        //    t.isExportSpecifier(node) ||\n        //    t.isImportDefaultSpecifier(node) ||\n        //    t.isImportNamespaceSpecifier(node) ||\n        //    t.isImportSpecifier(node)\n        //    ```\n        //    allowing only nodes with `.local`?\n        // @ts-expect-error todo(flow->ts)\n        gatherNodeParts(node.local, parts);\n      } else if (\n        isLiteral(node) &&\n        !isNullLiteral(node) &&\n        !isRegExpLiteral(node) &&\n        !isTemplateLiteral(node)\n      ) {\n        parts.push(node.value);\n      }\n      break;\n\n    case \"MemberExpression\":\n    case \"OptionalMemberExpression\":\n    case \"JSXMemberExpression\":\n      gatherNodeParts(node.object, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"Identifier\":\n    case \"JSXIdentifier\":\n      parts.push(node.name);\n      break;\n\n    case \"CallExpression\":\n    case \"OptionalCallExpression\":\n    case \"NewExpression\":\n      gatherNodeParts(node.callee, parts);\n      break;\n\n    case \"ObjectExpression\":\n    case \"ObjectPattern\":\n      for (const e of node.properties) {\n        gatherNodeParts(e, parts);\n      }\n      break;\n\n    case \"SpreadElement\":\n    case \"RestElement\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"ObjectProperty\":\n    case \"ObjectMethod\":\n    case \"ClassProperty\":\n    case \"ClassMethod\":\n    case \"ClassPrivateProperty\":\n    case \"ClassPrivateMethod\":\n      gatherNodeParts(node.key, parts);\n      break;\n\n    case \"ThisExpression\":\n      parts.push(\"this\");\n      break;\n\n    case \"Super\":\n      parts.push(\"super\");\n      break;\n\n    case \"Import\":\n    case \"ImportExpression\":\n      parts.push(\"import\");\n      break;\n\n    case \"DoExpression\":\n      parts.push(\"do\");\n      break;\n\n    case \"YieldExpression\":\n      parts.push(\"yield\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AwaitExpression\":\n      parts.push(\"await\");\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"AssignmentExpression\":\n      gatherNodeParts(node.left, parts);\n      break;\n\n    case \"VariableDeclarator\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"FunctionExpression\":\n    case \"FunctionDeclaration\":\n    case \"ClassExpression\":\n    case \"ClassDeclaration\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"PrivateName\":\n      gatherNodeParts(node.id, parts);\n      break;\n\n    case \"ParenthesizedExpression\":\n      gatherNodeParts(node.expression, parts);\n      break;\n\n    case \"UnaryExpression\":\n    case \"UpdateExpression\":\n      gatherNodeParts(node.argument, parts);\n      break;\n\n    case \"MetaProperty\":\n      gatherNodeParts(node.meta, parts);\n      gatherNodeParts(node.property, parts);\n      break;\n\n    case \"JSXElement\":\n      gatherNodeParts(node.openingElement, parts);\n      break;\n\n    case \"JSXOpeningElement\":\n      gatherNodeParts(node.name, parts);\n      break;\n\n    case \"JSXFragment\":\n      gatherNodeParts(node.openingFragment, parts);\n      break;\n\n    case \"JSXOpeningFragment\":\n      parts.push(\"Fragment\");\n      break;\n\n    case \"JSXNamespacedName\":\n      gatherNodeParts(node.namespace, parts);\n      gatherNodeParts(node.name, parts);\n      break;\n  }\n}\n\nfunction resetScope(scope: Scope) {\n  if (!process.env.BABEL_8_BREAKING) {\n    // @ts-expect-error(Babel 7 vs Babel 8)\n    scope.references = Object.create(null);\n    // @ts-expect-error(Babel 7 vs Babel 8)\n    scope.uids = Object.create(null);\n  } else if (scope.path.type === \"Program\") {\n    scope.referencesSet = new Set();\n    scope.uidsSet = new Set();\n  }\n\n  scope.bindings = Object.create(null);\n  scope.globals = Object.create(null);\n}\n\ninterface CollectVisitorState {\n  assignments: NodePath<t.AssignmentExpression>[];\n  references: NodePath<t.Identifier | t.JSXIdentifier>[];\n  constantViolations: NodePath[];\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var NOT_LOCAL_BINDING = Symbol.for(\n    \"should not be considered a local binding\",\n  );\n}\n\nconst collectorVisitor: Visitor<CollectVisitorState> = {\n  ForStatement(path) {\n    const declar = path.get(\"init\");\n    // delegate block scope handling to the `BlockScoped` method\n    if (declar.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", declar);\n    }\n  },\n\n  Declaration(path) {\n    // delegate block scope handling to the `BlockScoped` method\n    if (path.isBlockScoped()) return;\n\n    // delegate import handing to the `ImportDeclaration` method\n    if (path.isImportDeclaration()) return;\n\n    // this will be hit again once we traverse into it after this iteration\n    if (path.isExportDeclaration()) return;\n\n    // we've ran into a declaration!\n    const parent =\n      path.scope.getFunctionParent() || path.scope.getProgramParent();\n    parent.registerDeclaration(path);\n  },\n\n  ImportDeclaration(path) {\n    // import may only appear in the top level or inside a module/namespace (for TS/flow)\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  TSImportEqualsDeclaration(path) {\n    const parent = path.scope.getBlockParent();\n\n    parent.registerDeclaration(path);\n  },\n\n  ReferencedIdentifier(path, state) {\n    if (t.isTSQualifiedName(path.parent) && path.parent.right === path.node) {\n      return;\n    }\n    if (path.parentPath.isTSImportEqualsDeclaration()) return;\n    state.references.push(path);\n  },\n\n  ForXStatement(path, state) {\n    const left = path.get(\"left\");\n    if (left.isPattern() || left.isIdentifier()) {\n      state.constantViolations.push(path);\n    }\n    // delegate block scope handling to the `BlockScoped` method\n    else if (left.isVar()) {\n      const { scope } = path;\n      const parentScope = scope.getFunctionParent() || scope.getProgramParent();\n      parentScope.registerBinding(\"var\", left);\n    }\n  },\n\n  ExportDeclaration: {\n    exit(path) {\n      const { node, scope } = path;\n      // ExportAllDeclaration does not have `declaration`\n      if (isExportAllDeclaration(node)) return;\n      const declar = node.declaration;\n      if (isClassDeclaration(declar) || isFunctionDeclaration(declar)) {\n        const id = declar.id;\n        if (!id) return;\n\n        const binding = scope.getBinding(id.name);\n        binding?.reference(path);\n      } else if (isVariableDeclaration(declar)) {\n        for (const decl of declar.declarations) {\n          for (const name of Object.keys(getBindingIdentifiers(decl))) {\n            const binding = scope.getBinding(name);\n            binding?.reference(path);\n          }\n        }\n      }\n    },\n  },\n\n  LabeledStatement(path) {\n    path.scope.getBlockParent().registerDeclaration(path);\n  },\n\n  AssignmentExpression(path, state) {\n    state.assignments.push(path);\n  },\n\n  UpdateExpression(path, state) {\n    state.constantViolations.push(path);\n  },\n\n  UnaryExpression(path, state) {\n    if (path.node.operator === \"delete\") {\n      state.constantViolations.push(path);\n    }\n  },\n\n  BlockScoped(path) {\n    let scope = path.scope;\n    if (scope.path === path) scope = scope.parent;\n\n    const parent = scope.getBlockParent();\n    parent.registerDeclaration(path);\n\n    // Register class identifier in class' scope if this is a class declaration.\n    if (path.isClassDeclaration() && path.node.id) {\n      const id = path.node.id;\n      const name = id.name;\n\n      path.scope.bindings[name] = path.scope.parent.getBinding(name);\n    }\n  },\n\n  CatchClause(path) {\n    path.scope.registerBinding(\"let\", path);\n  },\n\n  Function(path) {\n    const params: Array<NodePath> = path.get(\"params\");\n    for (const param of params) {\n      path.scope.registerBinding(\"param\", param);\n    }\n\n    // Register function expression id after params. When the id\n    // collides with a function param, the id effectively can't be\n    // referenced: here we registered it as a constantViolation\n    if (\n      path.isFunctionExpression() &&\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  ClassExpression(path) {\n    if (\n      path.node.id &&\n      (process.env.BABEL_8_BREAKING ||\n        // @ts-expect-error Fixme: document symbol ast properties\n        !path.node.id[NOT_LOCAL_BINDING])\n    ) {\n      path.scope.registerBinding(\"local\", path.get(\"id\"), path);\n    }\n  },\n\n  TSTypeAnnotation(path) {\n    path.skip();\n  },\n};\n\nlet scopeVisitor: ExplodedVisitor<CollectVisitorState>;\n\nlet uid = 0;\n\nexport type { Binding };\n\nexport { Scope as default };\nclass Scope {\n  uid;\n\n  path: NodePath;\n  block: t.Pattern | t.Scopable;\n\n  inited;\n\n  labels: Map<string, NodePath<t.LabeledStatement>>;\n  bindings: { [name: string]: Binding };\n  /** Only defined in the program scope */\n  referencesSet?: Set<string>;\n  globals: { [name: string]: t.Identifier | t.JSXIdentifier };\n  /** Only defined in the program scope */\n  uidsSet?: Set<string>;\n  data: { [key: string | symbol]: unknown };\n  crawling: boolean;\n\n  /**\n   * This searches the current \"scope\" and collects all references/bindings\n   * within.\n   */\n  constructor(path: NodePath<t.Pattern | t.Scopable>) {\n    const { node } = path;\n    const cached = scopeCache.get(node);\n    // Sometimes, a scopable path is placed higher in the AST tree.\n    // In these cases, have to create a new Scope.\n    if (cached?.path === path) {\n      return cached;\n    }\n    scopeCache.set(node, this);\n\n    this.uid = uid++;\n\n    this.block = node;\n    this.path = path;\n\n    this.labels = new Map();\n    this.inited = false;\n\n    if (!process.env.BABEL_8_BREAKING) {\n      // Shadow the Babel 8 removal getters\n      Object.defineProperties(this, {\n        references: {\n          enumerable: true,\n          configurable: true,\n          writable: true,\n          value: Object.create(null),\n        },\n        uids: {\n          enumerable: true,\n          configurable: true,\n          writable: true,\n          value: Object.create(null),\n        },\n      });\n    }\n  }\n\n  /**\n   * Globals.\n   */\n\n  static globals = [...globalsBuiltinLower, ...globalsBuiltinUpper];\n\n  /**\n   * Variables available in current context.\n   */\n\n  static contextVariables = [\"arguments\", \"undefined\", \"Infinity\", \"NaN\"];\n\n  get parent() {\n    let parent,\n      path = this.path;\n    do {\n      // Skip method scope if coming from inside computed key or decorator expression\n      const shouldSkip = path.key === \"key\" || path.listKey === \"decorators\";\n      path = path.parentPath;\n      if (shouldSkip && path.isMethod()) path = path.parentPath;\n      if (path?.isScope()) parent = path;\n    } while (path && !parent);\n\n    return parent?.scope;\n  }\n\n  get references() {\n    throw new Error(\n      \"Scope#references is not available in Babel 8. Use Scope#referencesSet instead.\",\n    );\n  }\n\n  get uids() {\n    throw new Error(\n      \"Scope#uids is not available in Babel 8. Use Scope#uidsSet instead.\",\n    );\n  }\n\n  /**\n   * Generate a unique identifier and add it to the current scope.\n   */\n\n  generateDeclaredUidIdentifier(name?: string) {\n    const id = this.generateUidIdentifier(name);\n    this.push({ id });\n    return cloneNode(id);\n  }\n\n  /**\n   * Generate a unique identifier.\n   */\n\n  generateUidIdentifier(name?: string) {\n    return identifier(this.generateUid(name));\n  }\n\n  /**\n   * Generate a unique `_id1` binding.\n   */\n\n  generateUid(name: string = \"temp\"): string {\n    name = toIdentifier(name).replace(/^_+/, \"\").replace(/\\d+$/g, \"\");\n\n    let uid;\n    let i = 0;\n    do {\n      uid = `_${name}`;\n\n      // Ideally we would just use (i - 1) as the suffix, but that generates\n      // unnecessary changes in every single file generated by Babel :)\n      //\n      // i:       0        1  2  3  4  5  6  7  8  9 10 11 12 13 14 ...\n      // suffix:  (empty)  2  3  4  5  6  7  8  9  0  1 10 11 12 13 ...\n      if (i >= 11) uid += i - 1;\n      else if (i >= 9) uid += i - 9;\n      else if (i >= 1) uid += i + 1;\n      i++;\n    } while (\n      this.hasLabel(uid) ||\n      this.hasBinding(uid) ||\n      this.hasGlobal(uid) ||\n      this.hasReference(uid)\n    );\n\n    const program = this.getProgramParent();\n    if (process.env.BABEL_8_BREAKING) {\n      program.referencesSet.add(uid);\n      program.uidsSet.add(uid);\n    } else {\n      // @ts-expect-error Babel 7\n      program.references[uid] = true;\n      // @ts-expect-error Babel 7\n      program.uids[uid] = true;\n    }\n\n    return uid;\n  }\n\n  generateUidBasedOnNode(node: t.Node, defaultName?: string) {\n    const parts: NodePart[] = [];\n    gatherNodeParts(node, parts);\n\n    let id = parts.join(\"$\");\n    id = id.replace(/^_/, \"\") || defaultName || \"ref\";\n\n    return this.generateUid(id.slice(0, 20));\n  }\n\n  /**\n   * Generate a unique identifier based on a node.\n   */\n\n  generateUidIdentifierBasedOnNode(node: t.Node, defaultName?: string) {\n    return identifier(this.generateUidBasedOnNode(node, defaultName));\n  }\n\n  /**\n   * Determine whether evaluating the specific input `node` is a consequenceless reference. ie.\n   * evaluating it won't result in potentially arbitrary code from being ran. The following are\n   * allowed and determined not to cause side effects:\n   *\n   *  - `this` expressions\n   *  - `super` expressions\n   *  - Bound identifiers\n   */\n\n  isStatic(node: t.Node): boolean {\n    if (isThisExpression(node) || isSuper(node) || isTopicReference(node)) {\n      return true;\n    }\n\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding) {\n        return binding.constant;\n      } else {\n        return this.hasBinding(node.name);\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Possibly generate a memoised identifier if it is not static and has consequences.\n   */\n\n  maybeGenerateMemoised(node: t.Node, dontPush?: boolean) {\n    if (this.isStatic(node)) {\n      return null;\n    } else {\n      const id = this.generateUidIdentifierBasedOnNode(node);\n      if (!dontPush) {\n        this.push({ id });\n        return cloneNode(id);\n      }\n      return id;\n    }\n  }\n\n  checkBlockScopedCollisions(\n    local: Binding,\n    kind: BindingKind,\n    name: string,\n    id: any,\n  ) {\n    // ignore parameters\n    if (kind === \"param\") return;\n\n    // Ignore existing binding if it's the name of the current function or\n    // class expression\n    if (local.kind === \"local\") return;\n\n    const duplicate =\n      // don't allow duplicate bindings to exist alongside\n      kind === \"let\" ||\n      local.kind === \"let\" ||\n      local.kind === \"const\" ||\n      local.kind === \"module\" ||\n      // don't allow a local of param with a kind of let\n      (local.kind === \"param\" && kind === \"const\");\n\n    if (duplicate) {\n      throw this.path.hub.buildError(\n        id,\n        `Duplicate declaration \"${name}\"`,\n        TypeError,\n      );\n    }\n  }\n\n  rename(\n    oldName: string,\n    newName?: string,\n    // prettier-ignore\n    /* Babel 7 - block?: t.Pattern | t.Scopable */\n  ) {\n    const binding = this.getBinding(oldName);\n    if (binding) {\n      newName ||= this.generateUidIdentifier(oldName).name;\n      const renamer = new Renamer(binding, oldName, newName);\n      if (process.env.BABEL_8_BREAKING) {\n        renamer.rename();\n      } else {\n        // @ts-ignore(Babel 7 vs Babel 8) TODO: Delete this\n        renamer.rename(arguments[2]);\n      }\n    }\n  }\n\n  dump() {\n    const sep = \"-\".repeat(60);\n    console.log(sep);\n    let scope: Scope = this;\n    do {\n      console.log(\"#\", scope.block.type);\n      for (const name of Object.keys(scope.bindings)) {\n        const binding = scope.bindings[name];\n        console.log(\" -\", name, {\n          constant: binding.constant,\n          references: binding.references,\n          violations: binding.constantViolations.length,\n          kind: binding.kind,\n        });\n      }\n    } while ((scope = scope.parent));\n    console.log(sep);\n  }\n\n  hasLabel(name: string) {\n    return !!this.getLabel(name);\n  }\n\n  getLabel(name: string) {\n    return this.labels.get(name);\n  }\n\n  registerLabel(path: NodePath<t.LabeledStatement>) {\n    this.labels.set(path.node.label.name, path);\n  }\n\n  registerDeclaration(path: NodePath) {\n    if (path.isLabeledStatement()) {\n      this.registerLabel(path);\n    } else if (path.isFunctionDeclaration()) {\n      this.registerBinding(\"hoisted\", path.get(\"id\"), path);\n    } else if (path.isVariableDeclaration()) {\n      const declarations = path.get(\"declarations\");\n      const { kind } = path.node;\n      for (const declar of declarations) {\n        this.registerBinding(\n          kind === \"using\" || kind === \"await using\" ? \"const\" : kind,\n          declar,\n        );\n      }\n    } else if (path.isClassDeclaration()) {\n      if (path.node.declare) return;\n      this.registerBinding(\"let\", path);\n    } else if (path.isImportDeclaration()) {\n      const isTypeDeclaration =\n        path.node.importKind === \"type\" || path.node.importKind === \"typeof\";\n      const specifiers = path.get(\"specifiers\");\n      for (const specifier of specifiers) {\n        const isTypeSpecifier =\n          isTypeDeclaration ||\n          (specifier.isImportSpecifier() &&\n            (specifier.node.importKind === \"type\" ||\n              specifier.node.importKind === \"typeof\"));\n\n        this.registerBinding(isTypeSpecifier ? \"unknown\" : \"module\", specifier);\n      }\n    } else if (path.isExportDeclaration()) {\n      // todo: improve babel-types\n      const declar = path.get(\"declaration\") as NodePath;\n      if (\n        declar.isClassDeclaration() ||\n        declar.isFunctionDeclaration() ||\n        declar.isVariableDeclaration()\n      ) {\n        this.registerDeclaration(declar);\n      }\n    } else {\n      this.registerBinding(\"unknown\", path);\n    }\n  }\n\n  buildUndefinedNode() {\n    return buildUndefinedNode();\n  }\n\n  registerConstantViolation(path: NodePath) {\n    const ids = path.getAssignmentIdentifiers();\n    for (const name of Object.keys(ids)) {\n      this.getBinding(name)?.reassign(path);\n    }\n  }\n\n  registerBinding(\n    kind: Binding[\"kind\"],\n    path: NodePath,\n    bindingPath: NodePath = path,\n  ) {\n    if (!kind) throw new ReferenceError(\"no `kind`\");\n\n    if (path.isVariableDeclaration()) {\n      const declarators: Array<NodePath> = path.get(\"declarations\");\n      for (const declar of declarators) {\n        this.registerBinding(kind, declar);\n      }\n      return;\n    }\n\n    const parent = this.getProgramParent();\n    const ids = path.getOuterBindingIdentifiers(true);\n\n    for (const name of Object.keys(ids)) {\n      if (process.env.BABEL_8_BREAKING) {\n        parent.referencesSet.add(name);\n      } else {\n        // @ts-expect-error Babel 7\n        parent.references[name] = true;\n      }\n\n      for (const id of ids[name]) {\n        const local = this.getOwnBinding(name);\n\n        if (local) {\n          // same identifier so continue safely as we're likely trying to register it\n          // multiple times\n          if (local.identifier === id) continue;\n\n          this.checkBlockScopedCollisions(local, kind, name, id);\n        }\n\n        // A redeclaration of an existing variable is a modification\n        if (local) {\n          local.reassign(bindingPath);\n        } else {\n          this.bindings[name] = new Binding({\n            identifier: id,\n            scope: this,\n            path: bindingPath,\n            kind: kind,\n          });\n        }\n      }\n    }\n  }\n\n  addGlobal(node: t.Identifier | t.JSXIdentifier) {\n    this.globals[node.name] = node;\n  }\n\n  hasUid(name: string): boolean {\n    if (process.env.BABEL_8_BREAKING) {\n      return this.getProgramParent().uidsSet.has(name);\n    } else {\n      let scope: Scope = this;\n\n      do {\n        // @ts-expect-error Babel 7\n        if (scope.uids[name]) return true;\n      } while ((scope = scope.parent));\n\n      return false;\n    }\n  }\n\n  hasGlobal(name: string): boolean {\n    let scope: Scope = this;\n\n    do {\n      if (scope.globals[name]) return true;\n    } while ((scope = scope.parent));\n\n    return false;\n  }\n\n  hasReference(name: string): boolean {\n    if (process.env.BABEL_8_BREAKING) {\n      return this.getProgramParent().referencesSet.has(name);\n    } else {\n      // @ts-expect-error Babel 7\n      return !!this.getProgramParent().references[name];\n    }\n  }\n\n  isPure(node: t.Node, constantsOnly?: boolean): boolean {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (!binding) return false;\n      if (constantsOnly) return binding.constant;\n      return true;\n    } else if (\n      isThisExpression(node) ||\n      isMetaProperty(node) ||\n      isTopicReference(node) ||\n      isPrivateName(node)\n    ) {\n      return true;\n    } else if (isClass(node)) {\n      if (node.superClass && !this.isPure(node.superClass, constantsOnly)) {\n        return false;\n      }\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return this.isPure(node.body, constantsOnly);\n    } else if (isClassBody(node)) {\n      for (const method of node.body) {\n        if (!this.isPure(method, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isBinary(node)) {\n      return (\n        this.isPure(node.left, constantsOnly) &&\n        this.isPure(node.right, constantsOnly)\n      );\n    } else if (isArrayExpression(node) || isTupleExpression(node)) {\n      for (const elem of node.elements) {\n        if (elem !== null && !this.isPure(elem, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isObjectExpression(node) || isRecordExpression(node)) {\n      for (const prop of node.properties) {\n        if (!this.isPure(prop, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isMethod(node)) {\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      return true;\n    } else if (isProperty(node)) {\n      // @ts-expect-error todo(flow->ts): computed in not present on private properties\n      if (node.computed && !this.isPure(node.key, constantsOnly)) return false;\n      if (node.decorators?.length > 0) {\n        return false;\n      }\n      if (isObjectProperty(node) || node.static) {\n        if (node.value !== null && !this.isPure(node.value, constantsOnly)) {\n          return false;\n        }\n      }\n      return true;\n    } else if (isUnaryExpression(node)) {\n      return this.isPure(node.argument, constantsOnly);\n    } else if (isTemplateLiteral(node)) {\n      for (const expression of node.expressions) {\n        if (!this.isPure(expression, constantsOnly)) return false;\n      }\n      return true;\n    } else if (isTaggedTemplateExpression(node)) {\n      return (\n        matchesPattern(node.tag, \"String.raw\") &&\n        !this.hasBinding(\"String\", { noGlobals: true }) &&\n        this.isPure(node.quasi, constantsOnly)\n      );\n    } else if (isMemberExpression(node)) {\n      return (\n        !node.computed &&\n        isIdentifier(node.object) &&\n        node.object.name === \"Symbol\" &&\n        isIdentifier(node.property) &&\n        node.property.name !== \"for\" &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true })\n      );\n    } else if (isCallExpression(node)) {\n      return (\n        matchesPattern(node.callee, \"Symbol.for\") &&\n        !this.hasBinding(\"Symbol\", { noGlobals: true }) &&\n        node.arguments.length === 1 &&\n        t.isStringLiteral(node.arguments[0])\n      );\n    } else {\n      return isPureish(node);\n    }\n  }\n\n  /**\n   * Set some arbitrary data on the current scope.\n   */\n\n  setData(key: string | symbol, val: any) {\n    return (this.data[key] = val);\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key`.\n   */\n\n  getData(key: string | symbol): any {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) return data;\n    } while ((scope = scope.parent));\n  }\n\n  /**\n   * Recursively walk up scope tree looking for the data `key` and if it exists,\n   * remove it.\n   */\n\n  removeData(key: string) {\n    let scope: Scope = this;\n    do {\n      const data = scope.data[key];\n      if (data != null) scope.data[key] = null;\n    } while ((scope = scope.parent));\n  }\n\n  init() {\n    if (!this.inited) {\n      this.inited = true;\n      this.crawl();\n    }\n  }\n\n  crawl() {\n    const path = this.path;\n\n    resetScope(this);\n    this.data = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      if (scope.crawling) return;\n      if (scope.path.isProgram()) {\n        break;\n      }\n    } while ((scope = scope.parent));\n\n    const programParent = scope;\n\n    const state: CollectVisitorState = {\n      references: [],\n      constantViolations: [],\n      assignments: [],\n    };\n\n    this.crawling = true;\n    scopeVisitor ||= traverse.visitors.merge([\n      {\n        Scope(path) {\n          resetScope(path.scope);\n        },\n      },\n      collectorVisitor,\n    ]);\n    // traverse does not visit the root node, here we explicitly collect\n    // root node binding info when the root is not a Program.\n    if (path.type !== \"Program\") {\n      for (const visit of scopeVisitor.enter) {\n        visit.call(state, path, state);\n      }\n      const typeVisitors = scopeVisitor[path.type];\n      if (typeVisitors) {\n        for (const visit of typeVisitors.enter) {\n          visit.call(state, path, state);\n        }\n      }\n    }\n    path.traverse(scopeVisitor, state);\n    this.crawling = false;\n\n    // register assignments\n    for (const path of state.assignments) {\n      // register undeclared bindings as globals\n      const ids = path.getAssignmentIdentifiers();\n      for (const name of Object.keys(ids)) {\n        if (path.scope.getBinding(name)) continue;\n        programParent.addGlobal(ids[name]);\n      }\n\n      // register as constant violation\n      path.scope.registerConstantViolation(path);\n    }\n\n    // register references\n    for (const ref of state.references) {\n      const binding = ref.scope.getBinding(ref.node.name);\n      if (binding) {\n        binding.reference(ref);\n      } else {\n        programParent.addGlobal(ref.node);\n      }\n    }\n\n    // register constant violations\n    for (const path of state.constantViolations) {\n      path.scope.registerConstantViolation(path);\n    }\n  }\n\n  push(opts: {\n    id: t.ArrayPattern | t.Identifier | t.ObjectPattern;\n    init?: t.Expression;\n    unique?: boolean;\n    _blockHoist?: number | undefined;\n    kind?: \"var\" | \"let\" | \"const\";\n  }) {\n    let path = this.path;\n\n    if (path.isPattern()) {\n      path = this.getPatternParent().path;\n    } else if (!path.isBlockStatement() && !path.isProgram()) {\n      path = this.getBlockParent().path;\n    }\n\n    if (path.isSwitchStatement()) {\n      path = (this.getFunctionParent() || this.getProgramParent()).path;\n    }\n\n    const { init, unique, kind = \"var\", id } = opts;\n\n    // When injecting a non-const non-initialized binding inside\n    // an IIFE, if the number of call arguments is less than or\n    // equal to the number of function parameters, we can safely\n    // inject the binding into the parameter list.\n    if (\n      !init &&\n      !unique &&\n      (kind === \"var\" || kind === \"let\") &&\n      path.isFunction() &&\n      // @ts-expect-error ArrowFunctionExpression never has a name\n      !path.node.name &&\n      isCallExpression(path.parent, { callee: path.node }) &&\n      path.parent.arguments.length <= path.node.params.length &&\n      isIdentifier(id)\n    ) {\n      path.pushContainer(\"params\", id);\n      path.scope.registerBinding(\n        \"param\",\n        path.get(\"params\")[path.node.params.length - 1],\n      );\n      return;\n    }\n\n    if (path.isLoop() || path.isCatchClause() || path.isFunction()) {\n      path.ensureBlock();\n      path = path.get(\"body\");\n    }\n\n    const blockHoist = opts._blockHoist == null ? 2 : opts._blockHoist;\n\n    const dataKey = `declaration:${kind}:${blockHoist}`;\n    let declarPath = !unique && path.getData(dataKey);\n\n    if (!declarPath) {\n      const declar = variableDeclaration(kind, []);\n      // @ts-expect-error todo(flow->ts): avoid modifying nodes\n      declar._blockHoist = blockHoist;\n\n      [declarPath] = (path as NodePath<t.BlockStatement>).unshiftContainer(\n        \"body\",\n        [declar],\n      );\n      if (!unique) path.setData(dataKey, declarPath);\n    }\n\n    const declarator = variableDeclarator(id, init);\n    const len = declarPath.node.declarations.push(declarator);\n    path.scope.registerBinding(kind, declarPath.get(\"declarations\")[len - 1]);\n  }\n\n  /**\n   * Walk up to the top of the scope tree and get the `Program`.\n   */\n\n  getProgramParent(): Scope & {\n    referencesSet: Set<string>;\n    uidsSet: Set<string>;\n  } {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isProgram()) {\n        return scope as Scope & {\n          referencesSet: Set<string>;\n          uidsSet: Set<string>;\n        };\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\"Couldn't find a Program\");\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a Function or return null.\n   */\n\n  getFunctionParent(): Scope | null {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isFunctionParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    return null;\n  }\n\n  /**\n   * Walk up the scope tree until we hit either a BlockStatement/Loop/Program/Function/Switch or reach the\n   * very top and hit Program.\n   */\n\n  getBlockParent() {\n    let scope: Scope = this;\n    do {\n      if (scope.path.isBlockParent()) {\n        return scope;\n      }\n    } while ((scope = scope.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walk up from a pattern scope (function param initializer) until we hit a non-pattern scope,\n   * then returns its block parent\n   * @returns An ancestry scope whose path is a block parent\n   */\n  getPatternParent() {\n    let scope: Scope = this;\n    do {\n      if (!scope.path.isPattern()) {\n        return scope.getBlockParent();\n      }\n    } while ((scope = scope.parent.parent));\n    throw new Error(\n      \"We couldn't find a BlockStatement, For, Switch, Function, Loop or Program...\",\n    );\n  }\n\n  /**\n   * Walks the scope tree and gathers **all** bindings.\n   */\n\n  getAllBindings(): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    let scope: Scope = this;\n    do {\n      for (const key of Object.keys(scope.bindings)) {\n        if (key in ids === false) {\n          ids[key] = scope.bindings[key];\n        }\n      }\n      scope = scope.parent;\n    } while (scope);\n\n    return ids;\n  }\n\n  bindingIdentifierEquals(name: string, node: t.Node): boolean {\n    return this.getBindingIdentifier(name) === node;\n  }\n\n  getBinding(name: string): Binding | undefined {\n    let scope: Scope = this;\n    let previousPath;\n\n    do {\n      const binding = scope.getOwnBinding(name);\n      if (binding) {\n        // Check if a pattern is a part of parameter expressions.\n        // Note: for performance reason we skip checking previousPath.parentPath.isFunction()\n        // because `scope.path` is validated as scope in packages/babel-types/src/validators/isScope.js\n        // That is, if a scope path is pattern, its parent must be Function/CatchClause\n\n        // Spec *********: The closure created by this expression should not have visibility of\n        // declarations in the function body. If the binding is not a `param`-kind (as function parameters)\n        // or `local`-kind (as id in function expression),\n        // then it must be defined inside the function body, thus it should be skipped\n        if (\n          previousPath?.isPattern() &&\n          binding.kind !== \"param\" &&\n          binding.kind !== \"local\"\n        ) {\n          // do nothing\n        } else {\n          return binding;\n        }\n      } else if (\n        !binding &&\n        name === \"arguments\" &&\n        scope.path.isFunction() &&\n        !scope.path.isArrowFunctionExpression()\n      ) {\n        break;\n      }\n      previousPath = scope.path;\n    } while ((scope = scope.parent));\n  }\n\n  getOwnBinding(name: string): Binding | undefined {\n    return this.bindings[name];\n  }\n\n  // todo: return probably can be undefined…\n  getBindingIdentifier(name: string): t.Identifier {\n    return this.getBinding(name)?.identifier;\n  }\n\n  // todo: flow->ts return probably can be undefined\n  getOwnBindingIdentifier(name: string): t.Identifier {\n    const binding = this.bindings[name];\n    return binding?.identifier;\n  }\n\n  hasOwnBinding(name: string) {\n    return !!this.getOwnBinding(name);\n  }\n\n  // By default, we consider generated UIDs as bindings.\n  // This is because they are almost always used to declare variables,\n  // and since the scope isn't always up-to-date it's better to assume that\n  // there is a variable with that name. The `noUids` option can be used to\n  // turn off this behavior, for example if you know that the generate UID\n  // was used to declare a variable in a different scope.\n  hasBinding(\n    name: string,\n    opts?:\n      | boolean\n      | { noGlobals?: boolean; noUids?: boolean; upToScope?: Scope },\n  ) {\n    if (!name) return false;\n    // TODO: Only accept the object form.\n    let noGlobals;\n    let noUids;\n    let upToScope;\n    if (typeof opts === \"object\") {\n      noGlobals = opts.noGlobals;\n      noUids = opts.noUids;\n      upToScope = opts.upToScope;\n    } else if (typeof opts === \"boolean\") {\n      noGlobals = opts;\n    }\n    let scope: Scope = this;\n    do {\n      if (upToScope === scope) {\n        break;\n      }\n      if (scope.hasOwnBinding(name)) {\n        return true;\n      }\n    } while ((scope = scope.parent));\n\n    if (!noUids && this.hasUid(name)) return true;\n    if (!noGlobals && Scope.globals.includes(name)) return true;\n    if (!noGlobals && Scope.contextVariables.includes(name)) return true;\n    return false;\n  }\n\n  parentHasBinding(\n    name: string,\n    opts?: { noGlobals?: boolean; noUids?: boolean },\n  ) {\n    return this.parent?.hasBinding(name, opts);\n  }\n\n  /**\n   * Move a binding of `name` to another `scope`.\n   */\n\n  moveBindingTo(name: string, scope: Scope) {\n    const info = this.getBinding(name);\n    if (info) {\n      info.scope.removeOwnBinding(name);\n      info.scope = scope;\n      scope.bindings[name] = info;\n    }\n  }\n\n  removeOwnBinding(name: string) {\n    delete this.bindings[name];\n  }\n\n  removeBinding(name: string) {\n    // clear literal binding\n    this.getBinding(name)?.scope.removeOwnBinding(name);\n\n    // clear uids with this name - https://github.com/babel/babel/issues/2101\n    if (process.env.BABEL_8_BREAKING) {\n      this.getProgramParent().uidsSet.delete(name);\n    } else {\n      let scope: Scope = this;\n      do {\n        // @ts-expect-error Babel 7\n        if (scope.uids[name]) {\n          // @ts-expect-error Babel 7\n          scope.uids[name] = false;\n        }\n      } while ((scope = scope.parent));\n    }\n  }\n\n  /**\n   * Hoist all the `var` variable to the beginning of the function/program\n   * scope where their binding will be actually defined. For exmaple,\n   *     { var x = 2 }\n   * will be transformed to\n   *     var x; { x = 2 }\n   *\n   * @param emit A custom function to emit `var` declarations, for example to\n   *   emit them in a different scope.\n   */\n  hoistVariables(\n    emit: (id: t.Identifier, hasInit: boolean) => void = id =>\n      this.push({ id }),\n  ) {\n    this.crawl();\n\n    const seen = new Set();\n    for (const name of Object.keys(this.bindings)) {\n      const binding = this.bindings[name];\n      if (!binding) continue;\n      const { path } = binding;\n      if (!path.isVariableDeclarator()) continue;\n      const { parent, parentPath } = path;\n\n      if (parent.kind !== \"var\" || seen.has(parent)) continue;\n      seen.add(path.parent);\n\n      let firstId;\n      const init = [];\n      for (const decl of parent.declarations) {\n        firstId ??= decl.id;\n        if (decl.init) {\n          init.push(\n            assignmentExpression(\n              \"=\",\n              // var declarator must not be a void pattern\n              decl.id as Exclude<t.VariableDeclarator[\"id\"], t.VoidPattern>,\n              decl.init,\n            ),\n          );\n        }\n\n        const ids = Object.keys(getBindingIdentifiers(decl, false, true, true));\n        for (const name of ids) {\n          emit(identifier(name), decl.init != null);\n        }\n      }\n\n      // for (var i in test)\n      if (parentPath.parentPath.isFor({ left: parent })) {\n        parentPath.replaceWith(firstId);\n      } else if (init.length === 0) {\n        parentPath.remove();\n      } else {\n        const expr = init.length === 1 ? init[0] : sequenceExpression(init);\n        if (parentPath.parentPath.isForStatement({ init: parent })) {\n          parentPath.replaceWith(expr);\n        } else {\n          parentPath.replaceWith(expressionStatement(expr));\n        }\n      }\n    }\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /** @deprecated Not used in our codebase */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._renameFromMap = function _renameFromMap(\n    map: Record<string | symbol, unknown>,\n    oldName: string | symbol,\n    newName: string | symbol,\n    value: unknown,\n  ) {\n    if (map[oldName]) {\n      map[newName] = value;\n      map[oldName] = null;\n    }\n  };\n\n  /**\n   * Traverse node with current scope and path.\n   *\n   * !!! WARNING !!!\n   * This method assumes that `this.path` is the NodePath representing `node`.\n   * After running the traversal, the `.parentPath` of the NodePaths\n   * corresponding to `node`'s children will be set to `this.path`.\n   *\n   * There is no good reason to use this method, since the only safe way to use\n   * it is equivalent to `scope.path.traverse(opts, state)`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.traverse = function <S>(\n    this: Scope,\n    node: any,\n    opts: any,\n    state?: S,\n  ) {\n    traverse(node, opts, this, state, this.path);\n  };\n\n  /**\n   * Generate an `_id1`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype._generateUid = function _generateUid(\n    name: string,\n    i: number,\n  ) {\n    let id = name;\n    if (i > 1) id += i;\n    return `_${id}`;\n  };\n\n  // TODO: (Babel 8) Split i in two parameters, and use an object of flags\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.toArray = function toArray(\n    this: Scope,\n    node: t.Node,\n    i?: number | boolean,\n    arrayLikeIsIterable?: boolean | void,\n  ) {\n    if (isIdentifier(node)) {\n      const binding = this.getBinding(node.name);\n      if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n        return node;\n      }\n    }\n\n    if (isArrayExpression(node)) {\n      return node;\n    }\n\n    if (isIdentifier(node, { name: \"arguments\" })) {\n      return callExpression(\n        memberExpression(\n          memberExpression(\n            memberExpression(identifier(\"Array\"), identifier(\"prototype\")),\n            identifier(\"slice\"),\n          ),\n          identifier(\"call\"),\n        ),\n        [node],\n      );\n    }\n\n    let helperName;\n    const args = [node];\n    if (i === true) {\n      // Used in array-spread to create an array.\n      helperName = \"toConsumableArray\";\n    } else if (typeof i === \"number\") {\n      args.push(numericLiteral(i));\n\n      // Used in array-rest to create an array from a subset of an iterable.\n      helperName = \"slicedToArray\";\n      // TODO if (this.hub.isLoose(\"es6.forOf\")) helperName += \"-loose\";\n    } else {\n      // Used in array-rest to create an array\n      helperName = \"toArray\";\n    }\n\n    if (arrayLikeIsIterable) {\n      args.unshift(this.path.hub.addHelper(helperName));\n      helperName = \"maybeArrayLike\";\n    }\n\n    // @ts-expect-error todo(flow->ts): t.Node is not valid to use in args, function argument typeneeds to be clarified\n    return callExpression(this.path.hub.addHelper(helperName), args);\n  };\n\n  /**\n   * Walks the scope tree and gathers all declarations of `kind`.\n   */\n  // @ts-expect-error Babel 7 compatibility\n  Scope.prototype.getAllBindingsOfKind = function getAllBindingsOfKind(\n    ...kinds: string[]\n  ): Record<string, Binding> {\n    const ids = Object.create(null);\n\n    for (const kind of kinds) {\n      let scope: Scope = this;\n      do {\n        for (const name of Object.keys(scope.bindings)) {\n          const binding = scope.bindings[name];\n          if (binding.kind === kind) ids[name] = binding;\n        }\n        scope = scope.parent;\n      } while (scope);\n    }\n\n    return ids;\n  };\n\n  Object.defineProperties(Scope.prototype, {\n    parentBlock: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.parent;\n      },\n    },\n    hub: {\n      configurable: true,\n      enumerable: true,\n      get(this: Scope) {\n        return this.path.hub;\n      },\n    },\n  });\n}\n\ntype _Binding = Binding;\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace Scope {\n  export type Binding = _Binding;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAIA,IAAAG,EAAA,GAAAH,OAAA;AAiDsB,IAAAI,CAAA,GAAAD,EAAA;AAEtB,IAAAE,MAAA,GAAAL,OAAA;AAAkD,MArD3CM,mBAAmB,GAAAN,OAAA,CAAM,+CAA+C;EACxEO,mBAAmB,GAAAP,OAAA,CAAM,+CAA+C;AAAA;EAE7EQ,oBAAoB;EACpBC,cAAc;EACdC,SAAS;EACTC,qBAAqB;EACrBC,UAAU;EACVC,iBAAiB;EACjBC,QAAQ;EACRC,gBAAgB;EAChBC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,sBAAsB;EACtBC,0BAA0B;EAC1BC,wBAAwB;EACxBC,qBAAqB;EACrBC,YAAY;EACZC,mBAAmB;EACnBC,SAAS;EACTC,kBAAkB;EAClBC,QAAQ;EACRC,iBAAiB;EACjBC,aAAa;EACbC,kBAAkB;EAClBC,UAAU;EACVC,SAAS;EACTC,eAAe;EACfC,OAAO;EACPC,0BAA0B;EAC1BC,iBAAiB;EACjBC,gBAAgB;EAChBC,iBAAiB;EACjBC,qBAAqB;EACrBC,mBAAmB;EACnBC,cAAc;EACdC,gBAAgB;EAChBC,cAAc;EACdC,YAAY;EACZC,mBAAmB;EACnBC,kBAAkB;EAClBC,kBAAkB;EAClBC,iBAAiB;EACjBC,gBAAgB;EAChBC,gBAAgB;EAChBC,cAAc;EACdC,aAAa;EACbC,mBAAmB;EACnBC,kBAAkB;EAClBC;AAAkB,IAAApD,EAAA;AAQpB,SAASqD,eAAeA,CAACC,IAAY,EAAEC,KAAiB,EAAE;EACxD,QAAQD,IAAI,oBAAJA,IAAI,CAAEE,IAAI;IAChB;MACE,IAAInC,mBAAmB,CAACiC,IAAI,CAAC,IAAIJ,mBAAmB,CAACI,IAAI,CAAC,EAAE;QAAA,IAAAG,gBAAA;QAC1D,IACE,CAACzC,sBAAsB,CAACsC,IAAI,CAAC,IAC3BpC,wBAAwB,CAACoC,IAAI,CAAC,IAC9BjC,mBAAmB,CAACiC,IAAI,CAAC,KAC3BA,IAAI,CAACI,MAAM,EACX;UACAL,eAAe,CAACC,IAAI,CAACI,MAAM,EAAEH,KAAK,CAAC;QACrC,CAAC,MAAM,IACL,CAACrC,wBAAwB,CAACoC,IAAI,CAAC,IAAIjC,mBAAmB,CAACiC,IAAI,CAAC,MAAAG,gBAAA,GAC5DH,IAAI,CAACK,UAAU,aAAfF,gBAAA,CAAiBG,MAAM,EACvB;UACA,KAAK,MAAMC,CAAC,IAAIP,IAAI,CAACK,UAAU,EAAEN,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;QAC5D,CAAC,MAAM,IACL,CAACtC,0BAA0B,CAACqC,IAAI,CAAC,IAC/BpC,wBAAwB,CAACoC,IAAI,CAAC,KAChCA,IAAI,CAACQ,WAAW,EAChB;UACAT,eAAe,CAACC,IAAI,CAACQ,WAAW,EAAEP,KAAK,CAAC;QAC1C;MACF,CAAC,MAAM,IAAI9B,iBAAiB,CAAC6B,IAAI,CAAC,EAAE;QAUlCD,eAAe,CAACC,IAAI,CAACS,KAAK,EAAER,KAAK,CAAC;MACpC,CAAC,MAAM,IACLjC,SAAS,CAACgC,IAAI,CAAC,IACf,CAAC5B,aAAa,CAAC4B,IAAI,CAAC,IACpB,CAACxB,eAAe,CAACwB,IAAI,CAAC,IACtB,CAACrB,iBAAiB,CAACqB,IAAI,CAAC,EACxB;QACAC,KAAK,CAACS,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC;MACxB;MACA;IAEF,KAAK,kBAAkB;IACvB,KAAK,0BAA0B;IAC/B,KAAK,qBAAqB;MACxBZ,eAAe,CAACC,IAAI,CAACY,MAAM,EAAEX,KAAK,CAAC;MACnCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;IACjB,KAAK,eAAe;MAClBA,KAAK,CAACS,IAAI,CAACV,IAAI,CAACc,IAAI,CAAC;MACrB;IAEF,KAAK,gBAAgB;IACrB,KAAK,wBAAwB;IAC7B,KAAK,eAAe;MAClBf,eAAe,CAACC,IAAI,CAACe,MAAM,EAAEd,KAAK,CAAC;MACnC;IAEF,KAAK,kBAAkB;IACvB,KAAK,eAAe;MAClB,KAAK,MAAMM,CAAC,IAAIP,IAAI,CAACgB,UAAU,EAAE;QAC/BjB,eAAe,CAACQ,CAAC,EAAEN,KAAK,CAAC;MAC3B;MACA;IAEF,KAAK,eAAe;IACpB,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,gBAAgB;IACrB,KAAK,cAAc;IACnB,KAAK,eAAe;IACpB,KAAK,aAAa;IAClB,KAAK,sBAAsB;IAC3B,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACkB,GAAG,EAAEjB,KAAK,CAAC;MAChC;IAEF,KAAK,gBAAgB;MACnBA,KAAK,CAACS,IAAI,CAAC,MAAM,CAAC;MAClB;IAEF,KAAK,OAAO;MACVT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnB;IAEF,KAAK,QAAQ;IACb,KAAK,kBAAkB;MACrBT,KAAK,CAACS,IAAI,CAAC,QAAQ,CAAC;MACpB;IAEF,KAAK,cAAc;MACjBT,KAAK,CAACS,IAAI,CAAC,IAAI,CAAC;MAChB;IAEF,KAAK,iBAAiB;MACpBT,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,iBAAiB;MACpBA,KAAK,CAACS,IAAI,CAAC,OAAO,CAAC;MACnBX,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,sBAAsB;MACzBF,eAAe,CAACC,IAAI,CAACmB,IAAI,EAAElB,KAAK,CAAC;MACjC;IAEF,KAAK,oBAAoB;MACvBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,oBAAoB;IACzB,KAAK,qBAAqB;IAC1B,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACoB,EAAE,EAAEnB,KAAK,CAAC;MAC/B;IAEF,KAAK,yBAAyB;MAC5BF,eAAe,CAACC,IAAI,CAACqB,UAAU,EAAEpB,KAAK,CAAC;MACvC;IAEF,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrBF,eAAe,CAACC,IAAI,CAACiB,QAAQ,EAAEhB,KAAK,CAAC;MACrC;IAEF,KAAK,cAAc;MACjBF,eAAe,CAACC,IAAI,CAACsB,IAAI,EAAErB,KAAK,CAAC;MACjCF,eAAe,CAACC,IAAI,CAACa,QAAQ,EAAEZ,KAAK,CAAC;MACrC;IAEF,KAAK,YAAY;MACfF,eAAe,CAACC,IAAI,CAACuB,cAAc,EAAEtB,KAAK,CAAC;MAC3C;IAEF,KAAK,mBAAmB;MACtBF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;IAEF,KAAK,aAAa;MAChBF,eAAe,CAACC,IAAI,CAACwB,eAAe,EAAEvB,KAAK,CAAC;MAC5C;IAEF,KAAK,oBAAoB;MACvBA,KAAK,CAACS,IAAI,CAAC,UAAU,CAAC;MACtB;IAEF,KAAK,mBAAmB;MACtBX,eAAe,CAACC,IAAI,CAACyB,SAAS,EAAExB,KAAK,CAAC;MACtCF,eAAe,CAACC,IAAI,CAACc,IAAI,EAAEb,KAAK,CAAC;MACjC;EACJ;AACF;AAEA,SAASyB,UAAUA,CAACC,KAAY,EAAE;EACG;IAEjCA,KAAK,CAACC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEtCH,KAAK,CAACI,IAAI,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAClC;EAKAH,KAAK,CAACK,QAAQ,GAAGH,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACpCH,KAAK,CAACM,OAAO,GAAGJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACrC;AAQmC;EAEjC,IAAII,iBAAiB,GAAGC,MAAM,CAACC,GAAG,CAChC,0CACF,CAAC;AACH;AAEA,MAAMC,gBAA8C,GAAG;EACrDC,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAE/B,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE;MAClB,MAAM;QAAEf;MAAM,CAAC,GAAGY,IAAI;MACtB,MAAMI,WAAW,GAAGhB,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIjB,KAAK,CAACkB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAEN,MAAM,CAAC;IAC5C;EACF,CAAC;EAEDO,WAAWA,CAACR,IAAI,EAAE;IAEhB,IAAIA,IAAI,CAACS,aAAa,CAAC,CAAC,EAAE;IAG1B,IAAIT,IAAI,CAACxE,mBAAmB,CAAC,CAAC,EAAE;IAGhC,IAAIwE,IAAI,CAAC3C,mBAAmB,CAAC,CAAC,EAAE;IAGhC,MAAMqD,MAAM,GACVV,IAAI,CAACZ,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIL,IAAI,CAACZ,KAAK,CAACkB,gBAAgB,CAAC,CAAC;IACjEI,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDY,iBAAiBA,CAACZ,IAAI,EAAE;IAEtB,MAAMU,MAAM,GAAGV,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDc,yBAAyBA,CAACd,IAAI,EAAE;IAC9B,MAAMU,MAAM,GAAGV,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC;IAE1CH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;EAClC,CAAC;EAEDe,oBAAoBA,CAACf,IAAI,EAAEgB,KAAK,EAAE;IAChC,IAAI5G,CAAC,CAAC6G,iBAAiB,CAACjB,IAAI,CAACU,MAAM,CAAC,IAAIV,IAAI,CAACU,MAAM,CAACQ,KAAK,KAAKlB,IAAI,CAACvC,IAAI,EAAE;MACvE;IACF;IACA,IAAIuC,IAAI,CAACmB,UAAU,CAACC,2BAA2B,CAAC,CAAC,EAAE;IACnDJ,KAAK,CAAC3B,UAAU,CAAClB,IAAI,CAAC6B,IAAI,CAAC;EAC7B,CAAC;EAEDqB,aAAaA,CAACrB,IAAI,EAAEgB,KAAK,EAAE;IACzB,MAAMpC,IAAI,GAAGoB,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAItB,IAAI,CAAC0C,SAAS,CAAC,CAAC,IAAI1C,IAAI,CAACrD,YAAY,CAAC,CAAC,EAAE;MAC3CyF,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;IACrC,CAAC,MAEI,IAAIpB,IAAI,CAACuB,KAAK,CAAC,CAAC,EAAE;MACrB,MAAM;QAAEf;MAAM,CAAC,GAAGY,IAAI;MACtB,MAAMI,WAAW,GAAGhB,KAAK,CAACiB,iBAAiB,CAAC,CAAC,IAAIjB,KAAK,CAACkB,gBAAgB,CAAC,CAAC;MACzEF,WAAW,CAACG,eAAe,CAAC,KAAK,EAAE3B,IAAI,CAAC;IAC1C;EACF,CAAC;EAED4C,iBAAiB,EAAE;IACjBC,IAAIA,CAACzB,IAAI,EAAE;MACT,MAAM;QAAEvC,IAAI;QAAE2B;MAAM,CAAC,GAAGY,IAAI;MAE5B,IAAI7E,sBAAsB,CAACsC,IAAI,CAAC,EAAE;MAClC,MAAMwC,MAAM,GAAGxC,IAAI,CAACQ,WAAW;MAC/B,IAAI/C,kBAAkB,CAAC+E,MAAM,CAAC,IAAI3E,qBAAqB,CAAC2E,MAAM,CAAC,EAAE;QAC/D,MAAMpB,EAAE,GAAGoB,MAAM,CAACpB,EAAE;QACpB,IAAI,CAACA,EAAE,EAAE;QAET,MAAM6C,OAAO,GAAGtC,KAAK,CAACuC,UAAU,CAAC9C,EAAE,CAACN,IAAI,CAAC;QACzCmD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAAC5B,IAAI,CAAC;MAC1B,CAAC,MAAM,IAAIzD,qBAAqB,CAAC0D,MAAM,CAAC,EAAE;QACxC,KAAK,MAAM4B,IAAI,IAAI5B,MAAM,CAAC6B,YAAY,EAAE;UACtC,KAAK,MAAMvD,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACpH,qBAAqB,CAACkH,IAAI,CAAC,CAAC,EAAE;YAC3D,MAAMH,OAAO,GAAGtC,KAAK,CAACuC,UAAU,CAACpD,IAAI,CAAC;YACtCmD,OAAO,YAAPA,OAAO,CAAEE,SAAS,CAAC5B,IAAI,CAAC;UAC1B;QACF;MACF;IACF;EACF,CAAC;EAEDgC,gBAAgBA,CAAChC,IAAI,EAAE;IACrBA,IAAI,CAACZ,KAAK,CAACyB,cAAc,CAAC,CAAC,CAACF,mBAAmB,CAACX,IAAI,CAAC;EACvD,CAAC;EAEDiC,oBAAoBA,CAACjC,IAAI,EAAEgB,KAAK,EAAE;IAChCA,KAAK,CAACkB,WAAW,CAAC/D,IAAI,CAAC6B,IAAI,CAAC;EAC9B,CAAC;EAEDmC,gBAAgBA,CAACnC,IAAI,EAAEgB,KAAK,EAAE;IAC5BA,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;EACrC,CAAC;EAEDoC,eAAeA,CAACpC,IAAI,EAAEgB,KAAK,EAAE;IAC3B,IAAIhB,IAAI,CAACvC,IAAI,CAAC4E,QAAQ,KAAK,QAAQ,EAAE;MACnCrB,KAAK,CAACO,kBAAkB,CAACpD,IAAI,CAAC6B,IAAI,CAAC;IACrC;EACF,CAAC;EAEDsC,WAAWA,CAACtC,IAAI,EAAE;IAChB,IAAIZ,KAAK,GAAGY,IAAI,CAACZ,KAAK;IACtB,IAAIA,KAAK,CAACY,IAAI,KAAKA,IAAI,EAAEZ,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE7C,MAAMA,MAAM,GAAGtB,KAAK,CAACyB,cAAc,CAAC,CAAC;IACrCH,MAAM,CAACC,mBAAmB,CAACX,IAAI,CAAC;IAGhC,IAAIA,IAAI,CAAC9E,kBAAkB,CAAC,CAAC,IAAI8E,IAAI,CAACvC,IAAI,CAACoB,EAAE,EAAE;MAC7C,MAAMA,EAAE,GAAGmB,IAAI,CAACvC,IAAI,CAACoB,EAAE;MACvB,MAAMN,IAAI,GAAGM,EAAE,CAACN,IAAI;MAEpByB,IAAI,CAACZ,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC,GAAGyB,IAAI,CAACZ,KAAK,CAACsB,MAAM,CAACiB,UAAU,CAACpD,IAAI,CAAC;IAChE;EACF,CAAC;EAEDgE,WAAWA,CAACvC,IAAI,EAAE;IAChBA,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,KAAK,EAAEP,IAAI,CAAC;EACzC,CAAC;EAEDwC,QAAQA,CAACxC,IAAI,EAAE;IACb,MAAMyC,MAAuB,GAAGzC,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC;IAClD,KAAK,MAAMwC,KAAK,IAAID,MAAM,EAAE;MAC1BzC,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEmC,KAAK,CAAC;IAC5C;IAKA,IACE1C,IAAI,CAAC2C,oBAAoB,CAAC,CAAC,IAC3B3C,IAAI,CAACvC,IAAI,CAACoB,EAAE,IAGV,CAACmB,IAAI,CAACvC,IAAI,CAACoB,EAAE,CAACc,iBAAiB,CAAC,EAClC;MACAK,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED4C,eAAeA,CAAC5C,IAAI,EAAE;IACpB,IACEA,IAAI,CAACvC,IAAI,CAACoB,EAAE,IAGV,CAACmB,IAAI,CAACvC,IAAI,CAACoB,EAAE,CAACc,iBAAiB,CAAC,EAClC;MACAK,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAAC,OAAO,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IAC3D;EACF,CAAC;EAED6C,gBAAgBA,CAAC7C,IAAI,EAAE;IACrBA,IAAI,CAAC8C,IAAI,CAAC,CAAC;EACb;AACF,CAAC;AAED,IAAIC,YAAkD;AAEtD,IAAIC,GAAG,GAAG,CAAC;AAKX,MAAMC,KAAK,CAAC;EAsBVC,WAAWA,CAAClD,IAAsC,EAAE;IAAA,KArBpDgD,GAAG;IAAA,KAEHhD,IAAI;IAAA,KACJmD,KAAK;IAAA,KAELC,MAAM;IAAA,KAENC,MAAM;IAAA,KACN5D,QAAQ;IAAA,KAER6D,aAAa;IAAA,KACb5D,OAAO;IAAA,KAEP6D,OAAO;IAAA,KACPC,IAAI;IAAA,KACJC,QAAQ;IAON,MAAM;MAAEhG;IAAK,CAAC,GAAGuC,IAAI;IACrB,MAAM0D,MAAM,GAAGC,YAAU,CAACzD,GAAG,CAACzC,IAAI,CAAC;IAGnC,IAAI,CAAAiG,MAAM,oBAANA,MAAM,CAAE1D,IAAI,MAAKA,IAAI,EAAE;MACzB,OAAO0D,MAAM;IACf;IACAC,YAAU,CAACC,GAAG,CAACnG,IAAI,EAAE,IAAI,CAAC;IAE1B,IAAI,CAACuF,GAAG,GAAGA,GAAG,EAAE;IAEhB,IAAI,CAACG,KAAK,GAAG1F,IAAI;IACjB,IAAI,CAACuC,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACqD,MAAM,GAAG,IAAIQ,GAAG,CAAC,CAAC;IACvB,IAAI,CAACT,MAAM,GAAG,KAAK;IAEgB;MAEjC9D,MAAM,CAACwE,gBAAgB,CAAC,IAAI,EAAE;QAC5BzE,UAAU,EAAE;UACV0E,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,IAAI;UACd7F,KAAK,EAAEkB,MAAM,CAACC,MAAM,CAAC,IAAI;QAC3B,CAAC;QACDC,IAAI,EAAE;UACJuE,UAAU,EAAE,IAAI;UAChBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE,IAAI;UACd7F,KAAK,EAAEkB,MAAM,CAACC,MAAM,CAAC,IAAI;QAC3B;MACF,CAAC,CAAC;IACJ;EACF;EAcA,IAAImB,MAAMA,CAAA,EAAG;IAAA,IAAAwD,OAAA;IACX,IAAIxD,MAAM;MACRV,IAAI,GAAG,IAAI,CAACA,IAAI;IAClB,GAAG;MAAA,IAAAmE,KAAA;MAED,MAAMC,UAAU,GAAGpE,IAAI,CAACrB,GAAG,KAAK,KAAK,IAAIqB,IAAI,CAACqE,OAAO,KAAK,YAAY;MACtErE,IAAI,GAAGA,IAAI,CAACmB,UAAU;MACtB,IAAIiD,UAAU,IAAIpE,IAAI,CAACrE,QAAQ,CAAC,CAAC,EAAEqE,IAAI,GAAGA,IAAI,CAACmB,UAAU;MACzD,KAAAgD,KAAA,GAAInE,IAAI,aAAJmE,KAAA,CAAMG,OAAO,CAAC,CAAC,EAAE5D,MAAM,GAAGV,IAAI;IACpC,CAAC,QAAQA,IAAI,IAAI,CAACU,MAAM;IAExB,QAAAwD,OAAA,GAAOxD,MAAM,qBAANwD,OAAA,CAAQ9E,KAAK;EACtB;EAEA,IAAIC,UAAUA,CAAA,EAAG;IACf,MAAM,IAAIkF,KAAK,CACb,gFACF,CAAC;EACH;EAEA,IAAI/E,IAAIA,CAAA,EAAG;IACT,MAAM,IAAI+E,KAAK,CACb,oEACF,CAAC;EACH;EAMAC,6BAA6BA,CAACjG,IAAa,EAAE;IAC3C,MAAMM,EAAE,GAAG,IAAI,CAAC4F,qBAAqB,CAAClG,IAAI,CAAC;IAC3C,IAAI,CAACJ,IAAI,CAAC;MAAEU;IAAG,CAAC,CAAC;IACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;EACtB;EAMA4F,qBAAqBA,CAAClG,IAAa,EAAE;IACnC,OAAO3D,UAAU,CAAC,IAAI,CAAC8J,WAAW,CAACnG,IAAI,CAAC,CAAC;EAC3C;EAMAmG,WAAWA,CAACnG,IAAY,GAAG,MAAM,EAAU;IACzCA,IAAI,GAAG3B,YAAY,CAAC2B,IAAI,CAAC,CAACoG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAEjE,IAAI3B,GAAG;IACP,IAAI4B,CAAC,GAAG,CAAC;IACT,GAAG;MACD5B,GAAG,GAAG,IAAIzE,IAAI,EAAE;MAOhB,IAAIqG,CAAC,IAAI,EAAE,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC,CAAC,KACrB,IAAIA,CAAC,IAAI,CAAC,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC,CAAC,KACzB,IAAIA,CAAC,IAAI,CAAC,EAAE5B,GAAG,IAAI4B,CAAC,GAAG,CAAC;MAC7BA,CAAC,EAAE;IACL,CAAC,QACC,IAAI,CAACC,QAAQ,CAAC7B,GAAG,CAAC,IAClB,IAAI,CAAC8B,UAAU,CAAC9B,GAAG,CAAC,IACpB,IAAI,CAAC+B,SAAS,CAAC/B,GAAG,CAAC,IACnB,IAAI,CAACgC,YAAY,CAAChC,GAAG,CAAC;IAGxB,MAAMiC,OAAO,GAAG,IAAI,CAAC3E,gBAAgB,CAAC,CAAC;IAIhC;MAEL2E,OAAO,CAAC5F,UAAU,CAAC2D,GAAG,CAAC,GAAG,IAAI;MAE9BiC,OAAO,CAACzF,IAAI,CAACwD,GAAG,CAAC,GAAG,IAAI;IAC1B;IAEA,OAAOA,GAAG;EACZ;EAEAkC,sBAAsBA,CAACzH,IAAY,EAAE0H,WAAoB,EAAE;IACzD,MAAMzH,KAAiB,GAAG,EAAE;IAC5BF,eAAe,CAACC,IAAI,EAAEC,KAAK,CAAC;IAE5B,IAAImB,EAAE,GAAGnB,KAAK,CAAC0H,IAAI,CAAC,GAAG,CAAC;IACxBvG,EAAE,GAAGA,EAAE,CAAC8F,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAIQ,WAAW,IAAI,KAAK;IAEjD,OAAO,IAAI,CAACT,WAAW,CAAC7F,EAAE,CAACwG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EAC1C;EAMAC,gCAAgCA,CAAC7H,IAAY,EAAE0H,WAAoB,EAAE;IACnE,OAAOvK,UAAU,CAAC,IAAI,CAACsK,sBAAsB,CAACzH,IAAI,EAAE0H,WAAW,CAAC,CAAC;EACnE;EAYAI,QAAQA,CAAC9H,IAAY,EAAW;IAC9B,IAAIpB,gBAAgB,CAACoB,IAAI,CAAC,IAAIvB,OAAO,CAACuB,IAAI,CAAC,IAAIP,gBAAgB,CAACO,IAAI,CAAC,EAAE;MACrE,OAAO,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAImD,OAAO,EAAE;QACX,OAAOA,OAAO,CAAC8D,QAAQ;MACzB,CAAC,MAAM;QACL,OAAO,IAAI,CAACV,UAAU,CAACrH,IAAI,CAACc,IAAI,CAAC;MACnC;IACF;IAEA,OAAO,KAAK;EACd;EAMAkH,qBAAqBA,CAAChI,IAAY,EAAEiI,QAAkB,EAAE;IACtD,IAAI,IAAI,CAACH,QAAQ,CAAC9H,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAMoB,EAAE,GAAG,IAAI,CAACyG,gCAAgC,CAAC7H,IAAI,CAAC;MACtD,IAAI,CAACiI,QAAQ,EAAE;QACb,IAAI,CAACvH,IAAI,CAAC;UAAEU;QAAG,CAAC,CAAC;QACjB,OAAOnE,SAAS,CAACmE,EAAE,CAAC;MACtB;MACA,OAAOA,EAAE;IACX;EACF;EAEA8G,0BAA0BA,CACxBzH,KAAc,EACd0H,IAAiB,EACjBrH,IAAY,EACZM,EAAO,EACP;IAEA,IAAI+G,IAAI,KAAK,OAAO,EAAE;IAItB,IAAI1H,KAAK,CAAC0H,IAAI,KAAK,OAAO,EAAE;IAE5B,MAAMC,SAAS,GAEbD,IAAI,KAAK,KAAK,IACd1H,KAAK,CAAC0H,IAAI,KAAK,KAAK,IACpB1H,KAAK,CAAC0H,IAAI,KAAK,OAAO,IACtB1H,KAAK,CAAC0H,IAAI,KAAK,QAAQ,IAEtB1H,KAAK,CAAC0H,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAQ;IAE9C,IAAIC,SAAS,EAAE;MACb,MAAM,IAAI,CAAC7F,IAAI,CAAC8F,GAAG,CAACC,UAAU,CAC5BlH,EAAE,EACF,0BAA0BN,IAAI,GAAG,EACjCyH,SACF,CAAC;IACH;EACF;EAEAC,MAAMA,CACJC,OAAe,EACfC,OAAgB,EAGhB;IACA,MAAMzE,OAAO,GAAG,IAAI,CAACC,UAAU,CAACuE,OAAO,CAAC;IACxC,IAAIxE,OAAO,EAAE;MACXyE,OAAO,KAAPA,OAAO,GAAK,IAAI,CAAC1B,qBAAqB,CAACyB,OAAO,CAAC,CAAC3H,IAAI;MACpD,MAAM6H,OAAO,GAAG,IAAIC,gBAAO,CAAC3E,OAAO,EAAEwE,OAAO,EAAEC,OAAO,CAAC;MAG/C;QAELC,OAAO,CAACH,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;MAC9B;IACF;EACF;EAEAC,IAAIA,CAAA,EAAG;IACL,MAAMC,GAAG,GAAG,GAAG,CAACC,MAAM,CAAC,EAAE,CAAC;IAC1BC,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAChB,IAAIpH,KAAY,GAAG,IAAI;IACvB,GAAG;MACDsH,OAAO,CAACC,GAAG,CAAC,GAAG,EAAEvH,KAAK,CAAC+D,KAAK,CAACxF,IAAI,CAAC;MAClC,KAAK,MAAMY,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACK,QAAQ,CAAC,EAAE;QAC9C,MAAMiC,OAAO,GAAGtC,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC;QACpCmI,OAAO,CAACC,GAAG,CAAC,IAAI,EAAEpI,IAAI,EAAE;UACtBiH,QAAQ,EAAE9D,OAAO,CAAC8D,QAAQ;UAC1BnG,UAAU,EAAEqC,OAAO,CAACrC,UAAU;UAC9BuH,UAAU,EAAElF,OAAO,CAACH,kBAAkB,CAACxD,MAAM;UAC7C6H,IAAI,EAAElE,OAAO,CAACkE;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,QAASxG,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9BgG,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;EAClB;EAEA3B,QAAQA,CAACtG,IAAY,EAAE;IACrB,OAAO,CAAC,CAAC,IAAI,CAACsI,QAAQ,CAACtI,IAAI,CAAC;EAC9B;EAEAsI,QAAQA,CAACtI,IAAY,EAAE;IACrB,OAAO,IAAI,CAAC8E,MAAM,CAACnD,GAAG,CAAC3B,IAAI,CAAC;EAC9B;EAEAuI,aAAaA,CAAC9G,IAAkC,EAAE;IAChD,IAAI,CAACqD,MAAM,CAACO,GAAG,CAAC5D,IAAI,CAACvC,IAAI,CAACsJ,KAAK,CAACxI,IAAI,EAAEyB,IAAI,CAAC;EAC7C;EAEAW,mBAAmBA,CAACX,IAAc,EAAE;IAClC,IAAIA,IAAI,CAACgH,kBAAkB,CAAC,CAAC,EAAE;MAC7B,IAAI,CAACF,aAAa,CAAC9G,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIA,IAAI,CAAC1E,qBAAqB,CAAC,CAAC,EAAE;MACvC,IAAI,CAACiF,eAAe,CAAC,SAAS,EAAEP,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,EAAEF,IAAI,CAAC;IACvD,CAAC,MAAM,IAAIA,IAAI,CAACzD,qBAAqB,CAAC,CAAC,EAAE;MACvC,MAAMuF,YAAY,GAAG9B,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7C,MAAM;QAAE0F;MAAK,CAAC,GAAG5F,IAAI,CAACvC,IAAI;MAC1B,KAAK,MAAMwC,MAAM,IAAI6B,YAAY,EAAE;QACjC,IAAI,CAACvB,eAAe,CAClBqF,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,aAAa,GAAG,OAAO,GAAGA,IAAI,EAC3D3F,MACF,CAAC;MACH;IACF,CAAC,MAAM,IAAID,IAAI,CAAC9E,kBAAkB,CAAC,CAAC,EAAE;MACpC,IAAI8E,IAAI,CAACvC,IAAI,CAACwJ,OAAO,EAAE;MACvB,IAAI,CAAC1G,eAAe,CAAC,KAAK,EAAEP,IAAI,CAAC;IACnC,CAAC,MAAM,IAAIA,IAAI,CAACxE,mBAAmB,CAAC,CAAC,EAAE;MACrC,MAAM0L,iBAAiB,GACrBlH,IAAI,CAACvC,IAAI,CAAC0J,UAAU,KAAK,MAAM,IAAInH,IAAI,CAACvC,IAAI,CAAC0J,UAAU,KAAK,QAAQ;MACtE,MAAMrJ,UAAU,GAAGkC,IAAI,CAACE,GAAG,CAAC,YAAY,CAAC;MACzC,KAAK,MAAMkH,SAAS,IAAItJ,UAAU,EAAE;QAClC,MAAMuJ,eAAe,GACnBH,iBAAiB,IAChBE,SAAS,CAACE,iBAAiB,CAAC,CAAC,KAC3BF,SAAS,CAAC3J,IAAI,CAAC0J,UAAU,KAAK,MAAM,IACnCC,SAAS,CAAC3J,IAAI,CAAC0J,UAAU,KAAK,QAAQ,CAAE;QAE9C,IAAI,CAAC5G,eAAe,CAAC8G,eAAe,GAAG,SAAS,GAAG,QAAQ,EAAED,SAAS,CAAC;MACzE;IACF,CAAC,MAAM,IAAIpH,IAAI,CAAC3C,mBAAmB,CAAC,CAAC,EAAE;MAErC,MAAM4C,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAC,aAAa,CAAa;MAClD,IACED,MAAM,CAAC/E,kBAAkB,CAAC,CAAC,IAC3B+E,MAAM,CAAC3E,qBAAqB,CAAC,CAAC,IAC9B2E,MAAM,CAAC1D,qBAAqB,CAAC,CAAC,EAC9B;QACA,IAAI,CAACoE,mBAAmB,CAACV,MAAM,CAAC;MAClC;IACF,CAAC,MAAM;MACL,IAAI,CAACM,eAAe,CAAC,SAAS,EAAEP,IAAI,CAAC;IACvC;EACF;EAEA1C,kBAAkBA,CAAA,EAAG;IACnB,OAAOA,kBAAkB,CAAC,CAAC;EAC7B;EAEAiK,yBAAyBA,CAACvH,IAAc,EAAE;IACxC,MAAMwH,GAAG,GAAGxH,IAAI,CAACyH,wBAAwB,CAAC,CAAC;IAC3C,KAAK,MAAMlJ,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACyF,GAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACnC,CAAAA,gBAAA,OAAI,CAAC/F,UAAU,CAACpD,IAAI,CAAC,aAArBmJ,gBAAA,CAAuBC,QAAQ,CAAC3H,IAAI,CAAC;IACvC;EACF;EAEAO,eAAeA,CACbqF,IAAqB,EACrB5F,IAAc,EACd4H,WAAqB,GAAG5H,IAAI,EAC5B;IACA,IAAI,CAAC4F,IAAI,EAAE,MAAM,IAAIiC,cAAc,CAAC,WAAW,CAAC;IAEhD,IAAI7H,IAAI,CAACzD,qBAAqB,CAAC,CAAC,EAAE;MAChC,MAAMuL,WAA4B,GAAG9H,IAAI,CAACE,GAAG,CAAC,cAAc,CAAC;MAC7D,KAAK,MAAMD,MAAM,IAAI6H,WAAW,EAAE;QAChC,IAAI,CAACvH,eAAe,CAACqF,IAAI,EAAE3F,MAAM,CAAC;MACpC;MACA;IACF;IAEA,MAAMS,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAC,CAAC;IACtC,MAAMkH,GAAG,GAAGxH,IAAI,CAAC+H,0BAA0B,CAAC,IAAI,CAAC;IAEjD,KAAK,MAAMxJ,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACyF,GAAG,CAAC,EAAE;MAG5B;QAEL9G,MAAM,CAACrB,UAAU,CAACd,IAAI,CAAC,GAAG,IAAI;MAChC;MAEA,KAAK,MAAMM,EAAE,IAAI2I,GAAG,CAACjJ,IAAI,CAAC,EAAE;QAC1B,MAAML,KAAK,GAAG,IAAI,CAAC8J,aAAa,CAACzJ,IAAI,CAAC;QAEtC,IAAIL,KAAK,EAAE;UAGT,IAAIA,KAAK,CAACtD,UAAU,KAAKiE,EAAE,EAAE;UAE7B,IAAI,CAAC8G,0BAA0B,CAACzH,KAAK,EAAE0H,IAAI,EAAErH,IAAI,EAAEM,EAAE,CAAC;QACxD;QAGA,IAAIX,KAAK,EAAE;UACTA,KAAK,CAACyJ,QAAQ,CAACC,WAAW,CAAC;QAC7B,CAAC,MAAM;UACL,IAAI,CAACnI,QAAQ,CAAClB,IAAI,CAAC,GAAG,IAAI0J,gBAAO,CAAC;YAChCrN,UAAU,EAAEiE,EAAE;YACdO,KAAK,EAAE,IAAI;YACXY,IAAI,EAAE4H,WAAW;YACjBhC,IAAI,EAAEA;UACR,CAAC,CAAC;QACJ;MACF;IACF;EACF;EAEAsC,SAASA,CAACzK,IAAoC,EAAE;IAC9C,IAAI,CAACiC,OAAO,CAACjC,IAAI,CAACc,IAAI,CAAC,GAAGd,IAAI;EAChC;EAEA0K,MAAMA,CAAC5J,IAAY,EAAW;IAGrB;MACL,IAAIa,KAAY,GAAG,IAAI;MAEvB,GAAG;QAED,IAAIA,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,EAAE,OAAO,IAAI;MACnC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;MAE9B,OAAO,KAAK;IACd;EACF;EAEAqE,SAASA,CAACxG,IAAY,EAAW;IAC/B,IAAIa,KAAY,GAAG,IAAI;IAEvB,GAAG;MACD,IAAIA,KAAK,CAACM,OAAO,CAACnB,IAAI,CAAC,EAAE,OAAO,IAAI;IACtC,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,OAAO,KAAK;EACd;EAEAsE,YAAYA,CAACzG,IAAY,EAAW;IAG3B;MAEL,OAAO,CAAC,CAAC,IAAI,CAAC+B,gBAAgB,CAAC,CAAC,CAACjB,UAAU,CAACd,IAAI,CAAC;IACnD;EACF;EAEA6J,MAAMA,CAAC3K,IAAY,EAAE4K,aAAuB,EAAW;IACrD,IAAI9M,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAI,CAACmD,OAAO,EAAE,OAAO,KAAK;MAC1B,IAAI2G,aAAa,EAAE,OAAO3G,OAAO,CAAC8D,QAAQ;MAC1C,OAAO,IAAI;IACb,CAAC,MAAM,IACLnJ,gBAAgB,CAACoB,IAAI,CAAC,IACtBN,cAAc,CAACM,IAAI,CAAC,IACpBP,gBAAgB,CAACO,IAAI,CAAC,IACtBL,aAAa,CAACK,IAAI,CAAC,EACnB;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIzC,OAAO,CAACyC,IAAI,CAAC,EAAE;MAAA,IAAA6K,gBAAA;MACxB,IAAI7K,IAAI,CAAC8K,UAAU,IAAI,CAAC,IAAI,CAACH,MAAM,CAAC3K,IAAI,CAAC8K,UAAU,EAAEF,aAAa,CAAC,EAAE;QACnE,OAAO,KAAK;MACd;MACA,IAAI,EAAAC,gBAAA,GAAA7K,IAAI,CAAC+K,UAAU,qBAAfF,gBAAA,CAAiBvK,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI,CAACqK,MAAM,CAAC3K,IAAI,CAACgL,IAAI,EAAEJ,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAIpN,WAAW,CAACwC,IAAI,CAAC,EAAE;MAC5B,KAAK,MAAMiL,MAAM,IAAIjL,IAAI,CAACgL,IAAI,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACL,MAAM,CAACM,MAAM,EAAEL,aAAa,CAAC,EAAE,OAAO,KAAK;MACvD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIvN,QAAQ,CAAC2C,IAAI,CAAC,EAAE;MACzB,OACE,IAAI,CAAC2K,MAAM,CAAC3K,IAAI,CAACmB,IAAI,EAAEyJ,aAAa,CAAC,IACrC,IAAI,CAACD,MAAM,CAAC3K,IAAI,CAACyD,KAAK,EAAEmH,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAIxN,iBAAiB,CAAC4C,IAAI,CAAC,IAAIT,iBAAiB,CAACS,IAAI,CAAC,EAAE;MAC7D,KAAK,MAAMkL,IAAI,IAAIlL,IAAI,CAACmL,QAAQ,EAAE;QAChC,IAAID,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAACP,MAAM,CAACO,IAAI,EAAEN,aAAa,CAAC,EAAE,OAAO,KAAK;MACtE;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIvM,kBAAkB,CAAC2B,IAAI,CAAC,IAAIV,kBAAkB,CAACU,IAAI,CAAC,EAAE;MAC/D,KAAK,MAAMoL,IAAI,IAAIpL,IAAI,CAACgB,UAAU,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC2J,MAAM,CAACS,IAAI,EAAER,aAAa,CAAC,EAAE,OAAO,KAAK;MACrD;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI1M,QAAQ,CAAC8B,IAAI,CAAC,EAAE;MAAA,IAAAqL,iBAAA;MACzB,IAAIrL,IAAI,CAACsL,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAAC3K,IAAI,CAACkB,GAAG,EAAE0J,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAS,iBAAA,GAAArL,IAAI,CAAC+K,UAAU,qBAAfM,iBAAA,CAAiB/K,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIhC,UAAU,CAAC0B,IAAI,CAAC,EAAE;MAAA,IAAAuL,iBAAA;MAE3B,IAAIvL,IAAI,CAACsL,QAAQ,IAAI,CAAC,IAAI,CAACX,MAAM,CAAC3K,IAAI,CAACkB,GAAG,EAAE0J,aAAa,CAAC,EAAE,OAAO,KAAK;MACxE,IAAI,EAAAW,iBAAA,GAAAvL,IAAI,CAAC+K,UAAU,qBAAfQ,iBAAA,CAAiBjL,MAAM,IAAG,CAAC,EAAE;QAC/B,OAAO,KAAK;MACd;MACA,IAAId,gBAAgB,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAACwL,MAAM,EAAE;QACzC,IAAIxL,IAAI,CAACW,KAAK,KAAK,IAAI,IAAI,CAAC,IAAI,CAACgK,MAAM,CAAC3K,IAAI,CAACW,KAAK,EAAEiK,aAAa,CAAC,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAI/L,iBAAiB,CAACmB,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI,CAAC2K,MAAM,CAAC3K,IAAI,CAACiB,QAAQ,EAAE2J,aAAa,CAAC;IAClD,CAAC,MAAM,IAAIjM,iBAAiB,CAACqB,IAAI,CAAC,EAAE;MAClC,KAAK,MAAMqB,UAAU,IAAIrB,IAAI,CAACyL,WAAW,EAAE;QACzC,IAAI,CAAC,IAAI,CAACd,MAAM,CAACtJ,UAAU,EAAEuJ,aAAa,CAAC,EAAE,OAAO,KAAK;MAC3D;MACA,OAAO,IAAI;IACb,CAAC,MAAM,IAAIlM,0BAA0B,CAACsB,IAAI,CAAC,EAAE;MAC3C,OACEhB,cAAc,CAACgB,IAAI,CAAC0L,GAAG,EAAE,YAAY,CAAC,IACtC,CAAC,IAAI,CAACrE,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C,IAAI,CAAChB,MAAM,CAAC3K,IAAI,CAAC4L,KAAK,EAAEhB,aAAa,CAAC;IAE1C,CAAC,MAAM,IAAI3M,kBAAkB,CAAC+B,IAAI,CAAC,EAAE;MACnC,OACE,CAACA,IAAI,CAACsL,QAAQ,IACdxN,YAAY,CAACkC,IAAI,CAACY,MAAM,CAAC,IACzBZ,IAAI,CAACY,MAAM,CAACE,IAAI,KAAK,QAAQ,IAC7BhD,YAAY,CAACkC,IAAI,CAACa,QAAQ,CAAC,IAC3Bb,IAAI,CAACa,QAAQ,CAACC,IAAI,KAAK,KAAK,IAC5B,CAAC,IAAI,CAACuG,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC;IAEnD,CAAC,MAAM,IAAIrO,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MACjC,OACEhB,cAAc,CAACgB,IAAI,CAACe,MAAM,EAAE,YAAY,CAAC,IACzC,CAAC,IAAI,CAACsG,UAAU,CAAC,QAAQ,EAAE;QAAEsE,SAAS,EAAE;MAAK,CAAC,CAAC,IAC/C3L,IAAI,CAAC6I,SAAS,CAACvI,MAAM,KAAK,CAAC,IAC3B3D,CAAC,CAACkP,eAAe,CAAC7L,IAAI,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC;IAExC,CAAC,MAAM;MACL,OAAOtK,SAAS,CAACyB,IAAI,CAAC;IACxB;EACF;EAMA8L,OAAOA,CAAC5K,GAAoB,EAAE6K,GAAQ,EAAE;IACtC,OAAQ,IAAI,CAAChG,IAAI,CAAC7E,GAAG,CAAC,GAAG6K,GAAG;EAC9B;EAMAC,OAAOA,CAAC9K,GAAoB,EAAO;IACjC,IAAIS,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMoE,IAAI,GAAGpE,KAAK,CAACoE,IAAI,CAAC7E,GAAG,CAAC;MAC5B,IAAI6E,IAAI,IAAI,IAAI,EAAE,OAAOA,IAAI;IAC/B,CAAC,QAASpE,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAOAgJ,UAAUA,CAAC/K,GAAW,EAAE;IACtB,IAAIS,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,MAAMoE,IAAI,GAAGpE,KAAK,CAACoE,IAAI,CAAC7E,GAAG,CAAC;MAC5B,IAAI6E,IAAI,IAAI,IAAI,EAAEpE,KAAK,CAACoE,IAAI,CAAC7E,GAAG,CAAC,GAAG,IAAI;IAC1C,CAAC,QAASS,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAEAiJ,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACvG,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG,IAAI;MAClB,IAAI,CAACwG,KAAK,CAAC,CAAC;IACd;EACF;EAEAA,KAAKA,CAAA,EAAG;IACN,MAAM5J,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtBb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI,CAACqE,IAAI,GAAGlE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACqE,QAAQ,EAAE;MACpB,IAAIrE,KAAK,CAACY,IAAI,CAAC6J,SAAS,CAAC,CAAC,EAAE;QAC1B;MACF;IACF,CAAC,QAASzK,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,MAAMoJ,aAAa,GAAG1K,KAAK;IAE3B,MAAM4B,KAA0B,GAAG;MACjC3B,UAAU,EAAE,EAAE;MACdkC,kBAAkB,EAAE,EAAE;MACtBW,WAAW,EAAE;IACf,CAAC;IAED,IAAI,CAACuB,QAAQ,GAAG,IAAI;IACpBV,YAAY,KAAZA,YAAY,GAAKgH,cAAQ,CAACC,QAAQ,CAACC,KAAK,CAAC,CACvC;MACEhH,KAAKA,CAACjD,IAAI,EAAE;QACVb,UAAU,CAACa,IAAI,CAACZ,KAAK,CAAC;MACxB;IACF,CAAC,EACDU,gBAAgB,CACjB,CAAC;IAGF,IAAIE,IAAI,CAACrC,IAAI,KAAK,SAAS,EAAE;MAC3B,KAAK,MAAMuM,KAAK,IAAInH,YAAY,CAACoH,KAAK,EAAE;QACtCD,KAAK,CAACE,IAAI,CAACpJ,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;MAChC;MACA,MAAMqJ,YAAY,GAAGtH,YAAY,CAAC/C,IAAI,CAACrC,IAAI,CAAC;MAC5C,IAAI0M,YAAY,EAAE;QAChB,KAAK,MAAMH,KAAK,IAAIG,YAAY,CAACF,KAAK,EAAE;UACtCD,KAAK,CAACE,IAAI,CAACpJ,KAAK,EAAEhB,IAAI,EAAEgB,KAAK,CAAC;QAChC;MACF;IACF;IACAhB,IAAI,CAAC+J,QAAQ,CAAChH,YAAY,EAAE/B,KAAK,CAAC;IAClC,IAAI,CAACyC,QAAQ,GAAG,KAAK;IAGrB,KAAK,MAAMzD,IAAI,IAAIgB,KAAK,CAACkB,WAAW,EAAE;MAEpC,MAAMsF,GAAG,GAAGxH,IAAI,CAACyH,wBAAwB,CAAC,CAAC;MAC3C,KAAK,MAAMlJ,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAACyF,GAAG,CAAC,EAAE;QACnC,IAAIxH,IAAI,CAACZ,KAAK,CAACuC,UAAU,CAACpD,IAAI,CAAC,EAAE;QACjCuL,aAAa,CAAC5B,SAAS,CAACV,GAAG,CAACjJ,IAAI,CAAC,CAAC;MACpC;MAGAyB,IAAI,CAACZ,KAAK,CAACmI,yBAAyB,CAACvH,IAAI,CAAC;IAC5C;IAGA,KAAK,MAAMsK,GAAG,IAAItJ,KAAK,CAAC3B,UAAU,EAAE;MAClC,MAAMqC,OAAO,GAAG4I,GAAG,CAAClL,KAAK,CAACuC,UAAU,CAAC2I,GAAG,CAAC7M,IAAI,CAACc,IAAI,CAAC;MACnD,IAAImD,OAAO,EAAE;QACXA,OAAO,CAACE,SAAS,CAAC0I,GAAG,CAAC;MACxB,CAAC,MAAM;QACLR,aAAa,CAAC5B,SAAS,CAACoC,GAAG,CAAC7M,IAAI,CAAC;MACnC;IACF;IAGA,KAAK,MAAMuC,IAAI,IAAIgB,KAAK,CAACO,kBAAkB,EAAE;MAC3CvB,IAAI,CAACZ,KAAK,CAACmI,yBAAyB,CAACvH,IAAI,CAAC;IAC5C;EACF;EAEA7B,IAAIA,CAACoM,IAMJ,EAAE;IACD,IAAIvK,IAAI,GAAG,IAAI,CAACA,IAAI;IAEpB,IAAIA,IAAI,CAACsB,SAAS,CAAC,CAAC,EAAE;MACpBtB,IAAI,GAAG,IAAI,CAACwK,gBAAgB,CAAC,CAAC,CAACxK,IAAI;IACrC,CAAC,MAAM,IAAI,CAACA,IAAI,CAACyK,gBAAgB,CAAC,CAAC,IAAI,CAACzK,IAAI,CAAC6J,SAAS,CAAC,CAAC,EAAE;MACxD7J,IAAI,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAACb,IAAI;IACnC;IAEA,IAAIA,IAAI,CAAC0K,iBAAiB,CAAC,CAAC,EAAE;MAC5B1K,IAAI,GAAG,CAAC,IAAI,CAACK,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEN,IAAI;IACnE;IAEA,MAAM;MAAE2J,IAAI;MAAEgB,MAAM;MAAE/E,IAAI,GAAG,KAAK;MAAE/G;IAAG,CAAC,GAAG0L,IAAI;IAM/C,IACE,CAACZ,IAAI,IACL,CAACgB,MAAM,KACN/E,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,CAAC,IAClC5F,IAAI,CAAC4K,UAAU,CAAC,CAAC,IAEjB,CAAC5K,IAAI,CAACvC,IAAI,CAACc,IAAI,IACfxD,gBAAgB,CAACiF,IAAI,CAACU,MAAM,EAAE;MAAElC,MAAM,EAAEwB,IAAI,CAACvC;IAAK,CAAC,CAAC,IACpDuC,IAAI,CAACU,MAAM,CAAC4F,SAAS,CAACvI,MAAM,IAAIiC,IAAI,CAACvC,IAAI,CAACgF,MAAM,CAAC1E,MAAM,IACvDxC,YAAY,CAACsD,EAAE,CAAC,EAChB;MACAmB,IAAI,CAAC6K,aAAa,CAAC,QAAQ,EAAEhM,EAAE,CAAC;MAChCmB,IAAI,CAACZ,KAAK,CAACmB,eAAe,CACxB,OAAO,EACPP,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACF,IAAI,CAACvC,IAAI,CAACgF,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAChD,CAAC;MACD;IACF;IAEA,IAAIiC,IAAI,CAAC8K,MAAM,CAAC,CAAC,IAAI9K,IAAI,CAAC+K,aAAa,CAAC,CAAC,IAAI/K,IAAI,CAAC4K,UAAU,CAAC,CAAC,EAAE;MAC9D5K,IAAI,CAACgL,WAAW,CAAC,CAAC;MAClBhL,IAAI,GAAGA,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC;IACzB;IAEA,MAAM+K,UAAU,GAAGV,IAAI,CAACW,WAAW,IAAI,IAAI,GAAG,CAAC,GAAGX,IAAI,CAACW,WAAW;IAElE,MAAMC,OAAO,GAAG,eAAevF,IAAI,IAAIqF,UAAU,EAAE;IACnD,IAAIG,UAAU,GAAG,CAACT,MAAM,IAAI3K,IAAI,CAACyJ,OAAO,CAAC0B,OAAO,CAAC;IAEjD,IAAI,CAACC,UAAU,EAAE;MACf,MAAMnL,MAAM,GAAGpD,mBAAmB,CAAC+I,IAAI,EAAE,EAAE,CAAC;MAE5C3F,MAAM,CAACiL,WAAW,GAAGD,UAAU;MAE/B,CAACG,UAAU,CAAC,GAAIpL,IAAI,CAAgCqL,gBAAgB,CAClE,MAAM,EACN,CAACpL,MAAM,CACT,CAAC;MACD,IAAI,CAAC0K,MAAM,EAAE3K,IAAI,CAACuJ,OAAO,CAAC4B,OAAO,EAAEC,UAAU,CAAC;IAChD;IAEA,MAAME,UAAU,GAAGxO,kBAAkB,CAAC+B,EAAE,EAAE8K,IAAI,CAAC;IAC/C,MAAM4B,GAAG,GAAGH,UAAU,CAAC3N,IAAI,CAACqE,YAAY,CAAC3D,IAAI,CAACmN,UAAU,CAAC;IACzDtL,IAAI,CAACZ,KAAK,CAACmB,eAAe,CAACqF,IAAI,EAAEwF,UAAU,CAAClL,GAAG,CAAC,cAAc,CAAC,CAACqL,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E;EAMAjL,gBAAgBA,CAAA,EAGd;IACA,IAAIlB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAAC6J,SAAS,CAAC,CAAC,EAAE;QAC1B,OAAOzK,KAAK;MAId;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,MAAM,IAAI6D,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAMAlE,iBAAiBA,CAAA,EAAiB;IAChC,IAAIjB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAACwL,gBAAgB,CAAC,CAAC,EAAE;QACjC,OAAOpM,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,OAAO,IAAI;EACb;EAOAG,cAAcA,CAAA,EAAG;IACf,IAAIzB,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIA,KAAK,CAACY,IAAI,CAACyL,aAAa,CAAC,CAAC,EAAE;QAC9B,OAAOrM,KAAK;MACd;IACF,CAAC,QAASA,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAC9B,MAAM,IAAI6D,KAAK,CACb,8EACF,CAAC;EACH;EAOAiG,gBAAgBA,CAAA,EAAG;IACjB,IAAIpL,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAI,CAACA,KAAK,CAACY,IAAI,CAACsB,SAAS,CAAC,CAAC,EAAE;QAC3B,OAAOlC,KAAK,CAACyB,cAAc,CAAC,CAAC;MAC/B;IACF,CAAC,QAASzB,KAAK,GAAGA,KAAK,CAACsB,MAAM,CAACA,MAAM;IACrC,MAAM,IAAI6D,KAAK,CACb,8EACF,CAAC;EACH;EAMAmH,cAAcA,CAAA,EAA4B;IACxC,MAAMlE,GAAG,GAAGlI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,IAAIH,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,KAAK,MAAMT,GAAG,IAAIW,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACK,QAAQ,CAAC,EAAE;QAC7C,IAAId,GAAG,IAAI6I,GAAG,KAAK,KAAK,EAAE;UACxBA,GAAG,CAAC7I,GAAG,CAAC,GAAGS,KAAK,CAACK,QAAQ,CAACd,GAAG,CAAC;QAChC;MACF;MACAS,KAAK,GAAGA,KAAK,CAACsB,MAAM;IACtB,CAAC,QAAQtB,KAAK;IAEd,OAAOoI,GAAG;EACZ;EAEAmE,uBAAuBA,CAACpN,IAAY,EAAEd,IAAY,EAAW;IAC3D,OAAO,IAAI,CAACmO,oBAAoB,CAACrN,IAAI,CAAC,KAAKd,IAAI;EACjD;EAEAkE,UAAUA,CAACpD,IAAY,EAAuB;IAC5C,IAAIa,KAAY,GAAG,IAAI;IACvB,IAAIyM,YAAY;IAEhB,GAAG;MACD,MAAMnK,OAAO,GAAGtC,KAAK,CAAC4I,aAAa,CAACzJ,IAAI,CAAC;MACzC,IAAImD,OAAO,EAAE;QAAA,IAAAoK,aAAA;QAUX,IACE,CAAAA,aAAA,GAAAD,YAAY,aAAZC,aAAA,CAAcxK,SAAS,CAAC,CAAC,IACzBI,OAAO,CAACkE,IAAI,KAAK,OAAO,IACxBlE,OAAO,CAACkE,IAAI,KAAK,OAAO,EACxB,CAEF,CAAC,MAAM;UACL,OAAOlE,OAAO;QAChB;MACF,CAAC,MAAM,IACL,CAACA,OAAO,IACRnD,IAAI,KAAK,WAAW,IACpBa,KAAK,CAACY,IAAI,CAAC4K,UAAU,CAAC,CAAC,IACvB,CAACxL,KAAK,CAACY,IAAI,CAAC+L,yBAAyB,CAAC,CAAC,EACvC;QACA;MACF;MACAF,YAAY,GAAGzM,KAAK,CAACY,IAAI;IAC3B,CAAC,QAASZ,KAAK,GAAGA,KAAK,CAACsB,MAAM;EAChC;EAEAsH,aAAaA,CAACzJ,IAAY,EAAuB;IAC/C,OAAO,IAAI,CAACkB,QAAQ,CAAClB,IAAI,CAAC;EAC5B;EAGAqN,oBAAoBA,CAACrN,IAAY,EAAgB;IAAA,IAAAyN,iBAAA;IAC/C,QAAAA,iBAAA,GAAO,IAAI,CAACrK,UAAU,CAACpD,IAAI,CAAC,qBAArByN,iBAAA,CAAuBpR,UAAU;EAC1C;EAGAqR,uBAAuBA,CAAC1N,IAAY,EAAgB;IAClD,MAAMmD,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAAClB,IAAI,CAAC;IACnC,OAAOmD,OAAO,oBAAPA,OAAO,CAAE9G,UAAU;EAC5B;EAEAsR,aAAaA,CAAC3N,IAAY,EAAE;IAC1B,OAAO,CAAC,CAAC,IAAI,CAACyJ,aAAa,CAACzJ,IAAI,CAAC;EACnC;EAQAuG,UAAUA,CACRvG,IAAY,EACZgM,IAEgE,EAChE;IACA,IAAI,CAAChM,IAAI,EAAE,OAAO,KAAK;IAEvB,IAAI6K,SAAS;IACb,IAAI+C,MAAM;IACV,IAAIC,SAAS;IACb,IAAI,OAAO7B,IAAI,KAAK,QAAQ,EAAE;MAC5BnB,SAAS,GAAGmB,IAAI,CAACnB,SAAS;MAC1B+C,MAAM,GAAG5B,IAAI,CAAC4B,MAAM;MACpBC,SAAS,GAAG7B,IAAI,CAAC6B,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAO7B,IAAI,KAAK,SAAS,EAAE;MACpCnB,SAAS,GAAGmB,IAAI;IAClB;IACA,IAAInL,KAAY,GAAG,IAAI;IACvB,GAAG;MACD,IAAIgN,SAAS,KAAKhN,KAAK,EAAE;QACvB;MACF;MACA,IAAIA,KAAK,CAAC8M,aAAa,CAAC3N,IAAI,CAAC,EAAE;QAC7B,OAAO,IAAI;MACb;IACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAE9B,IAAI,CAACyL,MAAM,IAAI,IAAI,CAAChE,MAAM,CAAC5J,IAAI,CAAC,EAAE,OAAO,IAAI;IAC7C,IAAI,CAAC6K,SAAS,IAAInG,KAAK,CAACvD,OAAO,CAAC2M,QAAQ,CAAC9N,IAAI,CAAC,EAAE,OAAO,IAAI;IAC3D,IAAI,CAAC6K,SAAS,IAAInG,KAAK,CAACqJ,gBAAgB,CAACD,QAAQ,CAAC9N,IAAI,CAAC,EAAE,OAAO,IAAI;IACpE,OAAO,KAAK;EACd;EAEAgO,gBAAgBA,CACdhO,IAAY,EACZgM,IAAgD,EAChD;IAAA,IAAAiC,YAAA;IACA,QAAAA,YAAA,GAAO,IAAI,CAAC9L,MAAM,qBAAX8L,YAAA,CAAa1H,UAAU,CAACvG,IAAI,EAAEgM,IAAI,CAAC;EAC5C;EAMAkC,aAAaA,CAAClO,IAAY,EAAEa,KAAY,EAAE;IACxC,MAAMsN,IAAI,GAAG,IAAI,CAAC/K,UAAU,CAACpD,IAAI,CAAC;IAClC,IAAImO,IAAI,EAAE;MACRA,IAAI,CAACtN,KAAK,CAACuN,gBAAgB,CAACpO,IAAI,CAAC;MACjCmO,IAAI,CAACtN,KAAK,GAAGA,KAAK;MAClBA,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC,GAAGmO,IAAI;IAC7B;EACF;EAEAC,gBAAgBA,CAACpO,IAAY,EAAE;IAC7B,OAAO,IAAI,CAACkB,QAAQ,CAAClB,IAAI,CAAC;EAC5B;EAEAqO,aAAaA,CAACrO,IAAY,EAAE;IAAA,IAAAsO,iBAAA;IAE1B,CAAAA,iBAAA,OAAI,CAAClL,UAAU,CAACpD,IAAI,CAAC,aAArBsO,iBAAA,CAAuBzN,KAAK,CAACuN,gBAAgB,CAACpO,IAAI,CAAC;IAK5C;MACL,IAAIa,KAAY,GAAG,IAAI;MACvB,GAAG;QAED,IAAIA,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,EAAE;UAEpBa,KAAK,CAACI,IAAI,CAACjB,IAAI,CAAC,GAAG,KAAK;QAC1B;MACF,CAAC,QAASa,KAAK,GAAGA,KAAK,CAACsB,MAAM;IAChC;EACF;EAYAoM,cAAcA,CACZC,IAAkD,GAAGlO,EAAE,IACrD,IAAI,CAACV,IAAI,CAAC;IAAEU;EAAG,CAAC,CAAC,EACnB;IACA,IAAI,CAAC+K,KAAK,CAAC,CAAC;IAEZ,MAAMoD,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,KAAK,MAAM1O,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC,IAAI,CAACtC,QAAQ,CAAC,EAAE;MAC7C,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAAClB,IAAI,CAAC;MACnC,IAAI,CAACmD,OAAO,EAAE;MACd,MAAM;QAAE1B;MAAK,CAAC,GAAG0B,OAAO;MACxB,IAAI,CAAC1B,IAAI,CAACkN,oBAAoB,CAAC,CAAC,EAAE;MAClC,MAAM;QAAExM,MAAM;QAAES;MAAW,CAAC,GAAGnB,IAAI;MAEnC,IAAIU,MAAM,CAACkF,IAAI,KAAK,KAAK,IAAIoH,IAAI,CAACG,GAAG,CAACzM,MAAM,CAAC,EAAE;MAC/CsM,IAAI,CAACI,GAAG,CAACpN,IAAI,CAACU,MAAM,CAAC;MAErB,IAAI2M,OAAO;MACX,MAAM1D,IAAI,GAAG,EAAE;MACf,KAAK,MAAM9H,IAAI,IAAInB,MAAM,CAACoB,YAAY,EAAE;QACtCuL,OAAO,WAAPA,OAAO,GAAPA,OAAO,GAAKxL,IAAI,CAAChD,EAAE;QACnB,IAAIgD,IAAI,CAAC8H,IAAI,EAAE;UACbA,IAAI,CAACxL,IAAI,CACP3D,oBAAoB,CAClB,GAAG,EAEHqH,IAAI,CAAChD,EAAE,EACPgD,IAAI,CAAC8H,IACP,CACF,CAAC;QACH;QAEA,MAAMnC,GAAG,GAAGlI,MAAM,CAACyC,IAAI,CAACpH,qBAAqB,CAACkH,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvE,KAAK,MAAMtD,IAAI,IAAIiJ,GAAG,EAAE;UACtBuF,IAAI,CAACnS,UAAU,CAAC2D,IAAI,CAAC,EAAEsD,IAAI,CAAC8H,IAAI,IAAI,IAAI,CAAC;QAC3C;MACF;MAGA,IAAIxI,UAAU,CAACA,UAAU,CAACmM,KAAK,CAAC;QAAE1O,IAAI,EAAE8B;MAAO,CAAC,CAAC,EAAE;QACjDS,UAAU,CAACoM,WAAW,CAACF,OAAO,CAAC;MACjC,CAAC,MAAM,IAAI1D,IAAI,CAAC5L,MAAM,KAAK,CAAC,EAAE;QAC5BoD,UAAU,CAACqM,MAAM,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,MAAMC,IAAI,GAAG9D,IAAI,CAAC5L,MAAM,KAAK,CAAC,GAAG4L,IAAI,CAAC,CAAC,CAAC,GAAGpM,kBAAkB,CAACoM,IAAI,CAAC;QACnE,IAAIxI,UAAU,CAACA,UAAU,CAACuM,cAAc,CAAC;UAAE/D,IAAI,EAAEjJ;QAAO,CAAC,CAAC,EAAE;UAC1DS,UAAU,CAACoM,WAAW,CAACE,IAAI,CAAC;QAC9B,CAAC,MAAM;UACLtM,UAAU,CAACoM,WAAW,CAAC/Q,mBAAmB,CAACiR,IAAI,CAAC,CAAC;QACnD;MACF;IACF;EACF;AACF;AAACE,OAAA,CAAAC,OAAA,GAAA3K,KAAA;AA5/BKA,KAAK,CA+DFvD,OAAO,GAAG,CAAC,GAAGpF,mBAAmB,EAAE,GAAGC,mBAAmB,CAAC;AA/D7D0I,KAAK,CAqEFqJ,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC;AAy7B1B;EAG7CrJ,KAAK,CAAC4K,SAAS,CAACC,cAAc,GAAG,SAASA,cAAcA,CACtDC,GAAqC,EACrC7H,OAAwB,EACxBC,OAAwB,EACxB/H,KAAc,EACd;IACA,IAAI2P,GAAG,CAAC7H,OAAO,CAAC,EAAE;MAChB6H,GAAG,CAAC5H,OAAO,CAAC,GAAG/H,KAAK;MACpB2P,GAAG,CAAC7H,OAAO,CAAC,GAAG,IAAI;IACrB;EACF,CAAC;EAcDjD,KAAK,CAAC4K,SAAS,CAAC9D,QAAQ,GAAG,UAEzBtM,IAAS,EACT8M,IAAS,EACTvJ,KAAS,EACT;IACA,IAAA+I,cAAQ,EAACtM,IAAI,EAAE8M,IAAI,EAAE,IAAI,EAAEvJ,KAAK,EAAE,IAAI,CAAChB,IAAI,CAAC;EAC9C,CAAC;EAMDiD,KAAK,CAAC4K,SAAS,CAACG,YAAY,GAAG,SAASA,YAAYA,CAClDzP,IAAY,EACZqG,CAAS,EACT;IACA,IAAI/F,EAAE,GAAGN,IAAI;IACb,IAAIqG,CAAC,GAAG,CAAC,EAAE/F,EAAE,IAAI+F,CAAC;IAClB,OAAO,IAAI/F,EAAE,EAAE;EACjB,CAAC;EAIDoE,KAAK,CAAC4K,SAAS,CAACI,OAAO,GAAG,SAASA,OAAOA,CAExCxQ,IAAY,EACZmH,CAAoB,EACpBsJ,mBAAoC,EACpC;IACA,IAAI3S,YAAY,CAACkC,IAAI,CAAC,EAAE;MACtB,MAAMiE,OAAO,GAAG,IAAI,CAACC,UAAU,CAAClE,IAAI,CAACc,IAAI,CAAC;MAC1C,IAAImD,OAAO,YAAPA,OAAO,CAAE8D,QAAQ,IAAI9D,OAAO,CAAC1B,IAAI,CAACmO,aAAa,CAAC,OAAO,CAAC,EAAE;QAC5D,OAAO1Q,IAAI;MACb;IACF;IAEA,IAAI5C,iBAAiB,CAAC4C,IAAI,CAAC,EAAE;MAC3B,OAAOA,IAAI;IACb;IAEA,IAAIlC,YAAY,CAACkC,IAAI,EAAE;MAAEc,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;MAC7C,OAAO9D,cAAc,CACnBiC,gBAAgB,CACdA,gBAAgB,CACdA,gBAAgB,CAAC9B,UAAU,CAAC,OAAO,CAAC,EAAEA,UAAU,CAAC,WAAW,CAAC,CAAC,EAC9DA,UAAU,CAAC,OAAO,CACpB,CAAC,EACDA,UAAU,CAAC,MAAM,CACnB,CAAC,EACD,CAAC6C,IAAI,CACP,CAAC;IACH;IAEA,IAAI2Q,UAAU;IACd,MAAMC,IAAI,GAAG,CAAC5Q,IAAI,CAAC;IACnB,IAAImH,CAAC,KAAK,IAAI,EAAE;MAEdwJ,UAAU,GAAG,mBAAmB;IAClC,CAAC,MAAM,IAAI,OAAOxJ,CAAC,KAAK,QAAQ,EAAE;MAChCyJ,IAAI,CAAClQ,IAAI,CAACxB,cAAc,CAACiI,CAAC,CAAC,CAAC;MAG5BwJ,UAAU,GAAG,eAAe;IAE9B,CAAC,MAAM;MAELA,UAAU,GAAG,SAAS;IACxB;IAEA,IAAIF,mBAAmB,EAAE;MACvBG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACtO,IAAI,CAAC8F,GAAG,CAACyI,SAAS,CAACH,UAAU,CAAC,CAAC;MACjDA,UAAU,GAAG,gBAAgB;IAC/B;IAGA,OAAO3T,cAAc,CAAC,IAAI,CAACuF,IAAI,CAAC8F,GAAG,CAACyI,SAAS,CAACH,UAAU,CAAC,EAAEC,IAAI,CAAC;EAClE,CAAC;EAMDpL,KAAK,CAAC4K,SAAS,CAACW,oBAAoB,GAAG,SAASA,oBAAoBA,CAClE,GAAGC,KAAe,EACO;IACzB,MAAMjH,GAAG,GAAGlI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAE/B,KAAK,MAAMqG,IAAI,IAAI6I,KAAK,EAAE;MACxB,IAAIrP,KAAY,GAAG,IAAI;MACvB,GAAG;QACD,KAAK,MAAMb,IAAI,IAAIe,MAAM,CAACyC,IAAI,CAAC3C,KAAK,CAACK,QAAQ,CAAC,EAAE;UAC9C,MAAMiC,OAAO,GAAGtC,KAAK,CAACK,QAAQ,CAAClB,IAAI,CAAC;UACpC,IAAImD,OAAO,CAACkE,IAAI,KAAKA,IAAI,EAAE4B,GAAG,CAACjJ,IAAI,CAAC,GAAGmD,OAAO;QAChD;QACAtC,KAAK,GAAGA,KAAK,CAACsB,MAAM;MACtB,CAAC,QAAQtB,KAAK;IAChB;IAEA,OAAOoI,GAAG;EACZ,CAAC;EAEDlI,MAAM,CAACwE,gBAAgB,CAACb,KAAK,CAAC4K,SAAS,EAAE;IACvCa,WAAW,EAAE;MACX1K,YAAY,EAAE,IAAI;MAClBD,UAAU,EAAE,IAAI;MAChB7D,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAACU,MAAM;MACzB;IACF,CAAC;IACDoF,GAAG,EAAE;MACH9B,YAAY,EAAE,IAAI;MAClBD,UAAU,EAAE,IAAI;MAChB7D,GAAGA,CAAA,EAAc;QACf,OAAO,IAAI,CAACF,IAAI,CAAC8F,GAAG;MACtB;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}