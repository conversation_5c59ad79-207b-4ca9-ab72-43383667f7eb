{"version": 3, "sources": ["../../../src/api/getProjectDevelopmentCertificate.ts"], "sourcesContent": ["import { fetchAsync } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\nexport async function getProjectDevelopmentCertificateAsync(\n  easProjectId: string,\n  csrPEM: string\n): Promise<string> {\n  const response = await fetchAsync(\n    `projects/${encodeURIComponent(easProjectId)}/development-certificates`,\n    {\n      method: 'POST',\n      body: JSON.stringify({\n        csrPEM,\n      }),\n    }\n  );\n  if (!response.ok) {\n    throw new CommandError('API', `Unexpected error from Expo servers: ${response.statusText}.`);\n  }\n  return await response.text();\n}\n"], "names": ["getProjectDevelopmentCertificateAsync", "easProjectId", "csrPEM", "response", "fetchAsync", "encodeURIComponent", "method", "body", "JSON", "stringify", "ok", "CommandError", "statusText", "text"], "mappings": ";;;;+BAGsBA;;;eAAAA;;;wBAHK;wBACE;AAEtB,eAAeA,sCACpBC,YAAoB,EACpBC,MAAc;IAEd,MAAMC,WAAW,MAAMC,IAAAA,kBAAU,EAC/B,CAAC,SAAS,EAAEC,mBAAmBJ,cAAc,yBAAyB,CAAC,EACvE;QACEK,QAAQ;QACRC,MAAMC,KAAKC,SAAS,CAAC;YACnBP;QACF;IACF;IAEF,IAAI,CAACC,SAASO,EAAE,EAAE;QAChB,MAAM,IAAIC,oBAAY,CAAC,OAAO,CAAC,oCAAoC,EAAER,SAASS,UAAU,CAAC,CAAC,CAAC;IAC7F;IACA,OAAO,MAAMT,SAASU,IAAI;AAC5B"}