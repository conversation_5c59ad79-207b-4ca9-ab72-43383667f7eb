"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let UsersService = class UsersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async findAll(role) {
        const where = role ? { role } : {};
        return this.prisma.user.findMany({
            where,
            select: {
                id: true,
                phone: true,
                email: true,
                role: true,
                createdAt: true,
                updatedAt: true,
                clientProfile: {
                    select: {
                        id: true,
                        fullName: true,
                    },
                },
                driverProfile: {
                    select: {
                        id: true,
                        isVerified: true,
                        isOnline: true,
                        vehicle: true,
                    },
                },
                partnerStore: {
                    select: {
                        id: true,
                        name: true,
                        isOpen: true,
                    },
                },
            },
        });
    }
    async findOne(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                phone: true,
                email: true,
                role: true,
                createdAt: true,
                updatedAt: true,
                clientProfile: {
                    select: {
                        id: true,
                        fullName: true,
                    },
                },
                driverProfile: {
                    select: {
                        id: true,
                        isVerified: true,
                        isOnline: true,
                        currentLocation: true,
                        vehicle: true,
                        wallet: true,
                        documents: true,
                    },
                },
                partnerStore: {
                    select: {
                        id: true,
                        name: true,
                        address: true,
                        isOpen: true,
                        products: {
                            take: 10,
                            select: {
                                id: true,
                                name: true,
                                price: true,
                                inStock: true,
                            },
                        },
                    },
                },
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        return user;
    }
    async updateProfile(userId, updateData, currentUserId, currentUserRole) {
        if (currentUserRole !== client_1.UserRole.ADMIN && currentUserId !== userId) {
            throw new common_1.ForbiddenException('Vous ne pouvez modifier que votre propre profil');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        const updatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: {
                phone: updateData.phone,
                email: updateData.email,
            },
            select: {
                id: true,
                phone: true,
                email: true,
                role: true,
                updatedAt: true,
            },
        });
        if (user.role === client_1.UserRole.CLIENT && updateData.fullName) {
            await this.prisma.clientProfile.upsert({
                where: { userId },
                update: { fullName: updateData.fullName },
                create: { userId, fullName: updateData.fullName },
            });
        }
        return updatedUser;
    }
    async createDriverProfile(createDriverDto) {
        const { userId, vehicleType, licensePlate } = createDriverDto;
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user || user.role !== client_1.UserRole.DRIVER) {
            throw new common_1.NotFoundException('Utilisateur driver non trouvé');
        }
        const driverProfile = await this.prisma.driverProfile.create({
            data: {
                userId,
            },
        });
        if (vehicleType && licensePlate) {
            await this.prisma.vehicle.create({
                data: {
                    driverId: driverProfile.id,
                    type: vehicleType,
                    licensePlate,
                },
            });
        }
        await this.prisma.wallet.create({
            data: {
                driverId: driverProfile.id,
            },
        });
        return driverProfile;
    }
    async updateDriverStatus(driverId, isOnline, currentLocation) {
        const driverProfile = await this.prisma.driverProfile.findUnique({
            where: { id: driverId },
        });
        if (!driverProfile) {
            throw new common_1.NotFoundException('Profil driver non trouvé');
        }
        return this.prisma.driverProfile.update({
            where: { id: driverId },
            data: {
                isOnline,
                currentLocation,
            },
        });
    }
    async getDrivers(isOnline) {
        const where = isOnline !== undefined ? { isOnline } : {};
        return this.prisma.driverProfile.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        phone: true,
                        email: true,
                    },
                },
                vehicle: true,
                wallet: true,
            },
        });
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map