{"version": 3, "sources": ["../../../../src/run/ios/launchApp.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport path from 'path';\n\nimport * as XcodeBuild from './XcodeBuild';\nimport { BuildProps } from './XcodeBuild.types';\nimport { getAppDeltaDirectory, installOnDeviceAsync } from './appleDevice/installOnDeviceAsync';\nimport { Log } from '../../log';\nimport { AppleDeviceManager } from '../../start/platforms/ios/AppleDeviceManager';\nimport { launchBinaryOnMacAsync } from '../../start/platforms/ios/devicectl';\nimport { SimulatorLogStreamer } from '../../start/platforms/ios/simctlLogging';\nimport { DevServerManager } from '../../start/server/DevServerManager';\nimport { parsePlistAsync } from '../../utils/plist';\nimport { profile } from '../../utils/profile';\n\ntype BinaryLaunchInfo = {\n  bundleId: string;\n  schemes: string[];\n};\n\n/** Install and launch the app binary on a device. */\nexport async function launchAppAsync(\n  binaryPath: string,\n  manager: DevServerManager,\n  props: Pick<BuildProps, 'isSimulator' | 'device' | 'shouldStartBundler'>,\n  appId?: string\n) {\n  appId ??= (await profile(getLaunchInfoForBinaryAsync)(binaryPath)).bundleId;\n\n  Log.log(chalk.gray`\\u203A Installing ${binaryPath}`);\n  if (!props.isSimulator) {\n    if (props.device.osType === 'macOS') {\n      await launchBinaryOnMacAsync(appId, binaryPath);\n    } else {\n      await profile(installOnDeviceAsync)({\n        bundleIdentifier: appId,\n        bundle: binaryPath,\n        appDeltaDirectory: getAppDeltaDirectory(appId),\n        udid: props.device.udid,\n        deviceName: props.device.name,\n      });\n    }\n\n    return;\n  }\n\n  XcodeBuild.logPrettyItem(chalk`{bold Installing} on ${props.device.name}`);\n\n  const device = await AppleDeviceManager.resolveAsync({ device: props.device });\n  await device.installAppAsync(binaryPath);\n\n  XcodeBuild.logPrettyItem(chalk`{bold Opening} on ${device.name} {dim (${appId})}`);\n\n  if (props.shouldStartBundler) {\n    await SimulatorLogStreamer.getStreamer(device.device, {\n      appId,\n    }).attachAsync();\n  }\n\n  await manager.getDefaultDevServer().openCustomRuntimeAsync(\n    'simulator',\n    {\n      applicationId: appId,\n    },\n    { device }\n  );\n}\n\nexport async function getLaunchInfoForBinaryAsync(binaryPath: string): Promise<BinaryLaunchInfo> {\n  const builtInfoPlistPath = path.join(binaryPath, 'Info.plist');\n  const { CFBundleIdentifier, CFBundleURLTypes } = await parsePlistAsync(builtInfoPlistPath);\n\n  let schemes: string[] = [];\n\n  if (Array.isArray(CFBundleURLTypes)) {\n    schemes =\n      CFBundleURLTypes.reduce<string[]>((acc, urlType: unknown) => {\n        if (\n          urlType &&\n          typeof urlType === 'object' &&\n          'CFBundleURLSchemes' in urlType &&\n          Array.isArray(urlType.CFBundleURLSchemes)\n        ) {\n          return [...acc, ...urlType.CFBundleURLSchemes];\n        }\n        return acc;\n      }, []) ?? [];\n  }\n\n  return { bundleId: CFBundleIdentifier, schemes };\n}\n"], "names": ["getLaunchInfoForBinaryAsync", "launchAppAsync", "binaryPath", "manager", "props", "appId", "profile", "bundleId", "Log", "log", "chalk", "gray", "isSimulator", "device", "osType", "launchBinaryOnMacAsync", "installOnDeviceAsync", "bundleIdentifier", "bundle", "appDeltaDirectory", "getAppDeltaDirectory", "udid", "deviceName", "name", "XcodeBuild", "logPrettyItem", "AppleDeviceManager", "resolveAsync", "installAppAsync", "shouldStartBundler", "SimulatorLogStreamer", "getStreamer", "attachAsync", "getDefaultDevServer", "openCustomRuntimeAsync", "applicationId", "builtInfoPlistPath", "path", "join", "CFBundleIdentifier", "CFBundleURLTypes", "parsePlistAsync", "schemes", "Array", "isArray", "reduce", "acc", "urlType", "CFBundleURLSchemes"], "mappings": ";;;;;;;;;;;IAmEsBA,2BAA2B;eAA3BA;;IA/CAC,cAAc;eAAdA;;;;gEApBJ;;;;;;;gEACD;;;;;;oEAEW;sCAE+B;qBACvC;oCACe;2BACI;+BACF;uBAEL;yBACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB,eAAeA,eACpBC,UAAkB,EAClBC,OAAyB,EACzBC,KAAwE,EACxEC,KAAc;IAEdA,UAAU,AAAC,CAAA,MAAMC,IAAAA,gBAAO,EAACN,6BAA6BE,WAAU,EAAGK,QAAQ;IAE3EC,QAAG,CAACC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,kBAAkB,EAAET,WAAW,CAAC;IACnD,IAAI,CAACE,MAAMQ,WAAW,EAAE;QACtB,IAAIR,MAAMS,MAAM,CAACC,MAAM,KAAK,SAAS;YACnC,MAAMC,IAAAA,iCAAsB,EAACV,OAAOH;QACtC,OAAO;YACL,MAAMI,IAAAA,gBAAO,EAACU,0CAAoB,EAAE;gBAClCC,kBAAkBZ;gBAClBa,QAAQhB;gBACRiB,mBAAmBC,IAAAA,0CAAoB,EAACf;gBACxCgB,MAAMjB,MAAMS,MAAM,CAACQ,IAAI;gBACvBC,YAAYlB,MAAMS,MAAM,CAACU,IAAI;YAC/B;QACF;QAEA;IACF;IAEAC,YAAWC,aAAa,CAACf,IAAAA,gBAAK,CAAA,CAAC,qBAAqB,EAAEN,MAAMS,MAAM,CAACU,IAAI,CAAC,CAAC;IAEzE,MAAMV,SAAS,MAAMa,sCAAkB,CAACC,YAAY,CAAC;QAAEd,QAAQT,MAAMS,MAAM;IAAC;IAC5E,MAAMA,OAAOe,eAAe,CAAC1B;IAE7BsB,YAAWC,aAAa,CAACf,IAAAA,gBAAK,CAAA,CAAC,kBAAkB,EAAEG,OAAOU,IAAI,CAAC,OAAO,EAAElB,MAAM,EAAE,CAAC;IAEjF,IAAID,MAAMyB,kBAAkB,EAAE;QAC5B,MAAMC,mCAAoB,CAACC,WAAW,CAAClB,OAAOA,MAAM,EAAE;YACpDR;QACF,GAAG2B,WAAW;IAChB;IAEA,MAAM7B,QAAQ8B,mBAAmB,GAAGC,sBAAsB,CACxD,aACA;QACEC,eAAe9B;IACjB,GACA;QAAEQ;IAAO;AAEb;AAEO,eAAeb,4BAA4BE,UAAkB;IAClE,MAAMkC,qBAAqBC,eAAI,CAACC,IAAI,CAACpC,YAAY;IACjD,MAAM,EAAEqC,kBAAkB,EAAEC,gBAAgB,EAAE,GAAG,MAAMC,IAAAA,sBAAe,EAACL;IAEvE,IAAIM,UAAoB,EAAE;IAE1B,IAAIC,MAAMC,OAAO,CAACJ,mBAAmB;QACnCE,UACEF,iBAAiBK,MAAM,CAAW,CAACC,KAAKC;YACtC,IACEA,WACA,OAAOA,YAAY,YACnB,wBAAwBA,WACxBJ,MAAMC,OAAO,CAACG,QAAQC,kBAAkB,GACxC;gBACA,OAAO;uBAAIF;uBAAQC,QAAQC,kBAAkB;iBAAC;YAChD;YACA,OAAOF;QACT,GAAG,EAAE,KAAK,EAAE;IAChB;IAEA,OAAO;QAAEvC,UAAUgC;QAAoBG;IAAQ;AACjD"}