{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/LockdowndClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport { Socket } from 'net';\nimport * as tls from 'tls';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport { UsbmuxdPairRecord } from './UsbmuxdClient';\nimport { LockdownProtocolClient } from '../protocol/LockdownProtocol';\n\nconst debug = Debug('expo:apple-device:client:lockdownd');\n\nexport interface DeviceValues {\n  BasebandCertId: number;\n  BasebandKeyHashInformation: {\n    AKeyStatus: number;\n    SKeyHash: Buffer;\n    SKeyStatus: number;\n  };\n  BasebandSerialNumber: Buffer;\n  BasebandVersion: string;\n  BoardId: number;\n  BuildVersion: string;\n  ChipID: number;\n  ConnectionType: 'USB' | 'Network';\n  DeviceClass: string;\n  DeviceColor: string;\n  DeviceName: string;\n  DieID: number;\n  HardwareModel: string;\n  HasSiDP: boolean;\n  PartitionType: string;\n  ProductName: string;\n  ProductType: string;\n  ProductVersion: string;\n  ProductionSOC: boolean;\n  ProtocolVersion: string;\n  TelephonyCapability: boolean;\n  UniqueChipID: number;\n  UniqueDeviceID: string;\n  WiFiAddress: string;\n  [key: string]: any;\n}\n\ninterface LockdowndServiceResponse {\n  Request: 'StartService';\n  Service: string;\n  Port: number;\n  EnableServiceSSL?: boolean; // Only on iOS 13+\n}\n\ninterface LockdowndSessionResponse {\n  Request: 'StartSession';\n  EnableSessionSSL: boolean;\n}\n\ninterface LockdowndAllValuesResponse {\n  Request: 'GetValue';\n  Value: DeviceValues;\n}\n\ninterface LockdowndValueResponse {\n  Request: 'GetValue';\n  Key: string;\n  Value: string;\n}\n\ninterface LockdowndQueryTypeResponse {\n  Request: 'QueryType';\n  Type: string;\n}\n\nfunction isLockdowndServiceResponse(resp: any): resp is LockdowndServiceResponse {\n  return resp.Request === 'StartService' && resp.Service !== undefined && resp.Port !== undefined;\n}\n\nfunction isLockdowndSessionResponse(resp: any): resp is LockdowndSessionResponse {\n  return resp.Request === 'StartSession';\n}\n\nfunction isLockdowndAllValuesResponse(resp: any): resp is LockdowndAllValuesResponse {\n  return resp.Request === 'GetValue' && resp.Value !== undefined;\n}\n\nfunction isLockdowndValueResponse(resp: any): resp is LockdowndValueResponse {\n  return resp.Request === 'GetValue' && resp.Key !== undefined && typeof resp.Value === 'string';\n}\n\nfunction isLockdowndQueryTypeResponse(resp: any): resp is LockdowndQueryTypeResponse {\n  return resp.Request === 'QueryType' && resp.Type !== undefined;\n}\n\nexport class LockdowndClient extends ServiceClient<LockdownProtocolClient> {\n  constructor(public socket: Socket) {\n    super(socket, new LockdownProtocolClient(socket));\n  }\n\n  async startService(name: string) {\n    debug(`startService: ${name}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      Request: 'StartService',\n      Service: name,\n    });\n\n    if (isLockdowndServiceResponse(resp)) {\n      return { port: resp.Port, enableServiceSSL: !!resp.EnableServiceSSL };\n    } else {\n      throw new ResponseError(`Error starting service ${name}`, resp);\n    }\n  }\n\n  async startSession(pairRecord: UsbmuxdPairRecord) {\n    debug(`startSession: ${pairRecord}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      Request: 'StartSession',\n      HostID: pairRecord.HostID,\n      SystemBUID: pairRecord.SystemBUID,\n    });\n\n    if (isLockdowndSessionResponse(resp)) {\n      if (resp.EnableSessionSSL) {\n        this.protocolClient.socket = new tls.TLSSocket(this.protocolClient.socket, {\n          secureContext: tls.createSecureContext({\n            // Avoid using `secureProtocol` fixing the socket to a single TLS version.\n            // Newer Node versions might not support older TLS versions.\n            // By using the default `minVersion` and `maxVersion` options,\n            // The socket will automatically use the appropriate TLS version.\n            // See: https://nodejs.org/api/tls.html#tlscreatesecurecontextoptions\n            cert: pairRecord.RootCertificate,\n            key: pairRecord.RootPrivateKey,\n          }),\n        });\n        debug(`Socket upgraded to TLS connection`);\n      }\n      // TODO: save sessionID for StopSession?\n    } else {\n      throw new ResponseError('Error starting session', resp);\n    }\n  }\n\n  async getAllValues() {\n    debug(`getAllValues`);\n\n    const resp = await this.protocolClient.sendMessage({ Request: 'GetValue' });\n\n    if (isLockdowndAllValuesResponse(resp)) {\n      return resp.Value;\n    } else {\n      throw new ResponseError('Error getting lockdown value', resp);\n    }\n  }\n\n  async getValue(val: string) {\n    debug(`getValue: ${val}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      Request: 'GetValue',\n      Key: val,\n    });\n\n    if (isLockdowndValueResponse(resp)) {\n      return resp.Value;\n    } else {\n      throw new ResponseError('Error getting lockdown value', resp);\n    }\n  }\n\n  async queryType() {\n    debug('queryType');\n\n    const resp = await this.protocolClient.sendMessage({\n      Request: 'QueryType',\n    });\n\n    if (isLockdowndQueryTypeResponse(resp)) {\n      return resp.Type;\n    } else {\n      throw new ResponseError('Error getting lockdown query type', resp);\n    }\n  }\n\n  async doHandshake(pairRecord: UsbmuxdPairRecord) {\n    debug('doHandshake');\n\n    // if (await this.lockdownQueryType() !== 'com.apple.mobile.lockdown') {\n    //   throw new CommandError('Invalid type received from lockdown handshake');\n    // }\n    // await this.getLockdownValue('ProductVersion');\n    // TODO: validate pair and pair\n    await this.startSession(pairRecord);\n  }\n}\n"], "names": ["LockdowndClient", "debug", "Debug", "isLockdowndServiceResponse", "resp", "Request", "Service", "undefined", "Port", "isLockdowndSessionResponse", "isLockdowndAllValuesResponse", "Value", "isLockdowndValueResponse", "Key", "isLockdowndQueryTypeResponse", "Type", "ServiceClient", "constructor", "socket", "LockdownProtocolClient", "startService", "name", "protocolClient", "sendMessage", "port", "enableServiceSSL", "EnableServiceSSL", "ResponseError", "startSession", "pairRecord", "HostID", "SystemBUID", "EnableSessionSSL", "tls", "TLSSocket", "secureContext", "createSecureContext", "cert", "RootCertificate", "key", "RootPrivateKey", "getAllValues", "getValue", "val", "queryType", "doHandshake"], "mappings": "AAAA;;;;;;CAMC;;;;+BA2FYA;;;eAAAA;;;;gEA1FK;;;;;;;iEAEG;;;;;;+BAEwB;kCAEN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvC,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AA8DpB,SAASC,2BAA2BC,IAAS;IAC3C,OAAOA,KAAKC,OAAO,KAAK,kBAAkBD,KAAKE,OAAO,KAAKC,aAAaH,KAAKI,IAAI,KAAKD;AACxF;AAEA,SAASE,2BAA2BL,IAAS;IAC3C,OAAOA,KAAKC,OAAO,KAAK;AAC1B;AAEA,SAASK,6BAA6BN,IAAS;IAC7C,OAAOA,KAAKC,OAAO,KAAK,cAAcD,KAAKO,KAAK,KAAKJ;AACvD;AAEA,SAASK,yBAAyBR,IAAS;IACzC,OAAOA,KAAKC,OAAO,KAAK,cAAcD,KAAKS,GAAG,KAAKN,aAAa,OAAOH,KAAKO,KAAK,KAAK;AACxF;AAEA,SAASG,6BAA6BV,IAAS;IAC7C,OAAOA,KAAKC,OAAO,KAAK,eAAeD,KAAKW,IAAI,KAAKR;AACvD;AAEO,MAAMP,wBAAwBgB,4BAAa;IAChDC,YAAY,AAAOC,MAAc,CAAE;QACjC,KAAK,CAACA,QAAQ,IAAIC,wCAAsB,CAACD,eADxBA,SAAAA;IAEnB;IAEA,MAAME,aAAaC,IAAY,EAAE;QAC/BpB,MAAM,CAAC,cAAc,EAAEoB,MAAM;QAE7B,MAAMjB,OAAO,MAAM,IAAI,CAACkB,cAAc,CAACC,WAAW,CAAC;YACjDlB,SAAS;YACTC,SAASe;QACX;QAEA,IAAIlB,2BAA2BC,OAAO;YACpC,OAAO;gBAAEoB,MAAMpB,KAAKI,IAAI;gBAAEiB,kBAAkB,CAAC,CAACrB,KAAKsB,gBAAgB;YAAC;QACtE,OAAO;YACL,MAAM,IAAIC,4BAAa,CAAC,CAAC,uBAAuB,EAAEN,MAAM,EAAEjB;QAC5D;IACF;IAEA,MAAMwB,aAAaC,UAA6B,EAAE;QAChD5B,MAAM,CAAC,cAAc,EAAE4B,YAAY;QAEnC,MAAMzB,OAAO,MAAM,IAAI,CAACkB,cAAc,CAACC,WAAW,CAAC;YACjDlB,SAAS;YACTyB,QAAQD,WAAWC,MAAM;YACzBC,YAAYF,WAAWE,UAAU;QACnC;QAEA,IAAItB,2BAA2BL,OAAO;YACpC,IAAIA,KAAK4B,gBAAgB,EAAE;gBACzB,IAAI,CAACV,cAAc,CAACJ,MAAM,GAAG,IAAIe,CAAAA,MAAE,EAAEC,SAAS,CAAC,IAAI,CAACZ,cAAc,CAACJ,MAAM,EAAE;oBACzEiB,eAAeF,OAAIG,mBAAmB,CAAC;wBACrC,0EAA0E;wBAC1E,4DAA4D;wBAC5D,8DAA8D;wBAC9D,iEAAiE;wBACjE,qEAAqE;wBACrEC,MAAMR,WAAWS,eAAe;wBAChCC,KAAKV,WAAWW,cAAc;oBAChC;gBACF;gBACAvC,MAAM,CAAC,iCAAiC,CAAC;YAC3C;QACA,wCAAwC;QAC1C,OAAO;YACL,MAAM,IAAI0B,4BAAa,CAAC,0BAA0BvB;QACpD;IACF;IAEA,MAAMqC,eAAe;QACnBxC,MAAM,CAAC,YAAY,CAAC;QAEpB,MAAMG,OAAO,MAAM,IAAI,CAACkB,cAAc,CAACC,WAAW,CAAC;YAAElB,SAAS;QAAW;QAEzE,IAAIK,6BAA6BN,OAAO;YACtC,OAAOA,KAAKO,KAAK;QACnB,OAAO;YACL,MAAM,IAAIgB,4BAAa,CAAC,gCAAgCvB;QAC1D;IACF;IAEA,MAAMsC,SAASC,GAAW,EAAE;QAC1B1C,MAAM,CAAC,UAAU,EAAE0C,KAAK;QAExB,MAAMvC,OAAO,MAAM,IAAI,CAACkB,cAAc,CAACC,WAAW,CAAC;YACjDlB,SAAS;YACTQ,KAAK8B;QACP;QAEA,IAAI/B,yBAAyBR,OAAO;YAClC,OAAOA,KAAKO,KAAK;QACnB,OAAO;YACL,MAAM,IAAIgB,4BAAa,CAAC,gCAAgCvB;QAC1D;IACF;IAEA,MAAMwC,YAAY;QAChB3C,MAAM;QAEN,MAAMG,OAAO,MAAM,IAAI,CAACkB,cAAc,CAACC,WAAW,CAAC;YACjDlB,SAAS;QACX;QAEA,IAAIS,6BAA6BV,OAAO;YACtC,OAAOA,KAAKW,IAAI;QAClB,OAAO;YACL,MAAM,IAAIY,4BAAa,CAAC,qCAAqCvB;QAC/D;IACF;IAEA,MAAMyC,YAAYhB,UAA6B,EAAE;QAC/C5B,MAAM;QAEN,wEAAwE;QACxE,6EAA6E;QAC7E,IAAI;QACJ,iDAAiD;QACjD,+BAA+B;QAC/B,MAAM,IAAI,CAAC2B,YAAY,CAACC;IAC1B;AACF"}