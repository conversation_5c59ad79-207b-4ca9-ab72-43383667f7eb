"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const bcrypt = require("bcryptjs");
const client_1 = require("@prisma/client");
let AuthService = class AuthService {
    prisma;
    jwtService;
    constructor(prisma, jwtService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
    }
    async register(registerDto) {
        const { phone, email, password, role = client_1.UserRole.CLIENT, fullName } = registerDto;
        const existingUser = await this.prisma.user.findFirst({
            where: {
                OR: [
                    phone ? { phone } : {},
                    email ? { email } : {},
                ],
            },
        });
        if (existingUser) {
            throw new common_1.ConflictException('Un utilisateur avec ce téléphone ou email existe déjà');
        }
        const hashedPassword = await bcrypt.hash(password, 12);
        const user = await this.prisma.user.create({
            data: {
                phone,
                email,
                password: hashedPassword,
                role,
            },
        });
        if (role === client_1.UserRole.CLIENT && fullName) {
            await this.prisma.clientProfile.create({
                data: {
                    userId: user.id,
                    fullName,
                },
            });
        }
        const payload = {
            sub: user.id,
            phone: user.phone || undefined,
            email: user.email || undefined,
            role: user.role,
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                phone: user.phone,
                email: user.email,
                role: user.role,
            },
        };
    }
    async login(loginDto) {
        const { identifier, password } = loginDto;
        const user = await this.prisma.user.findFirst({
            where: {
                OR: [
                    { phone: identifier },
                    { email: identifier },
                ],
            },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Identifiants invalides');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Identifiants invalides');
        }
        const payload = {
            sub: user.id,
            phone: user.phone || undefined,
            email: user.email || undefined,
            role: user.role,
        };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user.id,
                phone: user.phone,
                email: user.email,
                role: user.role,
            },
        };
    }
    async validateUser(identifier, password) {
        const user = await this.prisma.user.findFirst({
            where: {
                OR: [
                    { phone: identifier },
                    { email: identifier },
                ],
            },
        });
        if (user && (await bcrypt.compare(password, user.password))) {
            const { password, ...result } = user;
            return result;
        }
        return null;
    }
    async findUserById(id) {
        return this.prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                phone: true,
                email: true,
                role: true,
                createdAt: true,
                updatedAt: true,
            },
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map