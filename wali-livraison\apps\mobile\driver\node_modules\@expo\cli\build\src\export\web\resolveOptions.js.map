{"version": 3, "sources": ["../../../../src/export/web/resolveOptions.ts"], "sourcesContent": ["export type Options = {\n  dev: boolean;\n  clear: boolean;\n};\n\nexport async function resolveOptionsAsync(args: any): Promise<Options> {\n  return {\n    clear: !!args['--clear'],\n    dev: !!args['--dev'],\n  };\n}\n"], "names": ["resolveOptionsAsync", "args", "clear", "dev"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;AAAf,eAAeA,oBAAoBC,IAAS;IACjD,OAAO;QACLC,OAAO,CAAC,CAACD,IAAI,CAAC,UAAU;QACxBE,KAAK,CAAC,CAACF,IAAI,CAAC,QAAQ;IACtB;AACF"}