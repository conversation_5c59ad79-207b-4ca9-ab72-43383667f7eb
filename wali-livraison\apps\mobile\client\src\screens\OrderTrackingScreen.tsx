import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const OrderTrackingScreen = () => {
  const orderStatus = [
    {
      id: '1',
      title: 'Commande confirmée',
      description: 'Votre commande a été confirmée',
      time: '14:30',
      completed: true,
    },
    {
      id: '2',
      title: 'Livreur assigné',
      description: 'Un livreur a été assigné à votre commande',
      time: '14:45',
      completed: true,
    },
    {
      id: '3',
      title: 'En cours de collecte',
      description: 'Le livreur se dirige vers le point de collecte',
      time: '15:00',
      completed: true,
    },
    {
      id: '4',
      title: 'En transit',
      description: 'Votre colis est en route vers la destination',
      time: '15:30',
      completed: false,
      current: true,
    },
    {
      id: '5',
      title: 'Livré',
      description: 'Votre colis a été livré avec succès',
      time: '',
      completed: false,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {/* Order Info */}
        <View style={styles.orderInfoContainer}>
          <View style={styles.orderHeader}>
            <Text style={styles.orderTitle}>Commande #12345</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>En transit</Text>
            </View>
          </View>
          
          <View style={styles.orderDetails}>
            <View style={styles.addressContainer}>
              <Ionicons name="location" size={16} color="#10B981" />
              <Text style={styles.addressLabel}>De:</Text>
              <Text style={styles.addressText}>Cocody, Abidjan</Text>
            </View>
            <View style={styles.addressContainer}>
              <Ionicons name="location" size={16} color="#EF4444" />
              <Text style={styles.addressLabel}>Vers:</Text>
              <Text style={styles.addressText}>Plateau, Abidjan</Text>
            </View>
          </View>
        </View>

        {/* Driver Info */}
        <View style={styles.driverContainer}>
          <View style={styles.driverInfo}>
            <View style={styles.driverAvatar}>
              <Ionicons name="person" size={24} color="#FFFFFF" />
            </View>
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>Kouassi Jean</Text>
              <Text style={styles.driverVehicle}>Moto • ABC 123</Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color="#F59E0B" />
                <Text style={styles.rating}>4.8</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.driverActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="call" size={20} color="#3B82F6" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="chatbubble" size={20} color="#3B82F6" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Map Placeholder */}
        <View style={styles.mapContainer}>
          <View style={styles.mapPlaceholder}>
            <Ionicons name="map" size={48} color="#9CA3AF" />
            <Text style={styles.mapText}>Carte en temps réel</Text>
            <Text style={styles.mapSubtext}>Suivez votre livreur en direct</Text>
          </View>
        </View>

        {/* Order Timeline */}
        <View style={styles.timelineContainer}>
          <Text style={styles.timelineTitle}>Suivi de la commande</Text>
          
          {orderStatus.map((status, index) => (
            <View key={status.id} style={styles.timelineItem}>
              <View style={styles.timelineLeft}>
                <View style={[
                  styles.timelineIcon,
                  status.completed && styles.timelineIconCompleted,
                  status.current && styles.timelineIconCurrent,
                ]}>
                  {status.completed ? (
                    <Ionicons name="checkmark" size={16} color="#FFFFFF" />
                  ) : status.current ? (
                    <View style={styles.currentDot} />
                  ) : (
                    <View style={styles.pendingDot} />
                  )}
                </View>
                {index < orderStatus.length - 1 && (
                  <View style={[
                    styles.timelineLine,
                    status.completed && styles.timelineLineCompleted,
                  ]} />
                )}
              </View>
              
              <View style={styles.timelineContent}>
                <View style={styles.timelineHeader}>
                  <Text style={[
                    styles.timelineItemTitle,
                    status.current && styles.timelineItemTitleCurrent,
                  ]}>
                    {status.title}
                  </Text>
                  {status.time && (
                    <Text style={styles.timelineTime}>{status.time}</Text>
                  )}
                </View>
                <Text style={styles.timelineDescription}>
                  {status.description}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Estimated Delivery */}
        <View style={styles.estimatedContainer}>
          <View style={styles.estimatedInfo}>
            <Ionicons name="time" size={20} color="#3B82F6" />
            <Text style={styles.estimatedText}>Livraison estimée dans</Text>
          </View>
          <Text style={styles.estimatedTime}>15-20 minutes</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  orderInfoContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 8,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  orderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  statusBadge: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  orderDetails: {
    gap: 8,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  addressLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    width: 30,
  },
  addressText: {
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  driverContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  driverAvatar: {
    width: 48,
    height: 48,
    backgroundColor: '#3B82F6',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  driverVehicle: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  driverActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    backgroundColor: '#EBF4FF',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapContainer: {
    backgroundColor: '#FFFFFF',
    marginBottom: 8,
    height: 200,
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
  },
  mapText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginTop: 8,
  },
  mapSubtext: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
  },
  timelineContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 8,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
  },
  timelineIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineIconCompleted: {
    backgroundColor: '#10B981',
  },
  timelineIconCurrent: {
    backgroundColor: '#3B82F6',
  },
  currentDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  pendingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#9CA3AF',
  },
  timelineLine: {
    width: 2,
    height: 32,
    backgroundColor: '#E5E7EB',
    marginTop: 4,
  },
  timelineLineCompleted: {
    backgroundColor: '#10B981',
  },
  timelineContent: {
    flex: 1,
  },
  timelineHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  timelineItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  timelineItemTitleCurrent: {
    color: '#3B82F6',
    fontWeight: '600',
  },
  timelineTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  timelineDescription: {
    fontSize: 12,
    color: '#6B7280',
  },
  estimatedContainer: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  estimatedInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  estimatedText: {
    fontSize: 14,
    color: '#374151',
  },
  estimatedTime: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3B82F6',
  },
});

export default OrderTrackingScreen;
