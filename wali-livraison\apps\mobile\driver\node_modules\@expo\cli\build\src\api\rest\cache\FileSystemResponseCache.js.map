{"version": 3, "sources": ["../../../../../src/api/rest/cache/FileSystemResponseCache.ts"], "sourcesContent": ["import crypto from 'node:crypto';\nimport fs from 'node:fs';\nimport path from 'node:path';\nimport stream, { Readable } from 'node:stream';\nimport { ReadableStream } from 'node:stream/web';\n\nimport type { ResponseCache, ResponseCacheEntry } from './ResponseCache';\nimport { fileExistsAsync } from '../../../utils/dir';\n\ntype FileSystemResponseCacheInfo = ResponseCacheEntry['info'] & {\n  /** The path to the cached body file */\n  bodyPath?: string;\n  /** If there is no response body */\n  empty?: boolean;\n  /** The expiration time, in milliseconds, when the response should be invalidated */\n  expiration?: number;\n};\n\nexport class FileSystemResponseCache implements ResponseCache {\n  /** The absolute path to the directory used to store responses */\n  private cacheDirectory: string;\n  /** Optional auto-expiration for all stored responses */\n  private timeToLive?: number;\n\n  constructor(options: { cacheDirectory: string; ttl?: number }) {\n    this.cacheDirectory = options.cacheDirectory;\n    this.timeToLive = options.ttl;\n  }\n\n  private getFilePaths(cacheKey: string) {\n    // Create a hash of the cache key to use as filename\n    const hash = crypto.createHash('sha256').update(cacheKey).digest('hex');\n    return {\n      info: path.join(this.cacheDirectory, `${hash}-info.json`),\n      body: path.join(this.cacheDirectory, `${hash}-body.bin`),\n    };\n  }\n\n  /** Retrieve the cache response, if any */\n  async get(cacheKey: string): Promise<ResponseCacheEntry | undefined> {\n    const paths = this.getFilePaths(cacheKey);\n\n    if (!(await fileExistsAsync(paths.info))) {\n      return undefined;\n    }\n\n    // Read and parse the info file\n    const infoBuffer = await fs.promises.readFile(paths.info);\n\n    try {\n      const responseInfo: FileSystemResponseCacheInfo = JSON.parse(infoBuffer.toString());\n\n      // Check if the response has expired\n      if (responseInfo.expiration && responseInfo.expiration < Date.now()) {\n        await this.remove(cacheKey);\n        return undefined;\n      }\n\n      // Remove cache-specific data from the response info\n      const { empty, expiration, bodyPath, ...cleanInfo } = responseInfo;\n\n      // Create response body stream\n      let responseBody: ReadableStream;\n      if (empty) {\n        responseBody = Readable.toWeb(Readable.from(Buffer.alloc(0)));\n      } else {\n        const bodyBuffer = await fs.promises.readFile(paths.body);\n        responseBody = Readable.toWeb(Readable.from(bodyBuffer));\n      }\n\n      return {\n        body: responseBody,\n        info: cleanInfo,\n      };\n    } catch {\n      // If file doesn't exist or other errors, return undefined\n      return undefined;\n    }\n  }\n\n  /** Store the response for caching */\n  async set(\n    cacheKey: string,\n    response: ResponseCacheEntry\n  ): Promise<ResponseCacheEntry | undefined> {\n    await fs.promises.mkdir(this.cacheDirectory, { recursive: true });\n    const paths = this.getFilePaths(cacheKey);\n\n    // Create a copy of the response info, to add cache-specific data\n    const responseInfo: FileSystemResponseCacheInfo = { ...response.info };\n\n    // Add expiration time if the \"time to live\" is set\n    if (typeof this.timeToLive === 'number') {\n      responseInfo.expiration = Date.now() + this.timeToLive;\n    }\n\n    try {\n      // Clone the response body stream since we need to read it twice\n      const [forSize, forWrite] = response.body.tee();\n\n      // Check if the body is empty by reading the first stream\n      const reader = forSize.getReader();\n      const { value } = await reader.read();\n      reader.releaseLock();\n\n      if (!value || value.length === 0) {\n        responseInfo.empty = true;\n      } else {\n        // Create write stream and pipe response body to file\n        const writeStream = fs.createWriteStream(paths.body);\n        const nodeStream = Readable.fromWeb(forWrite);\n        nodeStream.pipe(writeStream);\n\n        // Wait for the stream to finish\n        await stream.promises.finished(writeStream);\n\n        responseInfo.bodyPath = paths.body;\n      }\n\n      // Write info to file\n      await fs.promises.writeFile(paths.info, JSON.stringify(responseInfo));\n\n      return await this.get(cacheKey);\n    } catch (error) {\n      // Clean up any partially written files\n      await this.remove(cacheKey);\n      throw error;\n    }\n  }\n\n  /** Remove the response from caching */\n  async remove(cacheKey: string): Promise<void> {\n    const paths = this.getFilePaths(cacheKey);\n    await removeAllAsync(paths.info, paths.body);\n  }\n}\n\nfunction removeAllAsync(...paths: string[]) {\n  return Promise.all(\n    paths.map((path) => fs.promises.rm(path, { recursive: true, force: true }).catch(() => {}))\n  );\n}\n"], "names": ["FileSystemResponseCache", "constructor", "options", "cacheDirectory", "timeToLive", "ttl", "getFilePaths", "cache<PERSON>ey", "hash", "crypto", "createHash", "update", "digest", "info", "path", "join", "body", "get", "paths", "fileExistsAsync", "undefined", "infoBuffer", "fs", "promises", "readFile", "responseInfo", "JSON", "parse", "toString", "expiration", "Date", "now", "remove", "empty", "bodyPath", "cleanInfo", "responseBody", "Readable", "toWeb", "from", "<PERSON><PERSON><PERSON>", "alloc", "bodyBuffer", "set", "response", "mkdir", "recursive", "forSize", "forWrite", "tee", "reader", "<PERSON><PERSON><PERSON><PERSON>", "value", "read", "releaseLock", "length", "writeStream", "createWriteStream", "nodeStream", "fromWeb", "pipe", "stream", "finished", "writeFile", "stringify", "error", "removeAllAsync", "Promise", "all", "map", "rm", "force", "catch"], "mappings": ";;;;+BAkBaA;;;eAAAA;;;;gEAlBM;;;;;;;gEACJ;;;;;;;gEACE;;;;;;;iEACgB;;;;;;qBAID;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzB,MAAMA;IAMXC,YAAYC,OAAiD,CAAE;QAC7D,IAAI,CAACC,cAAc,GAAGD,QAAQC,cAAc;QAC5C,IAAI,CAACC,UAAU,GAAGF,QAAQG,GAAG;IAC/B;IAEQC,aAAaC,QAAgB,EAAE;QACrC,oDAAoD;QACpD,MAAMC,OAAOC,qBAAM,CAACC,UAAU,CAAC,UAAUC,MAAM,CAACJ,UAAUK,MAAM,CAAC;QACjE,OAAO;YACLC,MAAMC,mBAAI,CAACC,IAAI,CAAC,IAAI,CAACZ,cAAc,EAAE,GAAGK,KAAK,UAAU,CAAC;YACxDQ,MAAMF,mBAAI,CAACC,IAAI,CAAC,IAAI,CAACZ,cAAc,EAAE,GAAGK,KAAK,SAAS,CAAC;QACzD;IACF;IAEA,wCAAwC,GACxC,MAAMS,IAAIV,QAAgB,EAA2C;QACnE,MAAMW,QAAQ,IAAI,CAACZ,YAAY,CAACC;QAEhC,IAAI,CAAE,MAAMY,IAAAA,oBAAe,EAACD,MAAML,IAAI,GAAI;YACxC,OAAOO;QACT;QAEA,+BAA+B;QAC/B,MAAMC,aAAa,MAAMC,iBAAE,CAACC,QAAQ,CAACC,QAAQ,CAACN,MAAML,IAAI;QAExD,IAAI;YACF,MAAMY,eAA4CC,KAAKC,KAAK,CAACN,WAAWO,QAAQ;YAEhF,oCAAoC;YACpC,IAAIH,aAAaI,UAAU,IAAIJ,aAAaI,UAAU,GAAGC,KAAKC,GAAG,IAAI;gBACnE,MAAM,IAAI,CAACC,MAAM,CAACzB;gBAClB,OAAOa;YACT;YAEA,oDAAoD;YACpD,MAAM,EAAEa,KAAK,EAAEJ,UAAU,EAAEK,QAAQ,EAAE,GAAGC,WAAW,GAAGV;YAEtD,8BAA8B;YAC9B,IAAIW;YACJ,IAAIH,OAAO;gBACTG,eAAeC,sBAAQ,CAACC,KAAK,CAACD,sBAAQ,CAACE,IAAI,CAACC,OAAOC,KAAK,CAAC;YAC3D,OAAO;gBACL,MAAMC,aAAa,MAAMpB,iBAAE,CAACC,QAAQ,CAACC,QAAQ,CAACN,MAAMF,IAAI;gBACxDoB,eAAeC,sBAAQ,CAACC,KAAK,CAACD,sBAAQ,CAACE,IAAI,CAACG;YAC9C;YAEA,OAAO;gBACL1B,MAAMoB;gBACNvB,MAAMsB;YACR;QACF,EAAE,OAAM;YACN,0DAA0D;YAC1D,OAAOf;QACT;IACF;IAEA,mCAAmC,GACnC,MAAMuB,IACJpC,QAAgB,EAChBqC,QAA4B,EACa;QACzC,MAAMtB,iBAAE,CAACC,QAAQ,CAACsB,KAAK,CAAC,IAAI,CAAC1C,cAAc,EAAE;YAAE2C,WAAW;QAAK;QAC/D,MAAM5B,QAAQ,IAAI,CAACZ,YAAY,CAACC;QAEhC,iEAAiE;QACjE,MAAMkB,eAA4C;YAAE,GAAGmB,SAAS/B,IAAI;QAAC;QAErE,mDAAmD;QACnD,IAAI,OAAO,IAAI,CAACT,UAAU,KAAK,UAAU;YACvCqB,aAAaI,UAAU,GAAGC,KAAKC,GAAG,KAAK,IAAI,CAAC3B,UAAU;QACxD;QAEA,IAAI;YACF,gEAAgE;YAChE,MAAM,CAAC2C,SAASC,SAAS,GAAGJ,SAAS5B,IAAI,CAACiC,GAAG;YAE7C,yDAAyD;YACzD,MAAMC,SAASH,QAAQI,SAAS;YAChC,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAMF,OAAOG,IAAI;YACnCH,OAAOI,WAAW;YAElB,IAAI,CAACF,SAASA,MAAMG,MAAM,KAAK,GAAG;gBAChC9B,aAAaQ,KAAK,GAAG;YACvB,OAAO;gBACL,qDAAqD;gBACrD,MAAMuB,cAAclC,iBAAE,CAACmC,iBAAiB,CAACvC,MAAMF,IAAI;gBACnD,MAAM0C,aAAarB,sBAAQ,CAACsB,OAAO,CAACX;gBACpCU,WAAWE,IAAI,CAACJ;gBAEhB,gCAAgC;gBAChC,MAAMK,qBAAM,CAACtC,QAAQ,CAACuC,QAAQ,CAACN;gBAE/B/B,aAAaS,QAAQ,GAAGhB,MAAMF,IAAI;YACpC;YAEA,qBAAqB;YACrB,MAAMM,iBAAE,CAACC,QAAQ,CAACwC,SAAS,CAAC7C,MAAML,IAAI,EAAEa,KAAKsC,SAAS,CAACvC;YAEvD,OAAO,MAAM,IAAI,CAACR,GAAG,CAACV;QACxB,EAAE,OAAO0D,OAAO;YACd,uCAAuC;YACvC,MAAM,IAAI,CAACjC,MAAM,CAACzB;YAClB,MAAM0D;QACR;IACF;IAEA,qCAAqC,GACrC,MAAMjC,OAAOzB,QAAgB,EAAiB;QAC5C,MAAMW,QAAQ,IAAI,CAACZ,YAAY,CAACC;QAChC,MAAM2D,eAAehD,MAAML,IAAI,EAAEK,MAAMF,IAAI;IAC7C;AACF;AAEA,SAASkD,eAAe,GAAGhD,KAAe;IACxC,OAAOiD,QAAQC,GAAG,CAChBlD,MAAMmD,GAAG,CAAC,CAACvD,OAASQ,iBAAE,CAACC,QAAQ,CAAC+C,EAAE,CAACxD,MAAM;YAAEgC,WAAW;YAAMyB,OAAO;QAAK,GAAGC,KAAK,CAAC,KAAO;AAE5F"}