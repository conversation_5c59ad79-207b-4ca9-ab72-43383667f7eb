{"version": 3, "sources": ["../../../../../src/start/platforms/ios/assertSystemRequirements.ts"], "sourcesContent": ["import { profile } from '../../../utils/profile';\nimport { SimulatorAppPrerequisite } from '../../doctor/apple/SimulatorAppPrerequisite';\nimport { XcodePrerequisite } from '../../doctor/apple/XcodePrerequisite';\nimport { XcrunPrerequisite } from '../../doctor/apple/XcrunPrerequisite';\n\nexport async function assertSystemRequirementsAsync() {\n  // Order is important\n  await profile(\n    XcodePrerequisite.instance.assertAsync.bind(XcodePrerequisite.instance),\n    'XcodePrerequisite'\n  )();\n  await profile(\n    XcrunPrerequisite.instance.assertAsync.bind(XcrunPrerequisite.instance),\n    'XcrunPrerequisite'\n  )();\n  await profile(\n    SimulatorAppPrerequisite.instance.assertAsync.bind(SimulatorAppPrerequisite.instance),\n    'SimulatorAppPrerequisite'\n  )();\n}\n"], "names": ["assertSystemRequirementsAsync", "profile", "XcodePrerequisite", "instance", "assertAsync", "bind", "XcrunPrerequisite", "SimulatorAppPrerequisite"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;yBALE;0CACiB;mCACP;mCACA;AAE3B,eAAeA;IACpB,qBAAqB;IACrB,MAAMC,IAAAA,gBAAO,EACXC,oCAAiB,CAACC,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACH,oCAAiB,CAACC,QAAQ,GACtE;IAEF,MAAMF,IAAAA,gBAAO,EACXK,oCAAiB,CAACH,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACC,oCAAiB,CAACH,QAAQ,GACtE;IAEF,MAAMF,IAAAA,gBAAO,EACXM,kDAAwB,CAACJ,QAAQ,CAACC,WAAW,CAACC,IAAI,CAACE,kDAAwB,CAACJ,QAAQ,GACpF;AAEJ"}