{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/validateDependenciesVersions.ts"], "sourcesContent": ["import { ExpoConfig, PackageJSONConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport npmPackageArg from 'npm-package-arg';\nimport semver from 'semver';\nimport semverRangeSubset from 'semver/ranges/subset';\n\nimport { BundledNativeModules } from './bundledNativeModules';\nimport { getCombinedKnownVersionsAsync } from './getVersionedPackages';\nimport { resolveAllPackageVersionsAsync } from './resolvePackages';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:doctor:dependencies:validate') as typeof console.log;\n\ntype IncorrectDependency = {\n  packageName: string;\n  packageType: 'dependencies' | 'devDependencies';\n  expectedVersionOrRange: string;\n  actualVersion: string;\n};\n\ntype DependenciesToCheck = { known: string[]; unknown: string[] };\n\n/**\n * Print a list of incorrect dependency versions.\n * This only checks dependencies when not running in offline mode.\n *\n * @param projectRoot Expo project root.\n * @param exp Expo project config.\n * @param pkg Project's `package.json`.\n * @param packagesToCheck A list of packages to check, if undefined or empty, all will be checked.\n * @returns `true` if there are no incorrect dependencies.\n */\nexport async function validateDependenciesVersionsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'sdkVersion'>,\n  pkg: PackageJSONConfig,\n  packagesToCheck?: string[]\n): Promise<boolean | null> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Skipping dependency validation in offline mode');\n    return null;\n  }\n\n  const incorrectDeps = await getVersionedDependenciesAsync(projectRoot, exp, pkg, packagesToCheck);\n  return logIncorrectDependencies(incorrectDeps);\n}\n\nfunction logInvalidDependency({\n  packageName,\n  expectedVersionOrRange,\n  actualVersion,\n}: IncorrectDependency) {\n  Log.warn(\n    chalk`  {bold ${packageName}}{cyan @}{red ${actualVersion}} - expected version: {green ${expectedVersionOrRange}}`\n  );\n}\n\nexport function logIncorrectDependencies(incorrectDeps: IncorrectDependency[]) {\n  if (!incorrectDeps.length) {\n    return true;\n  }\n\n  Log.warn(\n    chalk`The following packages should be updated for best compatibility with the installed {bold expo} version:`\n  );\n  incorrectDeps.forEach((dep) => logInvalidDependency(dep));\n\n  Log.warn(\n    'Your project may not work correctly until you install the expected versions of the packages.'\n  );\n\n  return false;\n}\n\n/**\n * Return a list of versioned dependencies for the project SDK version.\n *\n * @param projectRoot Expo project root.\n * @param exp Expo project config.\n * @param pkg Project's `package.json`.\n * @param packagesToCheck A list of packages to check, if undefined or empty, all will be checked.\n * @returns A list of incorrect dependencies.\n */\nexport async function getVersionedDependenciesAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'sdkVersion'>,\n  pkg: PackageJSONConfig,\n  packagesToCheck?: string[]\n): Promise<IncorrectDependency[]> {\n  // This should never happen under normal circumstances since\n  // the CLI is versioned in the `expo` package.\n  assert(exp.sdkVersion, 'SDK Version is missing');\n\n  // Get from both endpoints and combine the known package versions.\n  const combinedKnownPackages = await getCombinedKnownVersionsAsync({\n    projectRoot,\n    sdkVersion: exp.sdkVersion,\n  });\n  // debug(`Known dependencies: %O`, combinedKnownPackages);\n\n  const resolvedDependencies = packagesToCheck?.length\n    ? // Diff the provided packages to ensure we only check against installed packages.\n      getFilteredObject(packagesToCheck, { ...pkg.dependencies, ...pkg.devDependencies })\n    : // If no packages are provided, check against the `package.json` `dependencies` + `devDependencies` object.\n      { ...pkg.dependencies, ...pkg.devDependencies };\n  debug(`Checking dependencies for ${exp.sdkVersion}: %O`, resolvedDependencies);\n\n  // intersection of packages from package.json and bundled native modules\n  const { known: resolvedPackagesToCheck, unknown } = getPackagesToCheck(\n    combinedKnownPackages,\n    resolvedDependencies\n  );\n  debug(`Comparing known versions: %O`, resolvedPackagesToCheck);\n  debug(`Skipping packages that cannot be versioned automatically: %O`, unknown);\n  // read package versions from the file system (node_modules)\n  const packageVersions = await resolveAllPackageVersionsAsync(\n    projectRoot,\n    resolvedPackagesToCheck\n  );\n  debug(`Package versions: %O`, packageVersions);\n  // find incorrect dependencies by comparing the actual package versions with the bundled native module version ranges\n  let incorrectDeps = findIncorrectDependencies(pkg, packageVersions, combinedKnownPackages);\n  debug(`Incorrect dependencies: %O`, incorrectDeps);\n\n  if (pkg?.expo?.install?.exclude) {\n    const packagesToExclude = pkg.expo.install.exclude;\n\n    // Parse the exclude list to ensure we can factor in any specified version ranges\n    const parsedPackagesToExclude = packagesToExclude.reduce(\n      (acc: Record<string, npmPackageArg.Result>, packageName: string) => {\n        const npaResult = npmPackageArg(packageName);\n        if (typeof npaResult.name === 'string') {\n          acc[npaResult.name] = npaResult;\n        } else {\n          acc[packageName] = npaResult;\n        }\n        return acc;\n      },\n      {}\n    );\n\n    const incorrectAndExcludedDeps = incorrectDeps\n      .filter((dep) => {\n        if (parsedPackagesToExclude[dep.packageName]) {\n          const { name, raw, rawSpec, type } = parsedPackagesToExclude[dep.packageName];\n          const suggestedRange = combinedKnownPackages[name];\n\n          // If only the package name itself is specified, then we keep it in the exclude list\n          if (name === raw) {\n            return true;\n          } else if (type === 'version') {\n            return suggestedRange === rawSpec;\n          } else if (type === 'range') {\n            // Fall through exclusions if the suggested range is invalid\n            if (!semver.validRange(suggestedRange)) {\n              debug(\n                `Invalid semver range in combined known packages for package ${name} in expo.install.exclude: %O`,\n                suggestedRange\n              );\n              return false;\n            }\n\n            return semverRangeSubset(suggestedRange, rawSpec);\n          } else {\n            debug(\n              `Unsupported npm package argument type for package ${name} in expo.install.exclude: %O`,\n              type\n            );\n          }\n        }\n\n        return false;\n      })\n      .map((dep) => dep.packageName);\n\n    debug(\n      `Incorrect dependency warnings filtered out by expo.install.exclude: %O`,\n      incorrectAndExcludedDeps\n    );\n    incorrectDeps = incorrectDeps.filter(\n      (dep) => !incorrectAndExcludedDeps.includes(dep.packageName)\n    );\n  }\n\n  return incorrectDeps;\n}\n\nfunction getFilteredObject(keys: string[], object: Record<string, string>) {\n  return keys.reduce<Record<string, string>>((acc, key) => {\n    acc[key] = object[key];\n    return acc;\n  }, {});\n}\n\nfunction getPackagesToCheck(\n  bundledNativeModules: BundledNativeModules,\n  dependencies?: Record<string, string> | null\n): DependenciesToCheck {\n  const dependencyNames = Object.keys(dependencies ?? {});\n  const known: string[] = [];\n  const unknown: string[] = [];\n  for (const dependencyName of dependencyNames) {\n    if (dependencyName in bundledNativeModules) {\n      known.push(dependencyName);\n    } else {\n      unknown.push(dependencyName);\n    }\n  }\n  return { known, unknown };\n}\n\nfunction findIncorrectDependencies(\n  pkg: PackageJSONConfig,\n  packageVersions: Record<string, string>,\n  bundledNativeModules: BundledNativeModules\n): IncorrectDependency[] {\n  const packages = Object.keys(packageVersions);\n  const incorrectDeps: IncorrectDependency[] = [];\n  for (const packageName of packages) {\n    const expectedVersionOrRange = bundledNativeModules[packageName];\n    const actualVersion = packageVersions[packageName];\n    if (isDependencyVersionIncorrect(packageName, actualVersion, expectedVersionOrRange)) {\n      incorrectDeps.push({\n        packageName,\n        packageType: findDependencyType(pkg, packageName),\n        expectedVersionOrRange,\n        actualVersion,\n      });\n    }\n  }\n  return incorrectDeps;\n}\n\nexport function isDependencyVersionIncorrect(\n  packageName: string,\n  actualVersion: string,\n  expectedVersionOrRange?: string\n) {\n  if (!expectedVersionOrRange) {\n    return false;\n  }\n\n  // we never want to go backwards with the expo patch version\n  if (packageName === 'expo') {\n    return semver.ltr(actualVersion, expectedVersionOrRange);\n  }\n\n  // For all other packages, check if the actual version satisfies the expected range\n  const satisfies = semver.satisfies(actualVersion, expectedVersionOrRange, {\n    includePrerelease: true,\n  });\n\n  return !satisfies;\n}\n\nfunction findDependencyType(\n  pkg: PackageJSONConfig,\n  packageName: string\n): IncorrectDependency['packageType'] {\n  if (pkg.devDependencies && packageName in pkg.devDependencies) {\n    return 'devDependencies';\n  }\n\n  return 'dependencies';\n}\n"], "names": ["getVersionedDependenciesAsync", "isDependencyVersionIncorrect", "logIncorrectDependencies", "validateDependenciesVersionsAsync", "debug", "require", "projectRoot", "exp", "pkg", "packagesToCheck", "env", "EXPO_OFFLINE", "Log", "warn", "incorrectDeps", "logInvalidDependency", "packageName", "expectedVersionOrRange", "actualVersion", "chalk", "length", "for<PERSON>ach", "dep", "assert", "sdkVersion", "combinedKnownPackages", "getCombinedKnownVersionsAsync", "resolvedDependencies", "getFilteredObject", "dependencies", "devDependencies", "known", "resolvedPackagesToCheck", "unknown", "getPackagesToCheck", "packageVersions", "resolveAllPackageVersionsAsync", "findIncorrectDependencies", "expo", "install", "exclude", "packagesToExclude", "parsedPackagesToExclude", "reduce", "acc", "npaResult", "npmPackageArg", "name", "incorrectAndExcludedDeps", "filter", "raw", "rawSpec", "type", "<PERSON><PERSON><PERSON><PERSON>", "semver", "validRange", "semverRangeSubset", "map", "includes", "keys", "object", "key", "bundledNativeModules", "dependencyNames", "Object", "dependencyName", "push", "packages", "packageType", "findDependencyType", "ltr", "satisfies", "includePrerelease"], "mappings": ";;;;;;;;;;;IAqFsBA,6BAA6B;eAA7BA;;IAsJNC,4BAA4B;eAA5BA;;IAhLAC,wBAAwB;eAAxBA;;IAzBMC,iCAAiC;eAAjCA;;;;gEAjCH;;;;;;;gEACD;;;;;;;gEACQ;;;;;;;gEACP;;;;;;;gEACW;;;;;;sCAGgB;iCACC;6DAC1B;qBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAqBxB,eAAeF,kCACpBG,WAAmB,EACnBC,GAAmC,EACnCC,GAAsB,EACtBC,eAA0B;IAE1B,IAAIC,QAAG,CAACC,YAAY,EAAE;QACpBC,KAAIC,IAAI,CAAC;QACT,OAAO;IACT;IAEA,MAAMC,gBAAgB,MAAMd,8BAA8BM,aAAaC,KAAKC,KAAKC;IACjF,OAAOP,yBAAyBY;AAClC;AAEA,SAASC,qBAAqB,EAC5BC,WAAW,EACXC,sBAAsB,EACtBC,aAAa,EACO;IACpBN,KAAIC,IAAI,CACNM,IAAAA,gBAAK,CAAA,CAAC,QAAQ,EAAEH,YAAY,cAAc,EAAEE,cAAc,6BAA6B,EAAED,uBAAuB,CAAC,CAAC;AAEtH;AAEO,SAASf,yBAAyBY,aAAoC;IAC3E,IAAI,CAACA,cAAcM,MAAM,EAAE;QACzB,OAAO;IACT;IAEAR,KAAIC,IAAI,CACNM,IAAAA,gBAAK,CAAA,CAAC,uGAAuG,CAAC;IAEhHL,cAAcO,OAAO,CAAC,CAACC,MAAQP,qBAAqBO;IAEpDV,KAAIC,IAAI,CACN;IAGF,OAAO;AACT;AAWO,eAAeb,8BACpBM,WAAmB,EACnBC,GAAmC,EACnCC,GAAsB,EACtBC,eAA0B;QAqCtBD,mBAAAA;IAnCJ,4DAA4D;IAC5D,8CAA8C;IAC9Ce,IAAAA,iBAAM,EAAChB,IAAIiB,UAAU,EAAE;IAEvB,kEAAkE;IAClE,MAAMC,wBAAwB,MAAMC,IAAAA,mDAA6B,EAAC;QAChEpB;QACAkB,YAAYjB,IAAIiB,UAAU;IAC5B;IACA,0DAA0D;IAE1D,MAAMG,uBAAuBlB,CAAAA,mCAAAA,gBAAiBW,MAAM,IAEhDQ,kBAAkBnB,iBAAiB;QAAE,GAAGD,IAAIqB,YAAY;QAAE,GAAGrB,IAAIsB,eAAe;IAAC,KAEjF;QAAE,GAAGtB,IAAIqB,YAAY;QAAE,GAAGrB,IAAIsB,eAAe;IAAC;IAClD1B,MAAM,CAAC,0BAA0B,EAAEG,IAAIiB,UAAU,CAAC,IAAI,CAAC,EAAEG;IAEzD,wEAAwE;IACxE,MAAM,EAAEI,OAAOC,uBAAuB,EAAEC,OAAO,EAAE,GAAGC,mBAClDT,uBACAE;IAEFvB,MAAM,CAAC,4BAA4B,CAAC,EAAE4B;IACtC5B,MAAM,CAAC,4DAA4D,CAAC,EAAE6B;IACtE,4DAA4D;IAC5D,MAAME,kBAAkB,MAAMC,IAAAA,+CAA8B,EAC1D9B,aACA0B;IAEF5B,MAAM,CAAC,oBAAoB,CAAC,EAAE+B;IAC9B,qHAAqH;IACrH,IAAIrB,gBAAgBuB,0BAA0B7B,KAAK2B,iBAAiBV;IACpErB,MAAM,CAAC,0BAA0B,CAAC,EAAEU;IAEpC,IAAIN,wBAAAA,YAAAA,IAAK8B,IAAI,sBAAT9B,oBAAAA,UAAW+B,OAAO,qBAAlB/B,kBAAoBgC,OAAO,EAAE;QAC/B,MAAMC,oBAAoBjC,IAAI8B,IAAI,CAACC,OAAO,CAACC,OAAO;QAElD,iFAAiF;QACjF,MAAME,0BAA0BD,kBAAkBE,MAAM,CACtD,CAACC,KAA2C5B;YAC1C,MAAM6B,YAAYC,IAAAA,wBAAa,EAAC9B;YAChC,IAAI,OAAO6B,UAAUE,IAAI,KAAK,UAAU;gBACtCH,GAAG,CAACC,UAAUE,IAAI,CAAC,GAAGF;YACxB,OAAO;gBACLD,GAAG,CAAC5B,YAAY,GAAG6B;YACrB;YACA,OAAOD;QACT,GACA,CAAC;QAGH,MAAMI,2BAA2BlC,cAC9BmC,MAAM,CAAC,CAAC3B;YACP,IAAIoB,uBAAuB,CAACpB,IAAIN,WAAW,CAAC,EAAE;gBAC5C,MAAM,EAAE+B,IAAI,EAAEG,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGV,uBAAuB,CAACpB,IAAIN,WAAW,CAAC;gBAC7E,MAAMqC,iBAAiB5B,qBAAqB,CAACsB,KAAK;gBAElD,oFAAoF;gBACpF,IAAIA,SAASG,KAAK;oBAChB,OAAO;gBACT,OAAO,IAAIE,SAAS,WAAW;oBAC7B,OAAOC,mBAAmBF;gBAC5B,OAAO,IAAIC,SAAS,SAAS;oBAC3B,4DAA4D;oBAC5D,IAAI,CAACE,iBAAM,CAACC,UAAU,CAACF,iBAAiB;wBACtCjD,MACE,CAAC,4DAA4D,EAAE2C,KAAK,4BAA4B,CAAC,EACjGM;wBAEF,OAAO;oBACT;oBAEA,OAAOG,IAAAA,iBAAiB,EAACH,gBAAgBF;gBAC3C,OAAO;oBACL/C,MACE,CAAC,kDAAkD,EAAE2C,KAAK,4BAA4B,CAAC,EACvFK;gBAEJ;YACF;YAEA,OAAO;QACT,GACCK,GAAG,CAAC,CAACnC,MAAQA,IAAIN,WAAW;QAE/BZ,MACE,CAAC,sEAAsE,CAAC,EACxE4C;QAEFlC,gBAAgBA,cAAcmC,MAAM,CAClC,CAAC3B,MAAQ,CAAC0B,yBAAyBU,QAAQ,CAACpC,IAAIN,WAAW;IAE/D;IAEA,OAAOF;AACT;AAEA,SAASc,kBAAkB+B,IAAc,EAAEC,MAA8B;IACvE,OAAOD,KAAKhB,MAAM,CAAyB,CAACC,KAAKiB;QAC/CjB,GAAG,CAACiB,IAAI,GAAGD,MAAM,CAACC,IAAI;QACtB,OAAOjB;IACT,GAAG,CAAC;AACN;AAEA,SAASV,mBACP4B,oBAA0C,EAC1CjC,YAA4C;IAE5C,MAAMkC,kBAAkBC,OAAOL,IAAI,CAAC9B,gBAAgB,CAAC;IACrD,MAAME,QAAkB,EAAE;IAC1B,MAAME,UAAoB,EAAE;IAC5B,KAAK,MAAMgC,kBAAkBF,gBAAiB;QAC5C,IAAIE,kBAAkBH,sBAAsB;YAC1C/B,MAAMmC,IAAI,CAACD;QACb,OAAO;YACLhC,QAAQiC,IAAI,CAACD;QACf;IACF;IACA,OAAO;QAAElC;QAAOE;IAAQ;AAC1B;AAEA,SAASI,0BACP7B,GAAsB,EACtB2B,eAAuC,EACvC2B,oBAA0C;IAE1C,MAAMK,WAAWH,OAAOL,IAAI,CAACxB;IAC7B,MAAMrB,gBAAuC,EAAE;IAC/C,KAAK,MAAME,eAAemD,SAAU;QAClC,MAAMlD,yBAAyB6C,oBAAoB,CAAC9C,YAAY;QAChE,MAAME,gBAAgBiB,eAAe,CAACnB,YAAY;QAClD,IAAIf,6BAA6Be,aAAaE,eAAeD,yBAAyB;YACpFH,cAAcoD,IAAI,CAAC;gBACjBlD;gBACAoD,aAAaC,mBAAmB7D,KAAKQ;gBACrCC;gBACAC;YACF;QACF;IACF;IACA,OAAOJ;AACT;AAEO,SAASb,6BACde,WAAmB,EACnBE,aAAqB,EACrBD,sBAA+B;IAE/B,IAAI,CAACA,wBAAwB;QAC3B,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAID,gBAAgB,QAAQ;QAC1B,OAAOsC,iBAAM,CAACgB,GAAG,CAACpD,eAAeD;IACnC;IAEA,mFAAmF;IACnF,MAAMsD,YAAYjB,iBAAM,CAACiB,SAAS,CAACrD,eAAeD,wBAAwB;QACxEuD,mBAAmB;IACrB;IAEA,OAAO,CAACD;AACV;AAEA,SAASF,mBACP7D,GAAsB,EACtBQ,WAAmB;IAEnB,IAAIR,IAAIsB,eAAe,IAAId,eAAeR,IAAIsB,eAAe,EAAE;QAC7D,OAAO;IACT;IAEA,OAAO;AACT"}