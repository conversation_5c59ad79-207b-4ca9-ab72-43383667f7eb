{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/formatProjectFilePath.ts"], "sourcesContent": ["import path from 'node:path';\nimport type { StackFrame } from 'stacktrace-parser';\n\nexport type MetroStackFrame = StackFrame & { collapse?: boolean };\n\nexport function formatProjectFilePath(projectRoot: string, file?: string | null): string {\n  if (file == null) {\n    return '<unknown>';\n  }\n  if (file === '<anonymous>') {\n    return file;\n  }\n\n  return path\n    .relative(projectRoot.replace(/\\\\/g, '/'), file.replace(/\\\\/g, '/'))\n    .replace(/\\?.*$/, '');\n}\n\nexport function getStackFormattedLocation(projectRoot: string, frame: MetroStackFrame) {\n  const column = frame.column != null && parseInt(String(frame.column), 10);\n  const location =\n    formatProjectFilePath(projectRoot, frame.file) +\n    (frame.lineNumber != null\n      ? ':' + frame.lineNumber + (column && !isNaN(column) ? ':' + (column + 1) : '')\n      : '');\n\n  return location;\n}\n"], "names": ["formatProjectFilePath", "getStackFormattedLocation", "projectRoot", "file", "path", "relative", "replace", "frame", "column", "parseInt", "String", "location", "lineNumber", "isNaN"], "mappings": ";;;;;;;;;;;IAKgBA,qBAAqB;eAArBA;;IAaAC,yBAAyB;eAAzBA;;;;gEAlBC;;;;;;;;;;;AAKV,SAASD,sBAAsBE,WAAmB,EAAEC,IAAoB;IAC7E,IAAIA,QAAQ,MAAM;QAChB,OAAO;IACT;IACA,IAAIA,SAAS,eAAe;QAC1B,OAAOA;IACT;IAEA,OAAOC,mBAAI,CACRC,QAAQ,CAACH,YAAYI,OAAO,CAAC,OAAO,MAAMH,KAAKG,OAAO,CAAC,OAAO,MAC9DA,OAAO,CAAC,SAAS;AACtB;AAEO,SAASL,0BAA0BC,WAAmB,EAAEK,KAAsB;IACnF,MAAMC,SAASD,MAAMC,MAAM,IAAI,QAAQC,SAASC,OAAOH,MAAMC,MAAM,GAAG;IACtE,MAAMG,WACJX,sBAAsBE,aAAaK,MAAMJ,IAAI,IAC5CI,CAAAA,MAAMK,UAAU,IAAI,OACjB,MAAML,MAAMK,UAAU,GAAIJ,CAAAA,UAAU,CAACK,MAAML,UAAU,MAAOA,CAAAA,SAAS,CAAA,IAAK,EAAC,IAC3E,EAAC;IAEP,OAAOG;AACT"}