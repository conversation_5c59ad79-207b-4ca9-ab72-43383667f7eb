{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@repo/ui": "*", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "@tanstack/react-query": "^5.62.7", "@tanstack/react-query-devtools": "^5.62.7", "axios": "^1.7.9", "socket.io-client": "^4.8.1", "react-hook-form": "^7.54.2", "@hookform/resolvers": "^3.10.0", "zod": "^3.24.1", "lucide-react": "^0.468.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.30.0", "typescript": "5.8.2", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss-animate": "^1.0.7"}}