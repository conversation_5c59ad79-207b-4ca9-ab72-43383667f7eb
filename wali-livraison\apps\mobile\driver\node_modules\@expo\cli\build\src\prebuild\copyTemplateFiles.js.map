{"version": 3, "sources": ["../../../src/prebuild/copyTemplateFiles.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport { MergeResults } from '@expo/config-plugins/build/utils/generateCode';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { copySync } from '../utils/dir';\nimport { mergeGitIgnorePaths } from '../utils/mergeGitIgnorePaths';\n\nconst debug = require('debug')('expo:prebuild:copyTemplateFiles') as typeof console.log;\n\ntype CopyFilesResults = {\n  /** Merge results for the root `.gitignore` file */\n  gitignore: MergeResults | null;\n  /** List of file paths that were copied from the template into the project. */\n  copiedPaths: string[];\n  /** List of file paths that were skipped due to a number of factors. */\n  skippedPaths: string[];\n};\n\n/**\n * Return true if the given platforms all have an internal `.gitignore` file.\n *\n * @param projectRoot\n * @param platforms\n */\nfunction hasAllPlatformSpecificGitIgnores(projectRoot: string, platforms: ModPlatform[]): boolean {\n  return platforms.reduce<boolean>(\n    (p, platform) => p && fs.existsSync(path.join(projectRoot, platform, '.gitignore')),\n    true\n  );\n}\n\n/** Create a custom log message based on the copy file results. */\nexport function createCopyFilesSuccessMessage(\n  platforms: ModPlatform[],\n  { skippedPaths, gitignore }: CopyFilesResults\n): string {\n  const pluralized = platforms.length > 1 ? 'directories' : 'directory';\n  let message = `Created native ${pluralized}`;\n\n  if (skippedPaths.length) {\n    message += chalk.dim(\n      ` | reusing ${skippedPaths.map((path) => chalk.bold(`/${path}`)).join(', ')}`\n    );\n  }\n  if (!gitignore) {\n    // Add no additional message...\n  } else if (!gitignore.didMerge) {\n    message += chalk.dim(` | reusing gitignore`);\n  } else if (gitignore.didMerge && gitignore.didClear) {\n    // This is legacy and for non-standard templates. The Expo template adds gitignores to the platform folders.\n    message += chalk.dim(` | updated gitignore`);\n  }\n  return message;\n}\n\n/** Copy template files into the project and possibly merge the `.gitignore` files.  */\nexport function copyTemplateFiles(\n  projectRoot: string,\n  {\n    templateDirectory,\n    platforms,\n  }: {\n    /** File path to the template directory. */\n    templateDirectory: string;\n    /** List of platforms to copy against. */\n    platforms: ModPlatform[];\n  }\n): CopyFilesResults {\n  const copiedPaths: string[] = [];\n  const skippedPaths: string[] = [];\n\n  platforms.forEach((copyFilePath) => {\n    const projectPath = path.join(projectRoot, copyFilePath);\n    if (fs.existsSync(projectPath)) {\n      skippedPaths.push(copyFilePath);\n    } else {\n      copiedPaths.push(copyFilePath);\n      copySync(path.join(templateDirectory, copyFilePath), projectPath);\n    }\n  });\n\n  const hasPlatformSpecificGitIgnores = hasAllPlatformSpecificGitIgnores(\n    templateDirectory,\n    platforms\n  );\n  debug(`All platforms have an internal gitignore: ${hasPlatformSpecificGitIgnores}`);\n\n  // TODO: Remove gitignore modifications -- maybe move to `npx expo-doctor`\n  const gitignore = hasPlatformSpecificGitIgnores\n    ? null\n    : mergeGitIgnorePaths(\n        path.join(projectRoot, '.gitignore'),\n        path.join(templateDirectory, '.gitignore')\n      );\n\n  return { copiedPaths, skippedPaths, gitignore };\n}\n"], "names": ["copyTemplateFiles", "createCopyFilesSuccessMessage", "debug", "require", "hasAllPlatformSpecificGitIgnores", "projectRoot", "platforms", "reduce", "p", "platform", "fs", "existsSync", "path", "join", "skippedPaths", "gitignore", "pluralized", "length", "message", "chalk", "dim", "map", "bold", "didMerge", "<PERSON><PERSON><PERSON><PERSON>", "templateDirectory", "copiedPaths", "for<PERSON>ach", "copyFilePath", "projectPath", "push", "copySync", "hasPlatformSpecificGitIgnores", "mergeGitIgnorePaths"], "mappings": ";;;;;;;;;;;IA0DgBA,iBAAiB;eAAjBA;;IAxBAC,6BAA6B;eAA7BA;;;;gEAhCE;;;;;;;gEACH;;;;;;;gEACE;;;;;;qBAEQ;qCACW;;;;;;AAEpC,MAAMC,QAAQC,QAAQ,SAAS;AAW/B;;;;;CAKC,GACD,SAASC,iCAAiCC,WAAmB,EAAEC,SAAwB;IACrF,OAAOA,UAAUC,MAAM,CACrB,CAACC,GAAGC,WAAaD,KAAKE,aAAE,CAACC,UAAU,CAACC,eAAI,CAACC,IAAI,CAACR,aAAaI,UAAU,gBACrE;AAEJ;AAGO,SAASR,8BACdK,SAAwB,EACxB,EAAEQ,YAAY,EAAEC,SAAS,EAAoB;IAE7C,MAAMC,aAAaV,UAAUW,MAAM,GAAG,IAAI,gBAAgB;IAC1D,IAAIC,UAAU,CAAC,eAAe,EAAEF,YAAY;IAE5C,IAAIF,aAAaG,MAAM,EAAE;QACvBC,WAAWC,gBAAK,CAACC,GAAG,CAClB,CAAC,WAAW,EAAEN,aAAaO,GAAG,CAAC,CAACT,OAASO,gBAAK,CAACG,IAAI,CAAC,CAAC,CAAC,EAAEV,MAAM,GAAGC,IAAI,CAAC,OAAO;IAEjF;IACA,IAAI,CAACE,WAAW;IACd,+BAA+B;IACjC,OAAO,IAAI,CAACA,UAAUQ,QAAQ,EAAE;QAC9BL,WAAWC,gBAAK,CAACC,GAAG,CAAC,CAAC,oBAAoB,CAAC;IAC7C,OAAO,IAAIL,UAAUQ,QAAQ,IAAIR,UAAUS,QAAQ,EAAE;QACnD,4GAA4G;QAC5GN,WAAWC,gBAAK,CAACC,GAAG,CAAC,CAAC,oBAAoB,CAAC;IAC7C;IACA,OAAOF;AACT;AAGO,SAASlB,kBACdK,WAAmB,EACnB,EACEoB,iBAAiB,EACjBnB,SAAS,EAMV;IAED,MAAMoB,cAAwB,EAAE;IAChC,MAAMZ,eAAyB,EAAE;IAEjCR,UAAUqB,OAAO,CAAC,CAACC;QACjB,MAAMC,cAAcjB,eAAI,CAACC,IAAI,CAACR,aAAauB;QAC3C,IAAIlB,aAAE,CAACC,UAAU,CAACkB,cAAc;YAC9Bf,aAAagB,IAAI,CAACF;QACpB,OAAO;YACLF,YAAYI,IAAI,CAACF;YACjBG,IAAAA,aAAQ,EAACnB,eAAI,CAACC,IAAI,CAACY,mBAAmBG,eAAeC;QACvD;IACF;IAEA,MAAMG,gCAAgC5B,iCACpCqB,mBACAnB;IAEFJ,MAAM,CAAC,0CAA0C,EAAE8B,+BAA+B;IAElF,0EAA0E;IAC1E,MAAMjB,YAAYiB,gCACd,OACAC,IAAAA,wCAAmB,EACjBrB,eAAI,CAACC,IAAI,CAACR,aAAa,eACvBO,eAAI,CAACC,IAAI,CAACY,mBAAmB;IAGnC,OAAO;QAAEC;QAAaZ;QAAcC;IAAU;AAChD"}