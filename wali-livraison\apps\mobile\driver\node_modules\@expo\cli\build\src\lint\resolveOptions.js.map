{"version": 3, "sources": ["../../../src/lint/resolveOptions.ts"], "sourcesContent": ["import { CommandError } from '../utils/errors';\nimport { assertNonBooleanArg } from '../utils/resolveArgs';\nimport { assertUnexpectedVariadicFlags, parseVariadicArguments } from '../utils/variadic';\n\nexport type Options = {\n  ext: string[];\n  config?: string;\n  cache: boolean;\n  fix: boolean;\n  fixType: string[];\n  ignore: boolean;\n  ignorePattern: string[];\n  quiet: boolean;\n  maxWarnings: number;\n};\n\nfunction splitCommaSeparatedList(argName: string, list: any): string[] {\n  if (list == null) return [];\n  assertNonBooleanArg(argName, list);\n\n  if (typeof list === 'string') {\n    return list.split(',').map((item) => item.trim());\n  }\n  if (Array.isArray(list)) {\n    return list.map((item) => splitCommaSeparatedList(argName, item)).flat();\n  }\n  if (typeof list === 'boolean') {\n    throw new CommandError(`Expected a string input for arg ${argName}`);\n  }\n\n  throw new CommandError(\n    `Expected a string or an array for arg ${argName}, but got: ${typeof list}`\n  );\n}\n\nfunction resolveOptions(options: Options): Options {\n  return {\n    ...options,\n    ext: splitCommaSeparatedList('--ext', options.ext),\n    fixType: splitCommaSeparatedList('--fix-type', options.fixType),\n    config: options.config,\n  };\n}\n\nexport async function resolveArgsAsync(\n  argv: string[]\n): Promise<{ variadic: string[]; options: Options; extras: string[] }> {\n  const knownFlags = [\n    '--ext',\n    '--config',\n    '--no-cache',\n    '--fix',\n    '--fix-type',\n    '--no-ignore',\n    '--ignore-pattern',\n    '--quiet',\n    '--max-warnings',\n  ];\n\n  const { variadic, extras, flags } = parseVariadicArguments(argv, [\n    // Just non-boolean flags\n    '--ext',\n    '--config',\n    '--fix-type',\n    '--ignore-pattern',\n    '--max-warnings',\n  ]);\n\n  assertUnexpectedVariadicFlags(knownFlags, { variadic, extras, flags }, 'npx expo lint');\n\n  const config = flags['--config'];\n  assertSingleStringInput('--config', config);\n\n  return {\n    // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']\n    variadic,\n    options: resolveOptions({\n      ext: splitCommaSeparatedList('--ext', flags['--ext']),\n      config: assertSingleStringInput('--config', config),\n      cache: !getBooleanArg('--no-cache', flags['--no-cache']),\n      fix: !!getBooleanArg('--fix', flags['--fix']),\n\n      fixType: splitCommaSeparatedList('--fix-type', flags['--fix-type']),\n      ignore: !getBooleanArg('--no-ignore', flags['--no-ignore']),\n      ignorePattern: splitCommaSeparatedList('--ignore-pattern', flags['--ignore-pattern']),\n      quiet: !!getBooleanArg('--quiet', flags['--quiet']),\n      maxWarnings: Number(flags['--max-warnings']) ?? -1,\n    }),\n    extras,\n  };\n}\n\nfunction assertSingleStringInput(argName: string, arg: any): string | undefined {\n  if (!arg) {\n    return arg;\n  }\n  assertNonBooleanArg(argName, arg);\n  if (Array.isArray(arg) && arg.length > 1) {\n    throw new CommandError(\n      'BAD_ARGS',\n      `Too many values provided for arg ${argName}. Provide only one of: ${arg.join(', ')}`\n    );\n  }\n  return arg as string;\n}\n\nfunction getBooleanArg(argName: string, arg: any): boolean | undefined {\n  if (arg == null) {\n    return undefined;\n  }\n\n  if (typeof arg === 'boolean') {\n    return arg;\n  }\n  if (typeof arg === 'string') {\n    if (arg === 'true' || arg === '1' || arg === '') {\n      return true;\n    }\n    if (arg === 'false' || arg === '0') {\n      return false;\n    }\n  }\n  if (Array.isArray(arg)) {\n    if (arg.length > 1) {\n      throw new CommandError('BAD_ARGS', `Too many ${argName} args provided.`);\n    } else {\n      return getBooleanArg(argName, arg[0]);\n    }\n  }\n  throw new CommandError('BAD_ARGS', `Expected a boolean input for arg ${argName}`);\n}\n"], "names": ["resolveArgsAsync", "splitCommaSeparatedList", "argName", "list", "assertNonBooleanArg", "split", "map", "item", "trim", "Array", "isArray", "flat", "CommandError", "resolveOptions", "options", "ext", "fixType", "config", "argv", "knownFlags", "variadic", "extras", "flags", "parseVariadicArguments", "assertUnexpectedVariadicFlags", "assertSingleStringInput", "cache", "getBooleanArg", "fix", "ignore", "ignorePattern", "quiet", "maxWarnings", "Number", "arg", "length", "join", "undefined"], "mappings": ";;;;+BA4CsBA;;;eAAAA;;;wBA5CO;6BACO;0BACkC;AActE,SAASC,wBAAwBC,OAAe,EAAEC,IAAS;IACzD,IAAIA,QAAQ,MAAM,OAAO,EAAE;IAC3BC,IAAAA,gCAAmB,EAACF,SAASC;IAE7B,IAAI,OAAOA,SAAS,UAAU;QAC5B,OAAOA,KAAKE,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,OAASA,KAAKC,IAAI;IAChD;IACA,IAAIC,MAAMC,OAAO,CAACP,OAAO;QACvB,OAAOA,KAAKG,GAAG,CAAC,CAACC,OAASN,wBAAwBC,SAASK,OAAOI,IAAI;IACxE;IACA,IAAI,OAAOR,SAAS,WAAW;QAC7B,MAAM,IAAIS,oBAAY,CAAC,CAAC,gCAAgC,EAAEV,SAAS;IACrE;IAEA,MAAM,IAAIU,oBAAY,CACpB,CAAC,sCAAsC,EAAEV,QAAQ,WAAW,EAAE,OAAOC,MAAM;AAE/E;AAEA,SAASU,eAAeC,OAAgB;IACtC,OAAO;QACL,GAAGA,OAAO;QACVC,KAAKd,wBAAwB,SAASa,QAAQC,GAAG;QACjDC,SAASf,wBAAwB,cAAca,QAAQE,OAAO;QAC9DC,QAAQH,QAAQG,MAAM;IACxB;AACF;AAEO,eAAejB,iBACpBkB,IAAc;IAEd,MAAMC,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGC,IAAAA,gCAAsB,EAACL,MAAM;QAC/D,yBAAyB;QACzB;QACA;QACA;QACA;QACA;KACD;IAEDM,IAAAA,uCAA6B,EAACL,YAAY;QAAEC;QAAUC;QAAQC;IAAM,GAAG;IAEvE,MAAML,SAASK,KAAK,CAAC,WAAW;IAChCG,wBAAwB,YAAYR;IAEpC,OAAO;QACL,uFAAuF;QACvFG;QACAN,SAASD,eAAe;YACtBE,KAAKd,wBAAwB,SAASqB,KAAK,CAAC,QAAQ;YACpDL,QAAQQ,wBAAwB,YAAYR;YAC5CS,OAAO,CAACC,cAAc,cAAcL,KAAK,CAAC,aAAa;YACvDM,KAAK,CAAC,CAACD,cAAc,SAASL,KAAK,CAAC,QAAQ;YAE5CN,SAASf,wBAAwB,cAAcqB,KAAK,CAAC,aAAa;YAClEO,QAAQ,CAACF,cAAc,eAAeL,KAAK,CAAC,cAAc;YAC1DQ,eAAe7B,wBAAwB,oBAAoBqB,KAAK,CAAC,mBAAmB;YACpFS,OAAO,CAAC,CAACJ,cAAc,WAAWL,KAAK,CAAC,UAAU;YAClDU,aAAaC,OAAOX,KAAK,CAAC,iBAAiB,KAAK,CAAC;QACnD;QACAD;IACF;AACF;AAEA,SAASI,wBAAwBvB,OAAe,EAAEgC,GAAQ;IACxD,IAAI,CAACA,KAAK;QACR,OAAOA;IACT;IACA9B,IAAAA,gCAAmB,EAACF,SAASgC;IAC7B,IAAIzB,MAAMC,OAAO,CAACwB,QAAQA,IAAIC,MAAM,GAAG,GAAG;QACxC,MAAM,IAAIvB,oBAAY,CACpB,YACA,CAAC,iCAAiC,EAAEV,QAAQ,uBAAuB,EAAEgC,IAAIE,IAAI,CAAC,OAAO;IAEzF;IACA,OAAOF;AACT;AAEA,SAASP,cAAczB,OAAe,EAAEgC,GAAQ;IAC9C,IAAIA,OAAO,MAAM;QACf,OAAOG;IACT;IAEA,IAAI,OAAOH,QAAQ,WAAW;QAC5B,OAAOA;IACT;IACA,IAAI,OAAOA,QAAQ,UAAU;QAC3B,IAAIA,QAAQ,UAAUA,QAAQ,OAAOA,QAAQ,IAAI;YAC/C,OAAO;QACT;QACA,IAAIA,QAAQ,WAAWA,QAAQ,KAAK;YAClC,OAAO;QACT;IACF;IACA,IAAIzB,MAAMC,OAAO,CAACwB,MAAM;QACtB,IAAIA,IAAIC,MAAM,GAAG,GAAG;YAClB,MAAM,IAAIvB,oBAAY,CAAC,YAAY,CAAC,SAAS,EAAEV,QAAQ,eAAe,CAAC;QACzE,OAAO;YACL,OAAOyB,cAAczB,SAASgC,GAAG,CAAC,EAAE;QACtC;IACF;IACA,MAAM,IAAItB,oBAAY,CAAC,YAAY,CAAC,iCAAiC,EAAEV,SAAS;AAClF"}