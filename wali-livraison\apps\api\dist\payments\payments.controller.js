"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payments_service_1 = require("./payments.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
let PaymentsController = class PaymentsController {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async processPayment(createPaymentDto, user) {
        return this.paymentsService.processPayment(createPaymentDto, user.id);
    }
    async getPaymentStatus(transactionId, body) {
        return this.paymentsService.getPaymentStatus(transactionId, body.paymentMethod);
    }
    async stripeWebhook(body) {
        console.log('Stripe webhook received:', body);
        return { received: true };
    }
    async orangeCallback(body) {
        console.log('Orange Money callback received:', body);
        return { received: true };
    }
    async mtnCallback(body) {
        console.log('MTN Money callback received:', body);
        return { received: true };
    }
    async waveCallback(body) {
        console.log('Wave callback received:', body);
        return { received: true };
    }
};
exports.PaymentsController = PaymentsController;
__decorate([
    (0, common_1.Post)('process'),
    (0, swagger_1.ApiOperation)({ summary: 'Traiter un paiement' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Paiement traité avec succès' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erreur de paiement' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "processPayment", null);
__decorate([
    (0, common_1.Get)('status/:transactionId'),
    (0, swagger_1.ApiOperation)({ summary: 'Vérifier le statut d\'un paiement' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statut du paiement' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Transaction non trouvée' }),
    __param(0, (0, common_1.Param)('transactionId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getPaymentStatus", null);
__decorate([
    (0, common_1.Post)('stripe/webhook'),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook Stripe' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "stripeWebhook", null);
__decorate([
    (0, common_1.Post)('orange/callback'),
    (0, swagger_1.ApiOperation)({ summary: 'Callback Orange Money' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "orangeCallback", null);
__decorate([
    (0, common_1.Post)('mtn/callback'),
    (0, swagger_1.ApiOperation)({ summary: 'Callback MTN Money' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "mtnCallback", null);
__decorate([
    (0, common_1.Post)('wave/callback'),
    (0, swagger_1.ApiOperation)({ summary: 'Callback Wave' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "waveCallback", null);
exports.PaymentsController = PaymentsController = __decorate([
    (0, swagger_1.ApiTags)('Payments'),
    (0, common_1.Controller)('payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [payments_service_1.PaymentsService])
], PaymentsController);
//# sourceMappingURL=payments.controller.js.map