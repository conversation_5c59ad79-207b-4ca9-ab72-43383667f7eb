{"version": 3, "sources": ["../../../../src/export/web/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../../utils/args';\nimport { logCmdError } from '../../utils/errors';\n\nexport const expoExportWeb: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clear': <PERSON><PERSON><PERSON>,\n      '--dev': <PERSON><PERSON>an,\n      // Aliases\n      '-h': '--help',\n      '-c': '--clear',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `(Deprecated) Bundle the static files of the web app with Webpack for hosting on a web server`,\n      chalk`npx expo export:web {dim <dir>}`,\n      [\n        chalk`<dir>                         Directory of the Expo project. {dim Default: Current working directory}`,\n        `--dev                         Bundle in development mode`,\n        `-c, --clear                   Clear the bundler cache`,\n        `-h, --help                    Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const projectRoot = getProjectRoot(args);\n  const { resolveOptionsAsync } = await import('./resolveOptions.js');\n  const options = await resolveOptionsAsync(args).catch(logCmdError);\n\n  const { exportWebAsync } = await import('./exportWebAsync.js');\n  return exportWebAsync(projectRoot, options).catch(logCmdError);\n};\n"], "names": ["expoExportWeb", "argv", "args", "assertArgs", "Boolean", "printHelp", "chalk", "join", "projectRoot", "getProjectRoot", "resolveOptionsAsync", "options", "catch", "logCmdError", "exportWebAsync"], "mappings": ";;;;;+BAOaA;;;eAAAA;;;;gEANK;;;;;;sBAGoC;wBAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,gBAAyB,OAAOC;IAC3C,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,WAAWA;QACX,SAASA;QACT,UAAU;QACV,MAAM;QACN,MAAM;IACR,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,4FAA4F,CAAC,EAC9FC,IAAAA,gBAAK,CAAA,CAAC,+BAA+B,CAAC,EACtC;YACEA,IAAAA,gBAAK,CAAA,CAAC,qGAAqG,CAAC;YAC5G,CAAC,wDAAwD,CAAC;YAC1D,CAAC,qDAAqD,CAAC;YACvD,CAAC,wCAAwC,CAAC;SAC3C,CAACC,IAAI,CAAC;IAEX;IAEA,MAAMC,cAAcC,IAAAA,oBAAc,EAACP;IACnC,MAAM,EAAEQ,mBAAmB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAC7C,MAAMC,UAAU,MAAMD,oBAAoBR,MAAMU,KAAK,CAACC,mBAAW;IAEjE,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,mEAAA,QAAO;IACxC,OAAOA,eAAeN,aAAaG,SAASC,KAAK,CAACC,mBAAW;AAC/D"}