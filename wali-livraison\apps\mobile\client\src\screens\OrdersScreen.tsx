import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

interface Order {
  id: string;
  type: 'PARCEL' | 'FOOD' | 'GROCERY';
  status: 'PENDING' | 'ACCEPTED' | 'IN_TRANSIT' | 'DELIVERED' | 'CANCELLED';
  createdAt: string;
  totalAmount: number;
  deliveryAddress: string;
}

const OrdersScreen = () => {
  const navigation = useNavigation();
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'active' | 'completed'>('all');

  // Mock data - à remplacer par des données réelles
  const [orders] = useState<Order[]>([
    {
      id: '1',
      type: 'PA<PERSON><PERSON>',
      status: 'IN_TRANSIT',
      createdAt: '2024-01-15T10:30:00Z',
      totalAmount: 2500,
      deliveryAddress: 'Cocody, Abidjan',
    },
    {
      id: '2',
      type: 'FOOD',
      status: 'DELIVERED',
      createdAt: '2024-01-14T18:45:00Z',
      totalAmount: 4500,
      deliveryAddress: 'Plateau, Abidjan',
    },
  ]);

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'PENDING':
        return '#F59E0B';
      case 'ACCEPTED':
        return '#3B82F6';
      case 'IN_TRANSIT':
        return '#8B5CF6';
      case 'DELIVERED':
        return '#10B981';
      case 'CANCELLED':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'PENDING':
        return 'En attente';
      case 'ACCEPTED':
        return 'Acceptée';
      case 'IN_TRANSIT':
        return 'En cours';
      case 'DELIVERED':
        return 'Livrée';
      case 'CANCELLED':
        return 'Annulée';
      default:
        return status;
    }
  };

  const getTypeIcon = (type: Order['type']) => {
    switch (type) {
      case 'PARCEL':
        return 'cube-outline';
      case 'FOOD':
        return 'restaurant-outline';
      case 'GROCERY':
        return 'basket-outline';
      default:
        return 'help-outline';
    }
  };

  const getTypeText = (type: Order['type']) => {
    switch (type) {
      case 'PARCEL':
        return 'Colis';
      case 'FOOD':
        return 'Repas';
      case 'GROCERY':
        return 'Courses';
      default:
        return type;
    }
  };

  const filteredOrders = orders.filter(order => {
    if (activeTab === 'active') {
      return ['PENDING', 'ACCEPTED', 'IN_TRANSIT'].includes(order.status);
    }
    if (activeTab === 'completed') {
      return ['DELIVERED', 'CANCELLED'].includes(order.status);
    }
    return true;
  });

  const onRefresh = () => {
    setRefreshing(true);
    // TODO: Implémenter le rafraîchissement des données
    setTimeout(() => setRefreshing(false), 1000);
  };

  const renderOrder = ({ item }: { item: Order }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => navigation.navigate('OrderTracking' as never, { orderId: item.id })}
    >
      <View style={styles.orderHeader}>
        <View style={styles.orderTypeContainer}>
          <View style={[styles.typeIcon, { backgroundColor: getStatusColor(item.status) + '20' }]}>
            <Ionicons name={getTypeIcon(item.type) as any} size={20} color={getStatusColor(item.status)} />
          </View>
          <View>
            <Text style={styles.orderType}>{getTypeText(item.type)}</Text>
            <Text style={styles.orderId}>#{item.id}</Text>
          </View>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.orderDetails}>
        <View style={styles.addressContainer}>
          <Ionicons name="location-outline" size={16} color="#6B7280" />
          <Text style={styles.address}>{item.deliveryAddress}</Text>
        </View>
        <Text style={styles.date}>
          {new Date(item.createdAt).toLocaleDateString('fr-FR', {
            day: 'numeric',
            month: 'short',
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
      </View>

      <View style={styles.orderFooter}>
        <Text style={styles.amount}>{item.totalAmount.toLocaleString()} FCFA</Text>
        <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Mes Commandes</Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
            Toutes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'active' && styles.activeTab]}
          onPress={() => setActiveTab('active')}
        >
          <Text style={[styles.tabText, activeTab === 'active' && styles.activeTabText]}>
            En cours
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'completed' && styles.activeTab]}
          onPress={() => setActiveTab('completed')}
        >
          <Text style={[styles.tabText, activeTab === 'completed' && styles.activeTabText]}>
            Terminées
          </Text>
        </TouchableOpacity>
      </View>

      {/* Orders List */}
      {filteredOrders.length > 0 ? (
        <FlatList
          data={filteredOrders}
          renderItem={renderOrder}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        />
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="receipt-outline" size={64} color="#9CA3AF" />
          <Text style={styles.emptyStateTitle}>Aucune commande</Text>
          <Text style={styles.emptyStateText}>
            {activeTab === 'all' 
              ? 'Vous n\'avez pas encore passé de commande'
              : activeTab === 'active'
              ? 'Aucune commande en cours'
              : 'Aucune commande terminée'
            }
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  activeTab: {
    backgroundColor: '#3B82F6',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  listContainer: {
    padding: 20,
  },
  orderCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  orderType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  orderId: {
    fontSize: 12,
    color: '#6B7280',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  orderDetails: {
    marginBottom: 12,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  address: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
    flex: 1,
  },
  date: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default OrdersScreen;
