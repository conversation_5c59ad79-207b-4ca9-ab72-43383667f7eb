{"version": 3, "sources": ["../../../src/logout/index.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoLogout: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Log out of an Expo account`,\n      `npx expo logout`,\n      // options\n      `-h, --help    Usage info`\n    );\n  }\n\n  const { logoutAsync } = await import('../api/user/user.js');\n  return logoutAsync().catch(logCmdError);\n};\n"], "names": ["expoLogout", "argv", "args", "assertArgs", "Boolean", "printHelp", "logoutAsync", "catch", "logCmdError"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;sBAHyB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,aAAsB,OAAOC;IACxC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,0BAA0B,CAAC,EAC5B,CAAC,eAAe,CAAC,EACjB,UAAU;QACV,CAAC,wBAAwB,CAAC;IAE9B;IAEA,MAAM,EAAEC,WAAW,EAAE,GAAG,MAAM,mEAAA,QAAO;IACrC,OAAOA,cAAcC,KAAK,CAACC,mBAAW;AACxC"}