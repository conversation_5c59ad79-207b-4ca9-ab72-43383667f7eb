import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderType, OrderStatus, UserRole } from '@prisma/client';

export interface CreateOrderDto {
  orderType: OrderType;
  pickupAddress?: any; // Pour les colis
  deliveryAddress: any;
  storeId?: string; // Pour les commandes e-commerce
  items?: Array<{
    productId: string;
    quantity: number;
  }>;
  price: number;
  deliveryFee: number;
}

export interface UpdateOrderStatusDto {
  status: OrderStatus;
  proofOfDelivery?: string;
}

@Injectable()
export class OrdersService {
  constructor(private prisma: PrismaService) {}

  async create(createOrderDto: CreateOrderDto, clientId: string) {
    const { orderType, pickupAddress, deliveryAddress, storeId, items, price, deliveryFee } = createOrderDto;
    
    const totalAmount = price + deliveryFee;

    // Créer la commande
    const order = await this.prisma.order.create({
      data: {
        orderType,
        clientId,
        pickupAddress,
        deliveryAddress,
        storeId,
        price,
        deliveryFee,
        totalAmount,
      },
    });

    // Ajouter les items si c'est une commande e-commerce
    if (items && items.length > 0) {
      await Promise.all(
        items.map(item =>
          this.prisma.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              quantity: item.quantity,
              price: 0, // À calculer depuis le produit
            },
          })
        )
      );
    }

    return this.findOne(order.id);
  }

  async findAll(
    clientId?: string, 
    driverId?: string, 
    storeId?: string, 
    status?: OrderStatus,
    orderType?: OrderType
  ) {
    const where: any = {};
    
    if (clientId) where.clientId = clientId;
    if (driverId) where.driverId = driverId;
    if (storeId) where.storeId = storeId;
    if (status) where.status = status;
    if (orderType) where.orderType = orderType;

    return this.prisma.order.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            phone: true,
            clientProfile: {
              select: { fullName: true },
            },
          },
        },
        driver: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                phone: true,
              },
            },
            vehicle: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                price: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string) {
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            phone: true,
            clientProfile: {
              select: { fullName: true },
            },
          },
        },
        driver: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                phone: true,
              },
            },
            vehicle: true,
            currentLocation: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                description: true,
                price: true,
                imageUrl: true,
              },
            },
          },
        },
        transaction: true,
        rating: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Commande non trouvée');
    }

    return order;
  }

  async assignDriver(orderId: string, driverId: string) {
    // Vérifier que la commande existe et est en attente
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new NotFoundException('Commande non trouvée');
    }

    if (order.status !== OrderStatus.PENDING) {
      throw new ForbiddenException('Cette commande ne peut plus être assignée');
    }

    // Vérifier que le driver existe et est disponible
    const driver = await this.prisma.driverProfile.findUnique({
      where: { id: driverId },
    });

    if (!driver || !driver.isOnline) {
      throw new ForbiddenException('Driver non disponible');
    }

    return this.prisma.order.update({
      where: { id: orderId },
      data: {
        driverId,
        status: OrderStatus.ACCEPTED,
      },
    });
  }

  async updateStatus(
    orderId: string, 
    updateStatusDto: UpdateOrderStatusDto, 
    userId: string, 
    userRole: UserRole
  ) {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: {
        driver: {
          select: {
            userId: true,
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundException('Commande non trouvée');
    }

    // Vérifier les permissions
    const canUpdate = 
      userRole === UserRole.ADMIN ||
      (userRole === UserRole.DRIVER && order.driver?.userId === userId) ||
      (userRole === UserRole.PARTNER && order.storeId);

    if (!canUpdate) {
      throw new ForbiddenException('Vous n\'avez pas l\'autorisation de modifier cette commande');
    }

    return this.prisma.order.update({
      where: { id: orderId },
      data: {
        status: updateStatusDto.status,
        proofOfDelivery: updateStatusDto.proofOfDelivery,
      },
    });
  }

  async getAvailableOrders(driverLocation?: any) {
    // Récupérer les commandes en attente
    return this.prisma.order.findMany({
      where: {
        status: OrderStatus.PENDING,
        driverId: null,
      },
      include: {
        client: {
          select: {
            phone: true,
            clientProfile: {
              select: { fullName: true },
            },
          },
        },
        store: {
          select: {
            name: true,
            address: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
  }

  async acceptOrder(orderId: string, driverId: string) {
    return this.assignDriver(orderId, driverId);
  }
}
