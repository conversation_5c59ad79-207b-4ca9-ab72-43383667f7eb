{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/UsbmuxdClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket, connect } from 'net';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport { CommandError } from '../../../../utils/errors';\nimport { parsePlistBuffer } from '../../../../utils/plist';\nimport { UsbmuxProtocolClient } from '../protocol/UsbmuxProtocol';\n\nconst debug = Debug('expo:apple-device:client:usbmuxd');\n\nexport interface UsbmuxdDeviceProperties {\n  /** @example 'USB' */\n  ConnectionType: 'USB' | 'Network';\n  /** @example 7 */\n  DeviceID: number;\n  /** @example 339738624 */\n  LocationID?: number;\n  /** @example '00008101-001964A22629003A' */\n  SerialNumber: string;\n  /**\n   * Only available for USB connection.\n   * @example 480000000\n   */\n  ConnectionSpeed?: number;\n  /**\n   * Only available for USB connection.\n   * @example 4776\n   */\n  ProductID?: number;\n  /**\n   * Only available for USB connection.\n   * @example '00008101-001964A22629003A'\n   */\n  UDID?: string;\n  /**\n   * Only available for USB connection.\n   * @example '00008101001964A22629003A'\n   */\n  USBSerialNumber?: string;\n  /**\n   * Only available for Network connection.\n   * @example '08:c7:29:05:f2:30@fe80::ac7:29ff:fe05:f230-supportsRP._apple-mobdev2._tcp.local.'\n   */\n  EscapedFullServiceName?: string;\n  /**\n   * Only available for Network connection.\n   * @example 5\n   */\n  InterfaceIndex?: number;\n  /**\n   * Only available for Network connection.\n   */\n  NetworkAddress?: Buffer;\n}\n\nexport interface UsbmuxdDevice {\n  /** @example 7 */\n  DeviceID: number;\n  MessageType: 'Attached'; // TODO: what else?\n  Properties: UsbmuxdDeviceProperties;\n}\n\nexport interface UsbmuxdConnectResponse {\n  MessageType: 'Result';\n  Number: number;\n}\n\nexport interface UsbmuxdDeviceResponse {\n  DeviceList: UsbmuxdDevice[];\n}\n\nexport interface UsbmuxdPairRecordResponse {\n  PairRecordData: Buffer;\n}\n\nexport interface UsbmuxdPairRecord {\n  DeviceCertificate: Buffer;\n  EscrowBag: Buffer;\n  HostCertificate: Buffer;\n  HostID: string;\n  HostPrivateKey: Buffer;\n  RootCertificate: Buffer;\n  RootPrivateKey: Buffer;\n  SystemBUID: string;\n  WiFiMACAddress: string;\n}\n\nfunction isUsbmuxdConnectResponse(resp: any): resp is UsbmuxdConnectResponse {\n  return resp.MessageType === 'Result' && resp.Number !== undefined;\n}\n\nfunction isUsbmuxdDeviceResponse(resp: any): resp is UsbmuxdDeviceResponse {\n  return resp.DeviceList !== undefined;\n}\n\nfunction isUsbmuxdPairRecordResponse(resp: any): resp is UsbmuxdPairRecordResponse {\n  return resp.PairRecordData !== undefined;\n}\n\nexport class UsbmuxdClient extends ServiceClient<UsbmuxProtocolClient> {\n  constructor(public socket: Socket) {\n    super(socket, new UsbmuxProtocolClient(socket));\n  }\n\n  static connectUsbmuxdSocket(): Socket {\n    debug('connectUsbmuxdSocket');\n    if (process.platform === 'win32') {\n      return connect({ port: 27015, host: 'localhost' });\n    } else {\n      return connect({ path: '/var/run/usbmuxd' });\n    }\n  }\n\n  async connect(device: Pick<UsbmuxdDevice, 'DeviceID'>, port: number): Promise<Socket> {\n    debug(`connect: ${device.DeviceID} on port ${port}`);\n    debug(`connect:device: %O`, device);\n\n    const response = await this.protocolClient.sendMessage({\n      messageType: 'Connect',\n      extraFields: {\n        DeviceID: device.DeviceID,\n        PortNumber: htons(port),\n      },\n    });\n    debug(`connect:device:response: %O`, response);\n\n    if (isUsbmuxdConnectResponse(response) && response.Number === 0) {\n      return this.protocolClient.socket;\n    } else {\n      throw new ResponseError(\n        `There was an error connecting to the USB connected device (id: ${device.DeviceID}, port: ${port})`,\n        response\n      );\n    }\n  }\n\n  async getDevices(): Promise<UsbmuxdDevice[]> {\n    debug('getDevices');\n\n    const resp = await this.protocolClient.sendMessage({\n      messageType: 'ListDevices',\n    });\n\n    if (isUsbmuxdDeviceResponse(resp)) {\n      return resp.DeviceList;\n    } else {\n      throw new ResponseError('Invalid response from getDevices', resp);\n    }\n  }\n\n  async getDevice(udid?: string): Promise<UsbmuxdDevice> {\n    debug(`getDevice ${udid ? 'udid: ' + udid : ''}`);\n    const devices = await this.getDevices();\n\n    if (!devices.length) {\n      throw new CommandError('APPLE_DEVICE_USBMUXD', 'No devices found');\n    }\n\n    if (!udid) {\n      return devices[0];\n    }\n\n    for (const device of devices) {\n      if (device.Properties && device.Properties.SerialNumber === udid) {\n        return device;\n      }\n    }\n\n    throw new CommandError('APPLE_DEVICE_USBMUXD', `No device found (udid: ${udid})`);\n  }\n\n  async readPairRecord(udid: string): Promise<UsbmuxdPairRecord> {\n    debug(`readPairRecord: ${udid}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      messageType: 'ReadPairRecord',\n      extraFields: { PairRecordID: udid },\n    });\n\n    if (isUsbmuxdPairRecordResponse(resp)) {\n      // the pair record can be created as a binary plist\n      const BPLIST_MAGIC = Buffer.from('bplist00');\n      if (BPLIST_MAGIC.compare(resp.PairRecordData, 0, 8) === 0) {\n        debug('Binary plist pair record detected.');\n        const pairRecords = parsePlistBuffer(resp.PairRecordData);\n        return Array.isArray(pairRecords) ? pairRecords[0] : pairRecords;\n      } else {\n        // TODO: use parsePlistBuffer\n        return plist.parse(resp.PairRecordData.toString()) as any; // TODO: type guard\n      }\n    } else {\n      throw new ResponseError(\n        `There was an error reading pair record for device (udid: ${udid})`,\n        resp\n      );\n    }\n  }\n}\n\nfunction htons(n: number): number {\n  return ((n & 0xff) << 8) | ((n >> 8) & 0xff);\n}\n"], "names": ["UsbmuxdClient", "debug", "Debug", "isUsbmuxdConnectResponse", "resp", "MessageType", "Number", "undefined", "isUsbmuxdDeviceResponse", "DeviceList", "isUsbmuxdPairRecordResponse", "PairRecordData", "ServiceClient", "constructor", "socket", "UsbmuxProtocolClient", "connectUsbmuxdSocket", "process", "platform", "connect", "port", "host", "path", "device", "DeviceID", "response", "protocolClient", "sendMessage", "messageType", "extraFields", "PortNumber", "htons", "ResponseError", "getDevices", "getDevice", "udid", "devices", "length", "CommandError", "Properties", "SerialNumber", "readPairRecord", "PairRecordID", "BPLIST_MAGIC", "<PERSON><PERSON><PERSON>", "from", "compare", "pairRecords", "parsePlist<PERSON><PERSON><PERSON>", "Array", "isArray", "plist", "parse", "toString", "n"], "mappings": "AAAA;;;;;;CAMC;;;;+BAqGYA;;;eAAAA;;;;gEApGK;;;;;;;gEACA;;;;;;;yBACc;;;;;;+BAEa;wBAChB;wBACI;gCACI;;;;;;AAErC,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AA+EpB,SAASC,yBAAyBC,IAAS;IACzC,OAAOA,KAAKC,WAAW,KAAK,YAAYD,KAAKE,MAAM,KAAKC;AAC1D;AAEA,SAASC,wBAAwBJ,IAAS;IACxC,OAAOA,KAAKK,UAAU,KAAKF;AAC7B;AAEA,SAASG,4BAA4BN,IAAS;IAC5C,OAAOA,KAAKO,cAAc,KAAKJ;AACjC;AAEO,MAAMP,sBAAsBY,4BAAa;IAC9CC,YAAY,AAAOC,MAAc,CAAE;QACjC,KAAK,CAACA,QAAQ,IAAIC,oCAAoB,CAACD,eADtBA,SAAAA;IAEnB;IAEA,OAAOE,uBAA+B;QACpCf,MAAM;QACN,IAAIgB,QAAQC,QAAQ,KAAK,SAAS;YAChC,OAAOC,IAAAA,cAAO,EAAC;gBAAEC,MAAM;gBAAOC,MAAM;YAAY;QAClD,OAAO;YACL,OAAOF,IAAAA,cAAO,EAAC;gBAAEG,MAAM;YAAmB;QAC5C;IACF;IAEA,MAAMH,QAAQI,MAAuC,EAAEH,IAAY,EAAmB;QACpFnB,MAAM,CAAC,SAAS,EAAEsB,OAAOC,QAAQ,CAAC,SAAS,EAAEJ,MAAM;QACnDnB,MAAM,CAAC,kBAAkB,CAAC,EAAEsB;QAE5B,MAAME,WAAW,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,aAAa;YACbC,aAAa;gBACXL,UAAUD,OAAOC,QAAQ;gBACzBM,YAAYC,MAAMX;YACpB;QACF;QACAnB,MAAM,CAAC,2BAA2B,CAAC,EAAEwB;QAErC,IAAItB,yBAAyBsB,aAAaA,SAASnB,MAAM,KAAK,GAAG;YAC/D,OAAO,IAAI,CAACoB,cAAc,CAACZ,MAAM;QACnC,OAAO;YACL,MAAM,IAAIkB,4BAAa,CACrB,CAAC,+DAA+D,EAAET,OAAOC,QAAQ,CAAC,QAAQ,EAAEJ,KAAK,CAAC,CAAC,EACnGK;QAEJ;IACF;IAEA,MAAMQ,aAAuC;QAC3ChC,MAAM;QAEN,MAAMG,OAAO,MAAM,IAAI,CAACsB,cAAc,CAACC,WAAW,CAAC;YACjDC,aAAa;QACf;QAEA,IAAIpB,wBAAwBJ,OAAO;YACjC,OAAOA,KAAKK,UAAU;QACxB,OAAO;YACL,MAAM,IAAIuB,4BAAa,CAAC,oCAAoC5B;QAC9D;IACF;IAEA,MAAM8B,UAAUC,IAAa,EAA0B;QACrDlC,MAAM,CAAC,UAAU,EAAEkC,OAAO,WAAWA,OAAO,IAAI;QAChD,MAAMC,UAAU,MAAM,IAAI,CAACH,UAAU;QAErC,IAAI,CAACG,QAAQC,MAAM,EAAE;YACnB,MAAM,IAAIC,oBAAY,CAAC,wBAAwB;QACjD;QAEA,IAAI,CAACH,MAAM;YACT,OAAOC,OAAO,CAAC,EAAE;QACnB;QAEA,KAAK,MAAMb,UAAUa,QAAS;YAC5B,IAAIb,OAAOgB,UAAU,IAAIhB,OAAOgB,UAAU,CAACC,YAAY,KAAKL,MAAM;gBAChE,OAAOZ;YACT;QACF;QAEA,MAAM,IAAIe,oBAAY,CAAC,wBAAwB,CAAC,uBAAuB,EAAEH,KAAK,CAAC,CAAC;IAClF;IAEA,MAAMM,eAAeN,IAAY,EAA8B;QAC7DlC,MAAM,CAAC,gBAAgB,EAAEkC,MAAM;QAE/B,MAAM/B,OAAO,MAAM,IAAI,CAACsB,cAAc,CAACC,WAAW,CAAC;YACjDC,aAAa;YACbC,aAAa;gBAAEa,cAAcP;YAAK;QACpC;QAEA,IAAIzB,4BAA4BN,OAAO;YACrC,mDAAmD;YACnD,MAAMuC,eAAeC,OAAOC,IAAI,CAAC;YACjC,IAAIF,aAAaG,OAAO,CAAC1C,KAAKO,cAAc,EAAE,GAAG,OAAO,GAAG;gBACzDV,MAAM;gBACN,MAAM8C,cAAcC,IAAAA,wBAAgB,EAAC5C,KAAKO,cAAc;gBACxD,OAAOsC,MAAMC,OAAO,CAACH,eAAeA,WAAW,CAAC,EAAE,GAAGA;YACvD,OAAO;gBACL,6BAA6B;gBAC7B,OAAOI,gBAAK,CAACC,KAAK,CAAChD,KAAKO,cAAc,CAAC0C,QAAQ,KAAY,mBAAmB;YAChF;QACF,OAAO;YACL,MAAM,IAAIrB,4BAAa,CACrB,CAAC,yDAAyD,EAAEG,KAAK,CAAC,CAAC,EACnE/B;QAEJ;IACF;AACF;AAEA,SAAS2B,MAAMuB,CAAS;IACtB,OAAO,AAAEA,CAAAA,IAAI,IAAG,KAAM,IAAM,AAACA,KAAK,IAAK;AACzC"}