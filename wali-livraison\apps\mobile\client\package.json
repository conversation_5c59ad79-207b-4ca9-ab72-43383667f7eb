{"name": "wali-client-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~52.0.0", "expo-status-bar": "~2.0.0", "react": "18.3.1", "react-native": "0.76.5", "react-native-safe-area-context": "4.12.0", "react-native-screens": "4.2.0", "@react-navigation/native": "^7.0.15", "@react-navigation/stack": "^7.1.1", "@react-navigation/bottom-tabs": "^7.1.0", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "expo-location": "~18.0.4", "expo-camera": "~16.0.8", "expo-image-picker": "~16.0.3", "react-native-maps": "1.18.0", "axios": "^1.7.9", "@tanstack/react-query": "^5.62.7", "react-hook-form": "^7.54.2", "expo-constants": "~17.0.3", "expo-linking": "~7.0.3", "expo-notifications": "~0.29.9", "expo-secure-store": "~14.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@types/react-native": "~0.73.0", "typescript": "^5.3.3"}, "private": true}