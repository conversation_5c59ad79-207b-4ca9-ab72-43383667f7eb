import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { UserRole } from '@prisma/client';
export interface RegisterDto {
    phone?: string;
    email?: string;
    password: string;
    role?: UserRole;
    fullName?: string;
}
export interface LoginDto {
    identifier: string;
    password: string;
}
export interface JwtPayload {
    sub: string;
    phone?: string;
    email?: string;
    role: UserRole;
}
export declare class AuthService {
    private prisma;
    private jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    register(registerDto: RegisterDto): Promise<{
        access_token: string;
        user: {
            id: string;
            phone: string | null;
            email: string | null;
            role: import(".prisma/client").$Enums.UserRole;
        };
    }>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: {
            id: string;
            phone: string | null;
            email: string | null;
            role: import(".prisma/client").$Enums.UserRole;
        };
    }>;
    validateUser(identifier: string, password: string): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
    findUserById(id: string): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
    } | null>;
}
