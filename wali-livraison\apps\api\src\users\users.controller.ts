import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Param, 
  Body, 
  Query, 
  UseGuards 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { UsersService, UpdateUserDto, CreateDriverProfileDto } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UserRole } from '@prisma/client';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Récupérer tous les utilisateurs (Admin seulement)' })
  @ApiQuery({ name: 'role', required: false, enum: UserRole })
  @ApiResponse({ status: 200, description: 'Liste des utilisateurs' })
  async findAll(@Query('role') role?: UserRole) {
    return this.usersService.findAll(role);
  }

  @Get('drivers')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.PARTNER)
  @ApiOperation({ summary: 'Récupérer tous les drivers' })
  @ApiQuery({ name: 'isOnline', required: false, type: Boolean })
  @ApiResponse({ status: 200, description: 'Liste des drivers' })
  async getDrivers(@Query('isOnline') isOnline?: boolean) {
    return this.usersService.getDrivers(isOnline);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Récupérer un utilisateur par ID' })
  @ApiResponse({ status: 200, description: 'Utilisateur trouvé' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async findOne(@Param('id') id: string) {
    return this.usersService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Mettre à jour le profil d\'un utilisateur' })
  @ApiResponse({ status: 200, description: 'Profil mis à jour' })
  @ApiResponse({ status: 403, description: 'Accès interdit' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async updateProfile(
    @Param('id') id: string,
    @Body() updateData: UpdateUserDto,
    @CurrentUser() currentUser: any,
  ) {
    return this.usersService.updateProfile(
      id, 
      updateData, 
      currentUser.id, 
      currentUser.role
    );
  }

  @Post('drivers/profile')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Créer un profil driver (Admin seulement)' })
  @ApiResponse({ status: 201, description: 'Profil driver créé' })
  @ApiResponse({ status: 404, description: 'Utilisateur non trouvé' })
  async createDriverProfile(@Body() createDriverDto: CreateDriverProfileDto) {
    return this.usersService.createDriverProfile(createDriverDto);
  }

  @Put('drivers/:id/status')
  @ApiOperation({ summary: 'Mettre à jour le statut d\'un driver' })
  @ApiResponse({ status: 200, description: 'Statut mis à jour' })
  @ApiResponse({ status: 404, description: 'Driver non trouvé' })
  async updateDriverStatus(
    @Param('id') driverId: string,
    @Body() statusData: { isOnline: boolean; currentLocation?: any },
  ) {
    return this.usersService.updateDriverStatus(
      driverId, 
      statusData.isOnline, 
      statusData.currentLocation
    );
  }
}
