{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveOptions.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { isSimulatorDevice, resolveDeviceAsync } from './resolveDevice';\nimport { resolveNativeSchemePropsAsync } from './resolveNativeScheme';\nimport { resolveXcodeProject } from './resolveXcodeProject';\nimport { isOSType } from '../../../start/platforms/ios/simctl';\nimport { resolveBuildCacheProvider } from '../../../utils/build-cache-providers';\nimport { profile } from '../../../utils/profile';\nimport { resolveBundlerPropsAsync } from '../../resolveBundlerProps';\nimport { BuildProps, Options } from '../XcodeBuild.types';\n\n/** Resolve arguments for the `run:ios` command. */\nexport async function resolveOptionsAsync(\n  projectRoot: string,\n  options: Options\n): Promise<BuildProps> {\n  const xcodeProject = resolveXcodeProject(projectRoot);\n\n  const bundlerProps = await resolveBundlerPropsAsync(projectRoot, options);\n\n  // Resolve the scheme before the device so we can filter devices based on\n  // whichever scheme is selected (i.e. don't present TV devices if the scheme cannot be run on a TV).\n  const { osType, name: scheme } = await resolveNativeSchemePropsAsync(\n    projectRoot,\n    options,\n    xcodeProject\n  );\n\n  // Use the configuration or `Debug` if none is provided.\n  const configuration = options.configuration || 'Debug';\n\n  // Resolve the device based on the provided device id or prompt\n  // from a list of devices (connected or simulated) that are filtered by the scheme.\n  const device = await profile(resolveDeviceAsync)(options.device, {\n    // It's unclear if there's any value to asserting that we haven't hardcoded the os type in the CLI.\n    osType: isOSType(osType) ? osType : undefined,\n    xcodeProject,\n    scheme,\n    configuration,\n  });\n\n  const isSimulator = isSimulatorDevice(device);\n\n  const projectConfig = getConfig(projectRoot);\n  const buildCacheProvider = await resolveBuildCacheProvider(\n    projectConfig.exp.experiments?.buildCacheProvider ??\n      projectConfig.exp.experiments?.remoteBuildCache?.provider,\n    projectRoot\n  );\n\n  // This optimization skips resetting the Metro cache needlessly.\n  // The cache is reset in `../node_modules/react-native/scripts/react-native-xcode.sh` when the\n  // project is running in Debug and built onto a physical device. It seems that this is done because\n  // the script is run from Xcode and unaware of the CLI instance.\n  const shouldSkipInitialBundling = configuration === 'Debug' && !isSimulator;\n\n  return {\n    ...bundlerProps,\n    shouldStartBundler: options.configuration === 'Debug' || bundlerProps.shouldStartBundler,\n    projectRoot,\n    isSimulator,\n    xcodeProject,\n    device,\n    configuration,\n    shouldSkipInitialBundling,\n    buildCache: options.buildCache !== false,\n    scheme,\n    buildCacheProvider,\n  };\n}\n"], "names": ["resolveOptionsAsync", "projectRoot", "options", "projectConfig", "xcodeProject", "resolveXcodeProject", "bundlerProps", "resolveBundlerPropsAsync", "osType", "name", "scheme", "resolveNativeSchemePropsAsync", "configuration", "device", "profile", "resolveDeviceAsync", "isOSType", "undefined", "isSimulator", "isSimulatorDevice", "getConfig", "buildCacheProvider", "resolveBuildCacheProvider", "exp", "experiments", "remoteBuildCache", "provider", "shouldSkipInitialBundling", "shouldStartBundler", "buildCache"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;;yBAZI;;;;;;+BAE4B;qCACR;qCACV;wBACX;qCACiB;yBAClB;qCACiB;AAIlC,eAAeA,oBACpBC,WAAmB,EACnBC,OAAgB;QA+BdC,gCACEA,iDAAAA;IA9BJ,MAAMC,eAAeC,IAAAA,wCAAmB,EAACJ;IAEzC,MAAMK,eAAe,MAAMC,IAAAA,6CAAwB,EAACN,aAAaC;IAEjE,yEAAyE;IACzE,oGAAoG;IACpG,MAAM,EAAEM,MAAM,EAAEC,MAAMC,MAAM,EAAE,GAAG,MAAMC,IAAAA,kDAA6B,EAClEV,aACAC,SACAE;IAGF,wDAAwD;IACxD,MAAMQ,gBAAgBV,QAAQU,aAAa,IAAI;IAE/C,+DAA+D;IAC/D,mFAAmF;IACnF,MAAMC,SAAS,MAAMC,IAAAA,gBAAO,EAACC,iCAAkB,EAAEb,QAAQW,MAAM,EAAE;QAC/D,mGAAmG;QACnGL,QAAQQ,IAAAA,gBAAQ,EAACR,UAAUA,SAASS;QACpCb;QACAM;QACAE;IACF;IAEA,MAAMM,cAAcC,IAAAA,gCAAiB,EAACN;IAEtC,MAAMV,gBAAgBiB,IAAAA,mBAAS,EAACnB;IAChC,MAAMoB,qBAAqB,MAAMC,IAAAA,8CAAyB,EACxDnB,EAAAA,iCAAAA,cAAcoB,GAAG,CAACC,WAAW,qBAA7BrB,+BAA+BkB,kBAAkB,OAC/ClB,kCAAAA,cAAcoB,GAAG,CAACC,WAAW,sBAA7BrB,kDAAAA,gCAA+BsB,gBAAgB,qBAA/CtB,gDAAiDuB,QAAQ,GAC3DzB;IAGF,gEAAgE;IAChE,8FAA8F;IAC9F,mGAAmG;IACnG,gEAAgE;IAChE,MAAM0B,4BAA4Bf,kBAAkB,WAAW,CAACM;IAEhE,OAAO;QACL,GAAGZ,YAAY;QACfsB,oBAAoB1B,QAAQU,aAAa,KAAK,WAAWN,aAAasB,kBAAkB;QACxF3B;QACAiB;QACAd;QACAS;QACAD;QACAe;QACAE,YAAY3B,QAAQ2B,UAAU,KAAK;QACnCnB;QACAW;IACF;AACF"}