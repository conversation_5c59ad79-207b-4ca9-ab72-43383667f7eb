{"version": 3, "sources": ["../../../../src/export/embed/exportServer.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, modifyConfigAsync, PackageJSONConfig } from '@expo/config';\nimport spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport { execSync } from 'node:child_process';\nimport path from 'path';\n\nimport { disableNetwork } from '../../api/settings';\nimport { Log } from '../../log';\nimport { isSpawnResultError } from '../../start/platforms/ios/xcrun';\nimport { MetroBundlerDevServer } from '../../start/server/metro/MetroBundlerDevServer';\nimport { removeAsync } from '../../utils/dir';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { exportApiRoutesStandaloneAsync } from '../exportStaticAsync';\nimport { copyPublicFolderAsync } from '../publicFolder';\nimport { ExportAssetMap, persistMetroFilesAsync } from '../saveAssets';\nimport { Options } from './resolveOptions';\nimport {\n  isExecutingFromXcodebuild,\n  logInXcode,\n  logMetroErrorInXcode,\n  warnInXcode,\n} from './xcodeCompilerLogger';\n\nconst debug = require('debug')('expo:export:server');\n\ntype ServerDeploymentResults = {\n  url: string;\n  dashboardUrl?: string;\n};\n\nexport async function exportStandaloneServerAsync(\n  projectRoot: string,\n  devServer: MetroBundlerDevServer,\n  {\n    exp,\n    pkg,\n    files,\n    options,\n  }: { exp: ExpoConfig; pkg: PackageJSONConfig; files: ExportAssetMap; options: Options }\n) {\n  if (!options.eager) {\n    await tryRemovingGeneratedOriginAsync(projectRoot, exp);\n  }\n\n  logInXcode('Exporting server');\n\n  // Store the server output in the project's .expo directory.\n  const serverOutput = path.join(projectRoot, '.expo/server', options.platform);\n\n  // Remove the previous server output to prevent stale files.\n  await removeAsync(serverOutput);\n\n  // Export the API routes for server rendering the React Server Components.\n  await exportApiRoutesStandaloneAsync(devServer, {\n    files,\n    platform: 'web',\n    apiRoutesOnly: true,\n  });\n\n  const publicPath = path.resolve(projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n  // Copy over public folder items\n  await copyPublicFolderAsync(publicPath, serverOutput);\n\n  // Copy over the server output on top of the public folder.\n  await persistMetroFilesAsync(files, serverOutput);\n\n  [...files.entries()].forEach(([key, value]) => {\n    if (value.targetDomain === 'server') {\n      // Delete server resources to prevent them from being exposed in the binary.\n      files.delete(key);\n    }\n  });\n\n  // TODO: Deprecate this in favor of a built-in prop that users should avoid setting.\n  const userDefinedServerUrl = exp.extra?.router?.origin;\n  let serverUrl = userDefinedServerUrl;\n\n  const shouldSkipServerDeployment = (() => {\n    if (!options.eager) {\n      logInXcode('Skipping server deployment because the script is not running in eager mode.');\n      return true;\n    }\n\n    // Add an opaque flag to disable server deployment.\n    if (env.EXPO_NO_DEPLOY) {\n      warnInXcode('Skipping server deployment because environment variable EXPO_NO_DEPLOY is set.');\n      return true;\n    }\n\n    // Can't safely deploy from Xcode since the PATH isn't set up correctly. We could amend this in the future and allow users who customize the PATH to deploy from Xcode.\n    if (isExecutingFromXcodebuild()) {\n      // TODO: Don't warn when the eager bundle has been run.\n      warnInXcode(\n        'Skipping server deployment because the build is running from an Xcode run script. Build with Expo CLI or EAS Build to deploy the server automatically.'\n      );\n      return true;\n    }\n\n    return false;\n  })();\n\n  // Deploy the server output to a hosting provider.\n  const deployedServerUrl = shouldSkipServerDeployment\n    ? false\n    : await runServerDeployCommandAsync(projectRoot, {\n        distDirectory: serverOutput,\n        deployScript: getServerDeploymentScript(pkg.scripts, options.platform),\n      });\n\n  if (!deployedServerUrl) {\n    return;\n  }\n\n  if (serverUrl) {\n    logInXcode(\n      `Using custom server URL: ${serverUrl} (ignoring deployment URL: ${deployedServerUrl})`\n    );\n  }\n\n  // If the user-defined server URL is not defined, use the deployed server URL.\n  // This allows for overwriting the server URL in the project's native files.\n  serverUrl ||= deployedServerUrl;\n\n  // If the user hasn't manually defined the server URL, write the deployed server URL to the app.json.\n  if (userDefinedServerUrl) {\n    Log.log('Skip automatically linking server origin to native container');\n    return;\n  }\n  Log.log('Writing generated server URL to app.json');\n\n  // NOTE: Is is it possible to assert that the config needs to be modifiable before building the app?\n  const modification = await modifyConfigAsync(\n    projectRoot,\n    {\n      extra: {\n        ...(exp.extra ?? {}),\n        router: {\n          ...(exp.extra?.router ?? {}),\n          generatedOrigin: serverUrl,\n        },\n      },\n    },\n    {\n      skipSDKVersionRequirement: true,\n    }\n  );\n\n  if (modification.type !== 'success') {\n    throw new CommandError(\n      `Failed to write generated server origin to app.json because the file is dynamic and does not extend the static config. The client will not be able to make server requests to API routes or static files. You can disable server linking with EXPO_NO_DEPLOY=1 or by disabling server output in the app.json.`\n    );\n  }\n}\n\nasync function dumpDeploymentLogs(projectRoot: string, logs: string, name = 'deploy') {\n  const outputPath = path.join(projectRoot, `.expo/logs/${name}.log`);\n  await fs.promises.mkdir(path.dirname(outputPath), { recursive: true });\n  debug('Dumping server deployment logs to: ' + outputPath);\n  await fs.promises.writeFile(outputPath, logs);\n  return outputPath;\n}\n\nfunction getCommandBin(command: string) {\n  try {\n    return execSync(`command -v ${command}`, { stdio: 'pipe' }).toString().trim();\n  } catch {\n    return null;\n  }\n}\n\nasync function runServerDeployCommandAsync(\n  projectRoot: string,\n  {\n    distDirectory,\n    deployScript,\n  }: { distDirectory: string; deployScript: { scriptName: string; script: string } | null }\n): Promise<string | false> {\n  const logOfflineError = () => {\n    const manualScript = deployScript\n      ? `npm run ${deployScript.scriptName}`\n      : `npx eas deploy --export-dir ${distDirectory}`;\n\n    logMetroErrorInXcode(\n      projectRoot,\n      chalk.red`Running CLI in offline mode, skipping server deployment. Deploy manually with: ${manualScript}`\n    );\n  };\n  if (env.EXPO_OFFLINE) {\n    logOfflineError();\n    return false;\n  }\n\n  // TODO: Only allow EAS deployments when staging is enabled, this is because the feature is still staging-only.\n  if (!env.EXPO_UNSTABLE_DEPLOY_SERVER) {\n    return false;\n  }\n\n  if (!env.EAS_BUILD) {\n    // This check helps avoid running EAS if the user isn't a user of EAS.\n    // We only need to run it when building outside of EAS.\n    const globalBin = getCommandBin('eas');\n    if (!globalBin) {\n      // This should never happen from EAS Builds.\n      // Possible to happen when building locally with `npx expo run`\n      logMetroErrorInXcode(\n        projectRoot,\n        `eas-cli is not installed globally, skipping server deployment. Install EAS CLI with 'npm install -g eas-cli'.`\n      );\n      return false;\n    }\n    debug('Found eas-cli:', globalBin);\n  }\n\n  let json: any;\n  try {\n    let results: spawnAsync.SpawnResult;\n\n    const spawnOptions: spawnAsync.SpawnOptions = {\n      cwd: projectRoot,\n      // Ensures that errors can be caught.\n      stdio: 'pipe',\n    };\n    // TODO: Support absolute paths in EAS CLI\n    const exportDir = path.relative(projectRoot, distDirectory);\n    if (deployScript) {\n      logInXcode(`Using custom server deploy script: ${deployScript.scriptName}`);\n      // Amend the path to try and make the custom scripts work.\n\n      results = await spawnAsync(\n        'npm',\n        ['run', deployScript.scriptName, `--export-dir=${exportDir}`],\n        spawnOptions\n      );\n    } else {\n      logInXcode('Deploying server to link with client');\n\n      // results = DEPLOYMENT_SUCCESS_FIXTURE;\n      results = await spawnAsync(\n        'npx',\n        ['eas-cli', 'deploy', '--non-interactive', '--json', `--export-dir=${exportDir}`],\n        spawnOptions\n      );\n\n      debug('Server deployment stdout:', results.stdout);\n\n      // Send stderr to stderr. stdout is parsed as JSON.\n      if (results.stderr) {\n        process.stderr.write(results.stderr);\n      }\n    }\n\n    const logPath = await dumpDeploymentLogs(projectRoot, results.output.join('\\n'));\n\n    try {\n      // {\n      //   \"dashboardUrl\": \"https://staging.expo.dev/projects/6460c11c-e1bc-4084-882a-fd9f57b825b1/hosting/deployments\",\n      //   \"identifier\": \"8a1pwbv6c5\",\n      //   \"url\": \"https://sep30--8a1pwbv6c5.staging.expo.app\"\n      // }\n      json = JSON.parse(results.stdout.trim());\n    } catch {\n      logMetroErrorInXcode(\n        projectRoot,\n        `Failed to parse server deployment JSON output. Check the logs for more information: ${logPath}`\n      );\n      return false;\n    }\n  } catch (error) {\n    if (isSpawnResultError(error)) {\n      const output = error.output.join('\\n').trim() || error.toString();\n      Log.log(\n        chalk.dim(\n          'An error occurred while deploying server. Logs stored at: ' +\n            (await dumpDeploymentLogs(projectRoot, output, 'deploy-error'))\n        )\n      );\n\n      // Likely a server offline or network error.\n      if (output.match(/ENOTFOUND/)) {\n        logOfflineError();\n        // Print the raw error message to help provide more context.\n        Log.log(chalk.dim(output));\n        // Prevent any other network requests (unlikely for this command).\n        disableNetwork();\n        return false;\n      }\n\n      logInXcode(output);\n      if (output.match(/spawn eas ENOENT/)) {\n        // EAS not installed.\n        logMetroErrorInXcode(\n          projectRoot,\n          `Server deployment failed because eas-cli cannot be accessed from the build script's environment (ENOENT). Install EAS CLI with 'npm install -g eas-cli'.`\n        );\n        return false;\n      }\n\n      if (error.stderr.match(/Must configure EAS project by running/)) {\n        // EAS not configured, this can happen when building a project locally before building in EAS.\n        // User must run `eas init`, `eas deploy`, or `eas build` first.\n\n        // TODO: Should we fail the build here or just warn users?\n        logMetroErrorInXcode(\n          projectRoot,\n          `Skipping server deployment because EAS is not configured. Run 'eas init' before trying again, or disable server output in the project.`\n        );\n        return false;\n      }\n    }\n\n    // Throw unhandled server deployment errors.\n    throw error;\n  }\n\n  // Assert json format\n  assertDeploymentJsonOutput(json);\n\n  // Warn about the URL not being valid. This should never happen, but might be possible with third-parties.\n  if (!canParseURL(json.url)) {\n    warnInXcode(`The server deployment URL is not a valid URL: ${json.url}`);\n  }\n\n  if (json.dashboardUrl) {\n    logInXcode(`Server dashboard: ${json.dashboardUrl}`);\n  }\n\n  logInXcode(`Server deployed to: ${json.url}`);\n\n  return json.url;\n}\n\nfunction canParseURL(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction assertDeploymentJsonOutput(json: any): asserts json is ServerDeploymentResults {\n  if (!json || typeof json !== 'object' || typeof json.url !== 'string') {\n    throw new Error(\n      'JSON output of server deployment command are not in the expected format: { url: \"https://...\" }'\n    );\n  }\n}\n\nfunction getServerDeploymentScript(\n  scripts: Record<string, string> | undefined,\n  platform: string\n): { scriptName: string; script: string } | null {\n  // Users can overwrite the default deployment script with:\n  // { scripts: { \"native:deploy\": \"eas deploy --json --non-interactive\" } }\n  // A quick search on GitHub showed that `native:deploy` is not used in any public repos yet.\n  // https://github.com/search?q=%22native%3Adeploy%22+path%3Apackage.json&type=code\n  const DEFAULT_SCRIPT_NAME = 'native:deploy';\n\n  const scriptNames = [\n    // DEFAULT_SCRIPT_NAME + ':' + platform,\n    DEFAULT_SCRIPT_NAME,\n  ];\n\n  for (const scriptName of scriptNames) {\n    if (scripts?.[scriptName]) {\n      return { scriptName, script: scripts[scriptName] };\n    }\n  }\n\n  return null;\n}\n\n/** We can try to remove the generated origin from the manifest when running outside of eager mode. Bundling is the last operation to run so the config will already be embedded with the origin. */\nasync function tryRemovingGeneratedOriginAsync(projectRoot: string, exp: ExpoConfig) {\n  if (env.CI) {\n    // Skip in CI since nothing is committed.\n    return;\n  }\n  if (exp.extra?.router?.generatedOrigin == null) {\n    debug('No generated origin needs removing');\n    return;\n  }\n\n  const modification = await modifyConfigAsync(\n    projectRoot,\n    {\n      extra: {\n        ...(exp.extra ?? {}),\n        router: {\n          ...(exp.extra?.router ?? {}),\n          generatedOrigin: undefined,\n        },\n      },\n    },\n    {\n      skipSDKVersionRequirement: true,\n    }\n  );\n\n  if (modification.type !== 'success') {\n    debug('Could not remove generated origin from manifest');\n  } else {\n    debug('Generated origin has been removed from manifest');\n  }\n}\n"], "names": ["exportStandaloneServerAsync", "debug", "require", "projectRoot", "devServer", "exp", "pkg", "files", "options", "eager", "tryRemovingGeneratedOriginAsync", "logInXcode", "serverOutput", "path", "join", "platform", "removeAsync", "exportApiRoutesStandaloneAsync", "apiRoutesOnly", "publicPath", "resolve", "env", "EXPO_PUBLIC_FOLDER", "copyPublicFolderAsync", "persistMetroFilesAsync", "entries", "for<PERSON>ach", "key", "value", "targetDomain", "delete", "userDefinedServerUrl", "extra", "router", "origin", "serverUrl", "shouldSkipServerDeployment", "EXPO_NO_DEPLOY", "warnInXcode", "isExecutingFromXcodebuild", "deployedServerUrl", "runServerDeployCommandAsync", "distDirectory", "deployScript", "getServerDeploymentScript", "scripts", "Log", "log", "modification", "modifyConfigAsync", "generated<PERSON><PERSON>in", "skipSDKVersionRequirement", "type", "CommandError", "dumpDeploymentLogs", "logs", "name", "outputPath", "fs", "promises", "mkdir", "dirname", "recursive", "writeFile", "getCommandBin", "command", "execSync", "stdio", "toString", "trim", "logOfflineError", "manualScript", "scriptName", "logMetroErrorInXcode", "chalk", "red", "EXPO_OFFLINE", "EXPO_UNSTABLE_DEPLOY_SERVER", "EAS_BUILD", "globalBin", "json", "results", "spawnOptions", "cwd", "exportDir", "relative", "spawnAsync", "stdout", "stderr", "process", "write", "logPath", "output", "JSON", "parse", "error", "isSpawnResultError", "dim", "match", "disableNetwork", "assertDeploymentJsonOutput", "canParseURL", "url", "dashboardUrl", "URL", "Error", "DEFAULT_SCRIPT_NAME", "scriptNames", "script", "CI", "undefined"], "mappings": "AAAA;;;;;CAKC;;;;+BAiCqBA;;;eAAAA;;;;yBAhC2C;;;;;;;gEAC1C;;;;;;;gEACL;;;;;;;gEACH;;;;;;;yBACU;;;;;;;gEACR;;;;;;0BAEc;qBACX;uBACe;qBAEP;qBACR;wBACS;mCACkB;8BACT;4BACiB;qCAOhD;;;;;;AAEP,MAAMC,QAAQC,QAAQ,SAAS;AAOxB,eAAeF,4BACpBG,WAAmB,EACnBC,SAAgC,EAChC,EACEC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,OAAO,EAC8E;QAqC1DH,mBAAAA,YA+DjBA;IAlGZ,IAAI,CAACG,QAAQC,KAAK,EAAE;QAClB,MAAMC,gCAAgCP,aAAaE;IACrD;IAEAM,IAAAA,+BAAU,EAAC;IAEX,4DAA4D;IAC5D,MAAMC,eAAeC,eAAI,CAACC,IAAI,CAACX,aAAa,gBAAgBK,QAAQO,QAAQ;IAE5E,4DAA4D;IAC5D,MAAMC,IAAAA,gBAAW,EAACJ;IAElB,0EAA0E;IAC1E,MAAMK,IAAAA,iDAA8B,EAACb,WAAW;QAC9CG;QACAQ,UAAU;QACVG,eAAe;IACjB;IAEA,MAAMC,aAAaN,eAAI,CAACO,OAAO,CAACjB,aAAakB,QAAG,CAACC,kBAAkB;IAEnE,gCAAgC;IAChC,MAAMC,IAAAA,mCAAqB,EAACJ,YAAYP;IAExC,2DAA2D;IAC3D,MAAMY,IAAAA,kCAAsB,EAACjB,OAAOK;IAEpC;WAAIL,MAAMkB,OAAO;KAAG,CAACC,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM;QACxC,IAAIA,MAAMC,YAAY,KAAK,UAAU;YACnC,4EAA4E;YAC5EtB,MAAMuB,MAAM,CAACH;QACf;IACF;IAEA,oFAAoF;IACpF,MAAMI,wBAAuB1B,aAAAA,IAAI2B,KAAK,sBAAT3B,oBAAAA,WAAW4B,MAAM,qBAAjB5B,kBAAmB6B,MAAM;IACtD,IAAIC,YAAYJ;IAEhB,MAAMK,6BAA6B,AAAC,CAAA;QAClC,IAAI,CAAC5B,QAAQC,KAAK,EAAE;YAClBE,IAAAA,+BAAU,EAAC;YACX,OAAO;QACT;QAEA,mDAAmD;QACnD,IAAIU,QAAG,CAACgB,cAAc,EAAE;YACtBC,IAAAA,gCAAW,EAAC;YACZ,OAAO;QACT;QAEA,uKAAuK;QACvK,IAAIC,IAAAA,8CAAyB,KAAI;YAC/B,uDAAuD;YACvDD,IAAAA,gCAAW,EACT;YAEF,OAAO;QACT;QAEA,OAAO;IACT,CAAA;IAEA,kDAAkD;IAClD,MAAME,oBAAoBJ,6BACtB,QACA,MAAMK,4BAA4BtC,aAAa;QAC7CuC,eAAe9B;QACf+B,cAAcC,0BAA0BtC,IAAIuC,OAAO,EAAErC,QAAQO,QAAQ;IACvE;IAEJ,IAAI,CAACyB,mBAAmB;QACtB;IACF;IAEA,IAAIL,WAAW;QACbxB,IAAAA,+BAAU,EACR,CAAC,yBAAyB,EAAEwB,UAAU,2BAA2B,EAAEK,kBAAkB,CAAC,CAAC;IAE3F;IAEA,8EAA8E;IAC9E,4EAA4E;IAC5EL,cAAcK;IAEd,qGAAqG;IACrG,IAAIT,sBAAsB;QACxBe,QAAG,CAACC,GAAG,CAAC;QACR;IACF;IACAD,QAAG,CAACC,GAAG,CAAC;IAER,oGAAoG;IACpG,MAAMC,eAAe,MAAMC,IAAAA,2BAAiB,EAC1C9C,aACA;QACE6B,OAAO;YACL,GAAI3B,IAAI2B,KAAK,IAAI,CAAC,CAAC;YACnBC,QAAQ;gBACN,GAAI5B,EAAAA,cAAAA,IAAI2B,KAAK,qBAAT3B,YAAW4B,MAAM,KAAI,CAAC,CAAC;gBAC3BiB,iBAAiBf;YACnB;QACF;IACF,GACA;QACEgB,2BAA2B;IAC7B;IAGF,IAAIH,aAAaI,IAAI,KAAK,WAAW;QACnC,MAAM,IAAIC,oBAAY,CACpB,CAAC,6SAA6S,CAAC;IAEnT;AACF;AAEA,eAAeC,mBAAmBnD,WAAmB,EAAEoD,IAAY,EAAEC,OAAO,QAAQ;IAClF,MAAMC,aAAa5C,eAAI,CAACC,IAAI,CAACX,aAAa,CAAC,WAAW,EAAEqD,KAAK,IAAI,CAAC;IAClE,MAAME,aAAE,CAACC,QAAQ,CAACC,KAAK,CAAC/C,eAAI,CAACgD,OAAO,CAACJ,aAAa;QAAEK,WAAW;IAAK;IACpE7D,MAAM,wCAAwCwD;IAC9C,MAAMC,aAAE,CAACC,QAAQ,CAACI,SAAS,CAACN,YAAYF;IACxC,OAAOE;AACT;AAEA,SAASO,cAAcC,OAAe;IACpC,IAAI;QACF,OAAOC,IAAAA,6BAAQ,EAAC,CAAC,WAAW,EAAED,SAAS,EAAE;YAAEE,OAAO;QAAO,GAAGC,QAAQ,GAAGC,IAAI;IAC7E,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAe5B,4BACbtC,WAAmB,EACnB,EACEuC,aAAa,EACbC,YAAY,EAC2E;IAEzF,MAAM2B,kBAAkB;QACtB,MAAMC,eAAe5B,eACjB,CAAC,QAAQ,EAAEA,aAAa6B,UAAU,EAAE,GACpC,CAAC,4BAA4B,EAAE9B,eAAe;QAElD+B,IAAAA,yCAAoB,EAClBtE,aACAuE,gBAAK,CAACC,GAAG,CAAC,+EAA+E,EAAEJ,aAAa,CAAC;IAE7G;IACA,IAAIlD,QAAG,CAACuD,YAAY,EAAE;QACpBN;QACA,OAAO;IACT;IAEA,+GAA+G;IAC/G,IAAI,CAACjD,QAAG,CAACwD,2BAA2B,EAAE;QACpC,OAAO;IACT;IAEA,IAAI,CAACxD,QAAG,CAACyD,SAAS,EAAE;QAClB,sEAAsE;QACtE,uDAAuD;QACvD,MAAMC,YAAYf,cAAc;QAChC,IAAI,CAACe,WAAW;YACd,4CAA4C;YAC5C,+DAA+D;YAC/DN,IAAAA,yCAAoB,EAClBtE,aACA,CAAC,6GAA6G,CAAC;YAEjH,OAAO;QACT;QACAF,MAAM,kBAAkB8E;IAC1B;IAEA,IAAIC;IACJ,IAAI;QACF,IAAIC;QAEJ,MAAMC,eAAwC;YAC5CC,KAAKhF;YACL,qCAAqC;YACrCgE,OAAO;QACT;QACA,0CAA0C;QAC1C,MAAMiB,YAAYvE,eAAI,CAACwE,QAAQ,CAAClF,aAAauC;QAC7C,IAAIC,cAAc;YAChBhC,IAAAA,+BAAU,EAAC,CAAC,mCAAmC,EAAEgC,aAAa6B,UAAU,EAAE;YAC1E,0DAA0D;YAE1DS,UAAU,MAAMK,IAAAA,qBAAU,EACxB,OACA;gBAAC;gBAAO3C,aAAa6B,UAAU;gBAAE,CAAC,aAAa,EAAEY,WAAW;aAAC,EAC7DF;QAEJ,OAAO;YACLvE,IAAAA,+BAAU,EAAC;YAEX,wCAAwC;YACxCsE,UAAU,MAAMK,IAAAA,qBAAU,EACxB,OACA;gBAAC;gBAAW;gBAAU;gBAAqB;gBAAU,CAAC,aAAa,EAAEF,WAAW;aAAC,EACjFF;YAGFjF,MAAM,6BAA6BgF,QAAQM,MAAM;YAEjD,mDAAmD;YACnD,IAAIN,QAAQO,MAAM,EAAE;gBAClBC,QAAQD,MAAM,CAACE,KAAK,CAACT,QAAQO,MAAM;YACrC;QACF;QAEA,MAAMG,UAAU,MAAMrC,mBAAmBnD,aAAa8E,QAAQW,MAAM,CAAC9E,IAAI,CAAC;QAE1E,IAAI;YACF,IAAI;YACJ,kHAAkH;YAClH,gCAAgC;YAChC,wDAAwD;YACxD,IAAI;YACJkE,OAAOa,KAAKC,KAAK,CAACb,QAAQM,MAAM,CAAClB,IAAI;QACvC,EAAE,OAAM;YACNI,IAAAA,yCAAoB,EAClBtE,aACA,CAAC,oFAAoF,EAAEwF,SAAS;YAElG,OAAO;QACT;IACF,EAAE,OAAOI,OAAO;QACd,IAAIC,IAAAA,yBAAkB,EAACD,QAAQ;YAC7B,MAAMH,SAASG,MAAMH,MAAM,CAAC9E,IAAI,CAAC,MAAMuD,IAAI,MAAM0B,MAAM3B,QAAQ;YAC/DtB,QAAG,CAACC,GAAG,CACL2B,gBAAK,CAACuB,GAAG,CACP,+DACG,MAAM3C,mBAAmBnD,aAAayF,QAAQ;YAIrD,4CAA4C;YAC5C,IAAIA,OAAOM,KAAK,CAAC,cAAc;gBAC7B5B;gBACA,4DAA4D;gBAC5DxB,QAAG,CAACC,GAAG,CAAC2B,gBAAK,CAACuB,GAAG,CAACL;gBAClB,kEAAkE;gBAClEO,IAAAA,wBAAc;gBACd,OAAO;YACT;YAEAxF,IAAAA,+BAAU,EAACiF;YACX,IAAIA,OAAOM,KAAK,CAAC,qBAAqB;gBACpC,qBAAqB;gBACrBzB,IAAAA,yCAAoB,EAClBtE,aACA,CAAC,wJAAwJ,CAAC;gBAE5J,OAAO;YACT;YAEA,IAAI4F,MAAMP,MAAM,CAACU,KAAK,CAAC,0CAA0C;gBAC/D,8FAA8F;gBAC9F,gEAAgE;gBAEhE,0DAA0D;gBAC1DzB,IAAAA,yCAAoB,EAClBtE,aACA,CAAC,sIAAsI,CAAC;gBAE1I,OAAO;YACT;QACF;QAEA,4CAA4C;QAC5C,MAAM4F;IACR;IAEA,qBAAqB;IACrBK,2BAA2BpB;IAE3B,0GAA0G;IAC1G,IAAI,CAACqB,YAAYrB,KAAKsB,GAAG,GAAG;QAC1BhE,IAAAA,gCAAW,EAAC,CAAC,8CAA8C,EAAE0C,KAAKsB,GAAG,EAAE;IACzE;IAEA,IAAItB,KAAKuB,YAAY,EAAE;QACrB5F,IAAAA,+BAAU,EAAC,CAAC,kBAAkB,EAAEqE,KAAKuB,YAAY,EAAE;IACrD;IAEA5F,IAAAA,+BAAU,EAAC,CAAC,oBAAoB,EAAEqE,KAAKsB,GAAG,EAAE;IAE5C,OAAOtB,KAAKsB,GAAG;AACjB;AAEA,SAASD,YAAYC,GAAW;IAC9B,IAAI;QACF,kCAAkC;QAClC,IAAIE,IAAIF;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,SAASF,2BAA2BpB,IAAS;IAC3C,IAAI,CAACA,QAAQ,OAAOA,SAAS,YAAY,OAAOA,KAAKsB,GAAG,KAAK,UAAU;QACrE,MAAM,IAAIG,MACR;IAEJ;AACF;AAEA,SAAS7D,0BACPC,OAA2C,EAC3C9B,QAAgB;IAEhB,0DAA0D;IAC1D,0EAA0E;IAC1E,4FAA4F;IAC5F,kFAAkF;IAClF,MAAM2F,sBAAsB;IAE5B,MAAMC,cAAc;QAClB,wCAAwC;QACxCD;KACD;IAED,KAAK,MAAMlC,cAAcmC,YAAa;QACpC,IAAI9D,2BAAAA,OAAS,CAAC2B,WAAW,EAAE;YACzB,OAAO;gBAAEA;gBAAYoC,QAAQ/D,OAAO,CAAC2B,WAAW;YAAC;QACnD;IACF;IAEA,OAAO;AACT;AAEA,kMAAkM,GAClM,eAAe9D,gCAAgCP,WAAmB,EAAEE,GAAe;QAK7EA,mBAAAA,YAWQA;IAfZ,IAAIgB,QAAG,CAACwF,EAAE,EAAE;QACV,yCAAyC;QACzC;IACF;IACA,IAAIxG,EAAAA,aAAAA,IAAI2B,KAAK,sBAAT3B,oBAAAA,WAAW4B,MAAM,qBAAjB5B,kBAAmB6C,eAAe,KAAI,MAAM;QAC9CjD,MAAM;QACN;IACF;IAEA,MAAM+C,eAAe,MAAMC,IAAAA,2BAAiB,EAC1C9C,aACA;QACE6B,OAAO;YACL,GAAI3B,IAAI2B,KAAK,IAAI,CAAC,CAAC;YACnBC,QAAQ;gBACN,GAAI5B,EAAAA,cAAAA,IAAI2B,KAAK,qBAAT3B,YAAW4B,MAAM,KAAI,CAAC,CAAC;gBAC3BiB,iBAAiB4D;YACnB;QACF;IACF,GACA;QACE3D,2BAA2B;IAC7B;IAGF,IAAIH,aAAaI,IAAI,KAAK,WAAW;QACnCnD,MAAM;IACR,OAAO;QACLA,MAAM;IACR;AACF"}