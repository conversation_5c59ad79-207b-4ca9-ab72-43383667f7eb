{"version": 3, "sources": ["../../../src/login/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoLogin: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--username': String,\n      '--password': String,\n      '--otp': String,\n      '--sso': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n      '-u': '--username',\n      '-p': '--password',\n      '-s': '--sso',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Log in to an Expo account`,\n      `npx expo login`,\n      [\n        `-u, --username <string>  Username`,\n        `-p, --password <string>  Password`,\n        `--otp <string>           One-time password from your 2FA device`,\n        `-s, --sso                Log in with SSO`,\n        `-h, --help               Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  const { showLoginPromptAsync } = await import('../api/user/actions.js');\n  return showLoginPromptAsync({\n    // Parsed options\n    username: args['--username'],\n    password: args['--password'],\n    otp: args['--otp'],\n    sso: !!args['--sso'],\n  }).catch(logCmdError);\n};\n"], "names": ["expoLogin", "argv", "args", "assertArgs", "Boolean", "String", "printHelp", "join", "showLoginPromptAsync", "username", "password", "otp", "sso", "catch", "logCmdError"], "mappings": ";;;;;+BAKaA;;;eAAAA;;;sBAHyB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,YAAqB,OAAOC;IACvC,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,cAAcC;QACd,cAAcA;QACd,SAASA;QACT,SAASD;QACT,UAAU;QACV,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,yBAAyB,CAAC,EAC3B,CAAC,cAAc,CAAC,EAChB;YACE,CAAC,iCAAiC,CAAC;YACnC,CAAC,iCAAiC,CAAC;YACnC,CAAC,+DAA+D,CAAC;YACjE,CAAC,wCAAwC,CAAC;YAC1C,CAAC,mCAAmC,CAAC;SACtC,CAACC,IAAI,CAAC;IAEX;IAEA,MAAM,EAAEC,oBAAoB,EAAE,GAAG,MAAM,mEAAA,QAAO;IAC9C,OAAOA,qBAAqB;QAC1B,iBAAiB;QACjBC,UAAUP,IAAI,CAAC,aAAa;QAC5BQ,UAAUR,IAAI,CAAC,aAAa;QAC5BS,KAAKT,IAAI,CAAC,QAAQ;QAClBU,KAAK,CAAC,CAACV,IAAI,CAAC,QAAQ;IACtB,GAAGW,KAAK,CAACC,mBAAW;AACtB"}