{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport type { NodePath } from \"./index.ts\";\nimport type { VirtualTypeAliases } from \"./path/lib/virtual-types.ts\";\nimport type {\n  ExplVisitorBase,\n  VisitorBaseNodes,\n  VisitorBaseAliases,\n} from \"./generated/visitor-types.d.ts\";\n\nexport type VisitPhase = \"enter\" | \"exit\";\n\ninterface VisitNodeObject<S, P extends t.Node> {\n  enter?: VisitNodeFunction<S, P>;\n  exit?: VisitNodeFunction<S, P>;\n}\n\nexport interface ExplVisitNode<S, P extends t.Node> {\n  enter?: VisitNodeFunction<S, P>[];\n  exit?: VisitNodeFunction<S, P>[];\n}\n\nexport interface ExplodedVisitor<S = unknown>\n  extends ExplVisitorBase<S>,\n    ExplVisitNode<S, t.Node> {\n  _exploded: true;\n  _verified: true;\n}\n\n// TODO: Assert that the keys of this are the keys of VirtualTypeAliases without\n// the keys of VisitorBaseNodes and VisitorBaseAliases\n// prettier-ignore\ninterface VisitorVirtualAliases<S> {\n  BindingIdentifier?: VisitNode<S, VirtualTypeAliases[\"BindingIdentifier\"]>;\n  BlockScoped?: VisitNode<S, VirtualTypeAliases[\"BlockScoped\"]>;\n  ExistentialTypeParam?: VisitNode<S, VirtualTypeAliases[\"ExistentialTypeParam\"]>;\n  Expression?: VisitNode<S, VirtualTypeAliases[\"Expression\"]>;\n  //Flow?: VisitNode<S, VirtualTypeAliases[\"Flow\"]>;\n  ForAwaitStatement?: VisitNode<S, VirtualTypeAliases[\"ForAwaitStatement\"]>;\n  Generated?: VisitNode<S, VirtualTypeAliases[\"Generated\"]>;\n  NumericLiteralTypeAnnotation?: VisitNode<S, VirtualTypeAliases[\"NumericLiteralTypeAnnotation\"]>;\n  Pure?: VisitNode<S, VirtualTypeAliases[\"Pure\"]>;\n  Referenced?: VisitNode<S, VirtualTypeAliases[\"Referenced\"]>;\n  ReferencedIdentifier?: VisitNode<S, VirtualTypeAliases[\"ReferencedIdentifier\"]>;\n  ReferencedMemberExpression?: VisitNode<S, VirtualTypeAliases[\"ReferencedMemberExpression\"]>;\n  //RestProperty?: VisitNode<S, VirtualTypeAliases[\"RestProperty\"]>;\n  Scope?: VisitNode<S, VirtualTypeAliases[\"Scope\"]>;\n  //SpreadProperty?: VisitNode<S, VirtualTypeAliases[\"SpreadProperty\"]>;\n  Statement?: VisitNode<S, VirtualTypeAliases[\"Statement\"]>;\n  User?: VisitNode<S, VirtualTypeAliases[\"User\"]>;\n  Var?: VisitNode<S, VirtualTypeAliases[\"Var\"]>;\n}\n\n// TODO: Do not export this? Or give it a better name?\nexport interface VisitorBase<S>\n  extends VisitNodeObject<S, t.Node>,\n    VisitorBaseNodes<S>,\n    VisitorBaseAliases<S>,\n    VisitorVirtualAliases<S> {\n  // Babel supports `NodeTypesWithoutComment | NodeTypesWithoutComment | ... ` but it is\n  // too complex for TS. So we type it as a general visitor only if the key contains `|`\n  // this is good enough for non-visitor traverse options e.g. `noScope`\n  [k: `${string}|${string}`]: VisitNode<S, t.Node>;\n}\n\nexport type Visitor<S = unknown> = VisitorBase<S> | ExplodedVisitor<S>;\n\nexport type VisitNode<S, P extends t.Node> =\n  | VisitNodeFunction<S, P>\n  | VisitNodeObject<S, P>;\n\nexport type VisitNodeFunction<S, P extends t.Node> = (\n  this: S,\n  path: NodePath<P>,\n  state: S,\n) => void;\n"], "mappings": "", "ignoreList": []}