{"version": 3, "sources": ["../../../../../src/start/doctor/dependencies/getVersionedPackages.ts"], "sourcesContent": ["import { PackageJSONConfig } from '@expo/config';\nimport npmPackageArg from 'npm-package-arg';\n\nimport { getVersionedNativeModulesAsync } from './bundledNativeModules';\nimport { hasExpoCanaryAsync } from './resolvePackages';\nimport { getVersionsAsync, SDKVersion } from '../../../api/getVersions';\nimport { Log } from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')(\n  'expo:doctor:dependencies:getVersionedPackages'\n) as typeof console.log;\n\nexport type DependencyList = Record<string, string>;\n\n/** Adds `react-dom`, `react`, and `react-native` to the list of known package versions (`relatedPackages`) */\nfunction normalizeSdkVersionObject(version?: SDKVersion): Record<string, string> {\n  if (!version) {\n    return {};\n  }\n  const { relatedPackages, facebookReactVersion, facebookReactNativeVersion, expoVersion } =\n    version;\n\n  const reactVersion = facebookReactVersion\n    ? {\n        react: facebookReactVersion,\n        'react-dom': facebookReactVersion,\n      }\n    : undefined;\n\n  const expoVersionIfAvailable = expoVersion ? { expo: expoVersion } : undefined;\n\n  return {\n    ...relatedPackages,\n    ...reactVersion,\n    ...expoVersionIfAvailable,\n    'react-native': facebookReactNativeVersion,\n  };\n}\n\n/** Get the known versions for a given SDK, combines all sources. */\nexport async function getCombinedKnownVersionsAsync({\n  projectRoot,\n  sdkVersion,\n  skipCache,\n}: {\n  projectRoot: string;\n  sdkVersion?: string;\n  skipCache?: boolean;\n}) {\n  const skipRemoteVersions = await hasExpoCanaryAsync(projectRoot);\n  if (skipRemoteVersions) {\n    Log.warn('Dependency validation might be unreliable when using canary SDK versions');\n  }\n\n  if (env.EXPO_NO_DEPENDENCY_VALIDATION) {\n    debug('Dependency validation is disabled through EXPO_NO_DEPENDENCY_VALIDATION=1');\n    return {};\n  }\n\n  const bundledNativeModules = sdkVersion\n    ? await getVersionedNativeModulesAsync(projectRoot, sdkVersion, { skipRemoteVersions })\n    : {};\n  const versionsForSdk = !skipRemoteVersions\n    ? await getRemoteVersionsForSdkAsync({ sdkVersion, skipCache })\n    : {};\n  return {\n    ...bundledNativeModules,\n    // Prefer the remote versions over the bundled versions, this enables us to push\n    // emergency fixes that users can access without having to update the `expo` package.\n    ...versionsForSdk,\n  };\n}\n\n/** @returns a key/value list of known dependencies and their version (including range). */\nexport async function getRemoteVersionsForSdkAsync({\n  sdkVersion,\n  skipCache,\n}: { sdkVersion?: string; skipCache?: boolean } = {}): Promise<DependencyList> {\n  if (env.EXPO_OFFLINE) {\n    Log.warn('Dependency validation is unreliable in offline-mode');\n    return {};\n  }\n\n  try {\n    const { sdkVersions } = await getVersionsAsync({ skipCache });\n\n    // We only want versioned dependencies so skip if they cannot be found.\n    if (!sdkVersion || !(sdkVersion in sdkVersions)) {\n      debug(\n        `Skipping versioned dependencies because the SDK version is not found. (sdkVersion: ${sdkVersion}, available: ${Object.keys(\n          sdkVersions\n        ).join(', ')})`\n      );\n      return {};\n    }\n\n    const version = sdkVersions[sdkVersion as keyof typeof sdkVersions] as unknown as SDKVersion;\n\n    return normalizeSdkVersionObject(version);\n  } catch (error: any) {\n    if (error instanceof CommandError && error.code === 'OFFLINE') {\n      return getRemoteVersionsForSdkAsync({ sdkVersion, skipCache });\n    }\n    throw error;\n  }\n}\n\ntype ExcludedNativeModules = {\n  name: string;\n  bundledNativeVersion: string;\n  isExcludedFromValidation: boolean;\n  specifiedVersion?: string; // e.g. 1.2.3, latest\n};\n\n/**\n * Versions a list of `packages` against a given `sdkVersion` based on local and remote versioning resources.\n *\n * @param projectRoot\n * @param param1\n * @returns\n */\nexport async function getVersionedPackagesAsync(\n  projectRoot: string,\n  {\n    packages,\n    sdkVersion,\n    pkg,\n  }: {\n    /** List of npm packages to process. */\n    packages: string[];\n    /** Target SDK Version number to version the `packages` for. */\n    sdkVersion: string;\n    pkg: PackageJSONConfig;\n  }\n): Promise<{\n  packages: string[];\n  messages: string[];\n  excludedNativeModules: ExcludedNativeModules[];\n}> {\n  const versionsForSdk = await getCombinedKnownVersionsAsync({\n    projectRoot,\n    sdkVersion,\n    skipCache: true,\n  });\n\n  let nativeModulesCount = 0;\n  let othersCount = 0;\n  const excludedNativeModules: ExcludedNativeModules[] = [];\n\n  const versionedPackages = packages.map((arg) => {\n    const { name, type, raw, rawSpec } = npmPackageArg(arg);\n\n    if (['tag', 'version', 'range'].includes(type) && name && versionsForSdk[name]) {\n      // Unimodule packages from npm registry are modified to use the bundled version.\n      // Some packages have the recommended version listed in https://exp.host/--/api/v2/versions.\n      const isExcludedFromValidation = pkg?.expo?.install?.exclude?.includes(name);\n      const hasSpecifiedExactVersion = rawSpec !== '' && rawSpec !== '*';\n      if (isExcludedFromValidation || hasSpecifiedExactVersion) {\n        othersCount++;\n        excludedNativeModules.push({\n          name,\n          bundledNativeVersion: versionsForSdk[name],\n          isExcludedFromValidation,\n          specifiedVersion: hasSpecifiedExactVersion ? rawSpec : '',\n        });\n        return raw;\n      }\n      nativeModulesCount++;\n      return `${name}@${versionsForSdk[name]}`;\n    } else {\n      // Other packages are passed through unmodified.\n      othersCount++;\n      return raw;\n    }\n  });\n\n  const messages = getOperationLog({\n    othersCount,\n    nativeModulesCount,\n    sdkVersion,\n  });\n\n  return {\n    packages: versionedPackages,\n    messages,\n    excludedNativeModules,\n  };\n}\n\n/** Craft a set of messages regarding the install operations. */\nexport function getOperationLog({\n  nativeModulesCount,\n  sdkVersion,\n  othersCount,\n}: {\n  nativeModulesCount: number;\n  othersCount: number;\n  sdkVersion: string;\n}): string[] {\n  return [\n    nativeModulesCount > 0 &&\n      `${nativeModulesCount} SDK ${sdkVersion} compatible native ${\n        nativeModulesCount === 1 ? 'module' : 'modules'\n      }`,\n    othersCount > 0 && `${othersCount} other ${othersCount === 1 ? 'package' : 'packages'}`,\n  ].filter(Boolean) as string[];\n}\n"], "names": ["getCombinedKnownVersionsAsync", "getOperationLog", "getRemoteVersionsForSdkAsync", "getVersionedPackagesAsync", "debug", "require", "normalizeSdkVersionObject", "version", "relatedPackages", "facebookReactVersion", "facebookReactNativeVersion", "expoVersion", "reactVersion", "react", "undefined", "expoVersionIfAvailable", "expo", "projectRoot", "sdkVersion", "<PERSON><PERSON><PERSON>", "skipRemoteVersions", "hasExpoCanaryAsync", "Log", "warn", "env", "EXPO_NO_DEPENDENCY_VALIDATION", "bundledNativeModules", "getVersionedNativeModulesAsync", "versionsForSdk", "EXPO_OFFLINE", "sdkVersions", "getVersionsAsync", "Object", "keys", "join", "error", "CommandError", "code", "packages", "pkg", "nativeModulesCount", "othersCount", "excludedNativeModules", "versionedPackages", "map", "arg", "name", "type", "raw", "rawSpec", "npmPackageArg", "includes", "isExcludedFromValidation", "install", "exclude", "hasSpecifiedExactVersion", "push", "bundledNativeVersion", "specifiedVersion", "messages", "filter", "Boolean"], "mappings": ";;;;;;;;;;;IA0CsBA,6BAA6B;eAA7BA;;IAsJNC,eAAe;eAAfA;;IApHMC,4BAA4B;eAA5BA;;IA+CAC,yBAAyB;eAAzBA;;;;gEA1HI;;;;;;sCAEqB;iCACZ;6BACU;qBACzB;qBACA;wBACS;;;;;;AAE7B,MAAMC,QAAQC,QAAQ,SACpB;AAKF,4GAA4G,GAC5G,SAASC,0BAA0BC,OAAoB;IACrD,IAAI,CAACA,SAAS;QACZ,OAAO,CAAC;IACV;IACA,MAAM,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,0BAA0B,EAAEC,WAAW,EAAE,GACtFJ;IAEF,MAAMK,eAAeH,uBACjB;QACEI,OAAOJ;QACP,aAAaA;IACf,IACAK;IAEJ,MAAMC,yBAAyBJ,cAAc;QAAEK,MAAML;IAAY,IAAIG;IAErE,OAAO;QACL,GAAGN,eAAe;QAClB,GAAGI,YAAY;QACf,GAAGG,sBAAsB;QACzB,gBAAgBL;IAClB;AACF;AAGO,eAAeV,8BAA8B,EAClDiB,WAAW,EACXC,UAAU,EACVC,SAAS,EAKV;IACC,MAAMC,qBAAqB,MAAMC,IAAAA,mCAAkB,EAACJ;IACpD,IAAIG,oBAAoB;QACtBE,QAAG,CAACC,IAAI,CAAC;IACX;IAEA,IAAIC,QAAG,CAACC,6BAA6B,EAAE;QACrCrB,MAAM;QACN,OAAO,CAAC;IACV;IAEA,MAAMsB,uBAAuBR,aACzB,MAAMS,IAAAA,oDAA8B,EAACV,aAAaC,YAAY;QAAEE;IAAmB,KACnF,CAAC;IACL,MAAMQ,iBAAiB,CAACR,qBACpB,MAAMlB,6BAA6B;QAAEgB;QAAYC;IAAU,KAC3D,CAAC;IACL,OAAO;QACL,GAAGO,oBAAoB;QACvB,gFAAgF;QAChF,qFAAqF;QACrF,GAAGE,cAAc;IACnB;AACF;AAGO,eAAe1B,6BAA6B,EACjDgB,UAAU,EACVC,SAAS,EACoC,GAAG,CAAC,CAAC;IAClD,IAAIK,QAAG,CAACK,YAAY,EAAE;QACpBP,QAAG,CAACC,IAAI,CAAC;QACT,OAAO,CAAC;IACV;IAEA,IAAI;QACF,MAAM,EAAEO,WAAW,EAAE,GAAG,MAAMC,IAAAA,6BAAgB,EAAC;YAAEZ;QAAU;QAE3D,uEAAuE;QACvE,IAAI,CAACD,cAAc,CAAEA,CAAAA,cAAcY,WAAU,GAAI;YAC/C1B,MACE,CAAC,mFAAmF,EAAEc,WAAW,aAAa,EAAEc,OAAOC,IAAI,CACzHH,aACAI,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjB,OAAO,CAAC;QACV;QAEA,MAAM3B,UAAUuB,WAAW,CAACZ,WAAuC;QAEnE,OAAOZ,0BAA0BC;IACnC,EAAE,OAAO4B,OAAY;QACnB,IAAIA,iBAAiBC,oBAAY,IAAID,MAAME,IAAI,KAAK,WAAW;YAC7D,OAAOnC,6BAA6B;gBAAEgB;gBAAYC;YAAU;QAC9D;QACA,MAAMgB;IACR;AACF;AAgBO,eAAehC,0BACpBc,WAAmB,EACnB,EACEqB,QAAQ,EACRpB,UAAU,EACVqB,GAAG,EAOJ;IAMD,MAAMX,iBAAiB,MAAM5B,8BAA8B;QACzDiB;QACAC;QACAC,WAAW;IACb;IAEA,IAAIqB,qBAAqB;IACzB,IAAIC,cAAc;IAClB,MAAMC,wBAAiD,EAAE;IAEzD,MAAMC,oBAAoBL,SAASM,GAAG,CAAC,CAACC;QACtC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE,GAAGC,IAAAA,wBAAa,EAACL;QAEnD,IAAI;YAAC;YAAO;YAAW;SAAQ,CAACM,QAAQ,CAACJ,SAASD,QAAQlB,cAAc,CAACkB,KAAK,EAAE;gBAG7CP,2BAAAA,mBAAAA;YAFjC,gFAAgF;YAChF,4FAA4F;YAC5F,MAAMa,2BAA2Bb,wBAAAA,YAAAA,IAAKvB,IAAI,sBAATuB,oBAAAA,UAAWc,OAAO,sBAAlBd,4BAAAA,kBAAoBe,OAAO,qBAA3Bf,0BAA6BY,QAAQ,CAACL;YACvE,MAAMS,2BAA2BN,YAAY,MAAMA,YAAY;YAC/D,IAAIG,4BAA4BG,0BAA0B;gBACxDd;gBACAC,sBAAsBc,IAAI,CAAC;oBACzBV;oBACAW,sBAAsB7B,cAAc,CAACkB,KAAK;oBAC1CM;oBACAM,kBAAkBH,2BAA2BN,UAAU;gBACzD;gBACA,OAAOD;YACT;YACAR;YACA,OAAO,GAAGM,KAAK,CAAC,EAAElB,cAAc,CAACkB,KAAK,EAAE;QAC1C,OAAO;YACL,gDAAgD;YAChDL;YACA,OAAOO;QACT;IACF;IAEA,MAAMW,WAAW1D,gBAAgB;QAC/BwC;QACAD;QACAtB;IACF;IAEA,OAAO;QACLoB,UAAUK;QACVgB;QACAjB;IACF;AACF;AAGO,SAASzC,gBAAgB,EAC9BuC,kBAAkB,EAClBtB,UAAU,EACVuB,WAAW,EAKZ;IACC,OAAO;QACLD,qBAAqB,KACnB,GAAGA,mBAAmB,KAAK,EAAEtB,WAAW,mBAAmB,EACzDsB,uBAAuB,IAAI,WAAW,WACtC;QACJC,cAAc,KAAK,GAAGA,YAAY,OAAO,EAAEA,gBAAgB,IAAI,YAAY,YAAY;KACxF,CAACmB,MAAM,CAACC;AACX"}