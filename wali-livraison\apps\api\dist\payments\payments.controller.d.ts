import { PaymentsService, CreatePaymentDto } from './payments.service';
import { PaymentMethod } from '@prisma/client';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    processPayment(createPaymentDto: CreatePaymentDto, user: any): Promise<import("./payments.service").PaymentResponse>;
    getPaymentStatus(transactionId: string, body: {
        paymentMethod: PaymentMethod;
    }): Promise<{
        transactionId: string;
        status: string;
        amount: number;
        paymentMethod: import(".prisma/client").$Enums.PaymentMethod;
        createdAt: Date;
    }>;
    stripeWebhook(body: any): Promise<{
        received: boolean;
    }>;
    orangeCallback(body: any): Promise<{
        received: boolean;
    }>;
    mtnCallback(body: any): Promise<{
        received: boolean;
    }>;
    waveCallback(body: any): Promise<{
        received: boolean;
    }>;
}
