"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const orders_service_1 = require("./orders.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const client_1 = require("@prisma/client");
let OrdersController = class OrdersController {
    ordersService;
    constructor(ordersService) {
        this.ordersService = ordersService;
    }
    async create(createOrderDto, user) {
        return this.ordersService.create(createOrderDto, user.id);
    }
    async findAll(clientId, driverId, storeId, status, orderType, user) {
        if (user.role === client_1.UserRole.CLIENT) {
            clientId = user.id;
        }
        else if (user.role === client_1.UserRole.DRIVER) {
            const driverProfile = await this.ordersService['prisma'].driverProfile.findUnique({
                where: { userId: user.id },
            });
            if (driverProfile) {
                driverId = driverProfile.id;
            }
        }
        return this.ordersService.findAll(clientId, driverId, storeId, status, orderType);
    }
    async getAvailableOrders(user) {
        return this.ordersService.getAvailableOrders();
    }
    async findOne(id) {
        return this.ordersService.findOne(id);
    }
    async assignDriver(orderId, assignData) {
        return this.ordersService.assignDriver(orderId, assignData.driverId);
    }
    async acceptOrder(orderId, user) {
        const driverProfile = await this.ordersService['prisma'].driverProfile.findUnique({
            where: { userId: user.id },
        });
        if (!driverProfile) {
            throw new Error('Profil driver non trouvé');
        }
        return this.ordersService.acceptOrder(orderId, driverProfile.id);
    }
    async updateStatus(orderId, updateStatusDto, user) {
        return this.ordersService.updateStatus(orderId, updateStatusDto, user.id, user.role);
    }
};
exports.OrdersController = OrdersController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.CLIENT),
    (0, swagger_1.ApiOperation)({ summary: 'Créer une nouvelle commande' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Commande créée avec succès' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Récupérer les commandes' }),
    (0, swagger_1.ApiQuery)({ name: 'clientId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'driverId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'storeId', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: client_1.OrderStatus }),
    (0, swagger_1.ApiQuery)({ name: 'orderType', required: false, enum: client_1.OrderType }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Liste des commandes' }),
    __param(0, (0, common_1.Query)('clientId')),
    __param(1, (0, common_1.Query)('driverId')),
    __param(2, (0, common_1.Query)('storeId')),
    __param(3, (0, common_1.Query)('status')),
    __param(4, (0, common_1.Query)('orderType')),
    __param(5, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('available'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.DRIVER),
    (0, swagger_1.ApiOperation)({ summary: 'Récupérer les commandes disponibles pour les drivers' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Commandes disponibles' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "getAvailableOrders", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Récupérer une commande par ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Commande trouvée' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Commande non trouvée' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id/assign'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Assigner un driver à une commande (Admin seulement)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Driver assigné' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Commande non trouvée' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "assignDriver", null);
__decorate([
    (0, common_1.Put)(':id/accept'),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(client_1.UserRole.DRIVER),
    (0, swagger_1.ApiOperation)({ summary: 'Accepter une commande (Driver seulement)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Commande acceptée' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Commande non disponible' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "acceptOrder", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Mettre à jour le statut d\'une commande' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statut mis à jour' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Non autorisé' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Commande non trouvée' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "updateStatus", null);
exports.OrdersController = OrdersController = __decorate([
    (0, swagger_1.ApiTags)('Orders'),
    (0, common_1.Controller)('orders'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [orders_service_1.OrdersService])
], OrdersController);
//# sourceMappingURL=orders.controller.js.map