{"version": 3, "sources": ["../../../../../src/start/server/metro/metroErrorInterface.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport chalk from 'chalk';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { parse, StackFrame } from 'stacktrace-parser';\nimport terminalLink from 'terminal-link';\n\nimport { LogBoxLog } from './log-box/LogBoxLog';\nimport type { CodeFrame, StackFrame as MetroStackFrame } from './log-box/LogBoxSymbolication';\nimport { getStackFormattedLocation } from './log-box/formatProjectFilePath';\nimport { Log } from '../../../log';\nimport { stripAnsi } from '../../../utils/ansi';\nimport { env } from '../../../utils/env';\nimport { CommandError, SilentError } from '../../../utils/errors';\nimport { createMetroEndpointAsync } from '../getStaticRenderFunctions';\n\nfunction fill(width: number): string {\n  return Array(width).join(' ');\n}\n\nfunction formatPaths(config: { filePath: string | null; line?: number; col?: number }) {\n  const filePath = chalk.reset(config.filePath);\n  return (\n    chalk.dim('(') +\n    filePath +\n    chalk.dim(`:${[config.line, config.col].filter(Boolean).join(':')})`)\n  );\n}\n\nexport async function logMetroErrorWithStack(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame?: CodeFrame;\n    error: Error;\n  }\n) {\n  if (error instanceof SilentError) {\n    return;\n  }\n\n  // process.stdout.write('\\u001b[0m'); // Reset attributes\n  // process.stdout.write('\\u001bc'); // Reset the terminal\n\n  Log.log();\n  Log.log(chalk.red('Metro error: ') + error.message);\n  Log.log();\n\n  if (error instanceof CommandError) {\n    return;\n  }\n\n  Log.log(\n    getStackAsFormattedLog(projectRoot, { stack, codeFrame, error, showCollapsedFrames: true })\n  );\n}\n\nexport function getStackAsFormattedLog(\n  projectRoot: string,\n  {\n    stack,\n    codeFrame,\n    error,\n    showCollapsedFrames = env.EXPO_DEBUG,\n  }: {\n    stack: MetroStackFrame[];\n    codeFrame?: CodeFrame;\n    error?: Error;\n    showCollapsedFrames?: boolean;\n  }\n): string {\n  const logs: string[] = [];\n  let hasCodeFramePresented = false;\n  if (codeFrame) {\n    const maxWarningLineLength = Math.max(800, process.stdout.columns);\n\n    const lineText = codeFrame.content;\n    const isPreviewTooLong = codeFrame.content\n      .split('\\n')\n      .some((line) => line.length > maxWarningLineLength);\n    const column = codeFrame.location?.column;\n    // When the preview is too long, we skip reading the file and attempting to apply\n    // code coloring, this is because it can get very slow.\n    if (isPreviewTooLong) {\n      let previewLine = '';\n      let cursorLine = '';\n\n      const formattedPath = formatPaths({\n        filePath: codeFrame.fileName,\n        line: codeFrame.location?.row,\n        col: codeFrame.location?.column,\n      });\n      // Create a curtailed preview line like:\n      // `...transition:'fade'},k._updatePropsStack=function(){clearImmediate(k._updateImmediate),k._updateImmediate...`\n      // If there is no text preview or column number, we can't do anything.\n      if (lineText && column != null) {\n        const rangeWindow = Math.round(\n          Math.max(codeFrame.fileName?.length ?? 0, Math.max(80, process.stdout.columns)) / 2\n        );\n        let minBounds = Math.max(0, column - rangeWindow);\n        const maxBounds = Math.min(minBounds + rangeWindow * 2, lineText.length);\n        previewLine = lineText.slice(minBounds, maxBounds);\n\n        // If we splice content off the start, then we should append `...`.\n        // This is unlikely to happen since we limit the activation size.\n        if (minBounds > 0) {\n          // Adjust the min bounds so the cursor is aligned after we add the \"...\"\n          minBounds -= 3;\n          previewLine = chalk.dim('...') + previewLine;\n        }\n        if (maxBounds < lineText.length) {\n          previewLine += chalk.dim('...');\n        }\n\n        // If the column property could be found, then use that to fix the cursor location which is often broken in regex.\n        cursorLine = (column == null ? '' : fill(column) + chalk.reset('^')).slice(minBounds);\n\n        logs.push(formattedPath, '', previewLine, cursorLine, chalk.dim('(error truncated)'));\n        hasCodeFramePresented = true;\n      }\n    } else {\n      logs.push(codeFrame.content);\n      hasCodeFramePresented = true;\n    }\n  }\n\n  if (stack?.length) {\n    const stackProps = stack.map((frame) => {\n      return {\n        title: frame.methodName,\n        subtitle: getStackFormattedLocation(projectRoot, frame),\n        collapse: frame.collapse,\n      };\n    });\n\n    const stackLines: string[] = [];\n    const backupStackLines: string[] = [];\n\n    stackProps.forEach((frame) => {\n      const shouldShow = !frame.collapse || showCollapsedFrames;\n\n      const position = terminalLink.isSupported\n        ? terminalLink(frame.subtitle, frame.subtitle)\n        : frame.subtitle;\n      let lineItem = chalk.gray(`  ${frame.title} (${position})`);\n\n      if (frame.collapse) {\n        lineItem = chalk.dim(lineItem);\n      }\n      // Never show the internal module system.\n      const isMetroRuntime =\n        /\\/metro-runtime\\/src\\/polyfills\\/require\\.js/.test(frame.subtitle) ||\n        /\\/metro-require\\/require\\.js/.test(frame.subtitle);\n      if (!isMetroRuntime) {\n        if (shouldShow) {\n          stackLines.push(lineItem);\n        }\n        backupStackLines.push(lineItem);\n      }\n    });\n\n    if (hasCodeFramePresented) {\n      logs.push('');\n    }\n    logs.push(chalk.bold`Call Stack`);\n\n    if (!backupStackLines.length) {\n      logs.push(chalk.gray('  No stack trace available.'));\n    } else {\n      // If there are not stack lines then it means the error likely happened in the node modules, in this case we should fallback to showing all the\n      // the stacks to give the user whatever help we can.\n      const displayStack = stackLines.length ? stackLines : backupStackLines;\n      logs.push(displayStack.join('\\n'));\n    }\n  } else if (error) {\n    logs.push(chalk.gray(`  ${error.stack}`));\n  }\n  return logs.join('\\n');\n}\n\nexport const IS_METRO_BUNDLE_ERROR_SYMBOL = Symbol('_isMetroBundleError');\nconst HAS_LOGGED_SYMBOL = Symbol('_hasLoggedInCLI');\n\nexport async function logMetroError(\n  projectRoot: string,\n  {\n    error,\n  }: {\n    error: Error & {\n      [HAS_LOGGED_SYMBOL]?: boolean;\n    };\n  }\n) {\n  if (error instanceof SilentError || error[HAS_LOGGED_SYMBOL]) {\n    return;\n  }\n  error[HAS_LOGGED_SYMBOL] = true;\n\n  const stack = parseErrorStack(projectRoot, error.stack);\n\n  const log = new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n\n  await new Promise((res) => log.symbolicate('stack', res));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\nfunction isTransformError(\n  error: any\n): error is { type: 'TransformError'; filename: string; lineNumber: number; column: number } {\n  return error.type === 'TransformError';\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nfunction logFromError({ error, projectRoot }: { error: Error; projectRoot: string }) {\n  // Remap direct Metro Node.js errors to a format that will appear more client-friendly in the logbox UI.\n  let stack: MetroStackFrame[] | undefined;\n  if (isTransformError(error) && error.filename) {\n    // Syntax errors in static rendering.\n    stack = [\n      {\n        file: path.join(projectRoot, error.filename),\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: error.lineNumber,\n        column: error.column,\n      },\n    ];\n  } else if ('originModulePath' in error && typeof error.originModulePath === 'string') {\n    // TODO: Use import stack here when the error is resolution based.\n    stack = [\n      {\n        file: error.originModulePath,\n        methodName: '<unknown>',\n        arguments: [],\n        // TODO: Import stack\n        lineNumber: 0,\n        column: 0,\n      },\n    ];\n  } else {\n    stack = parseErrorStack(projectRoot, error.stack);\n  }\n\n  return new LogBoxLog({\n    level: 'static',\n    message: {\n      content: error.message,\n      substitutions: [],\n    },\n    isComponentError: false,\n    stack,\n    category: 'static',\n    componentStack: [],\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function logMetroErrorAsync({\n  error,\n  projectRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n}\n\n/** @returns the html required to render the static metro error as an SPA. */\nexport async function getErrorOverlayHtmlAsync({\n  error,\n  projectRoot,\n  routerRoot,\n}: {\n  error: Error;\n  projectRoot: string;\n  routerRoot: string;\n}) {\n  const log = logFromError({ projectRoot, error });\n\n  await new Promise<void>((res) => log.symbolicate('stack', () => res()));\n\n  logMetroErrorWithStack(projectRoot, {\n    stack: log.symbolicated?.stack?.stack ?? [],\n    codeFrame: log.codeFrame,\n    error,\n  });\n\n  if ('message' in log && 'content' in log.message && typeof log.message.content === 'string') {\n    log.message.content = stripAnsi(log.message.content)!;\n  }\n\n  const logBoxContext = {\n    selectedLogIndex: 0,\n    isDisabled: false,\n    logs: [log],\n  };\n  const html = `<html><head><style>#root,body,html{height:100%}body{overflow:hidden}#root{display:flex}</style></head><body><div id=\"root\"></div><script id=\"_expo-static-error\" type=\"application/json\">${JSON.stringify(\n    logBoxContext\n  )}</script></body></html>`;\n\n  const errorOverlayEntry = await createMetroEndpointAsync(\n    projectRoot,\n    // Keep the URL relative\n    '',\n    resolveFrom(projectRoot, 'expo-router/_error'),\n    {\n      mode: 'development',\n      platform: 'web',\n      minify: false,\n      optimize: false,\n      usedExports: false,\n      baseUrl: '',\n      routerRoot,\n      isExporting: false,\n      reactCompiler: false,\n    }\n  );\n\n  const htmlWithJs = html.replace('</body>', `<script src=${errorOverlayEntry}></script></body>`);\n  return htmlWithJs;\n}\n\nfunction parseErrorStack(\n  projectRoot: string,\n  stack?: string\n): (StackFrame & { collapse?: boolean })[] {\n  if (stack == null) {\n    return [];\n  }\n  if (Array.isArray(stack)) {\n    return stack;\n  }\n\n  const serverRoot = getMetroServerRoot(projectRoot);\n\n  return parse(stack)\n    .map((frame) => {\n      // frame.file will mostly look like `http://localhost:8081/index.bundle?platform=web&dev=true&hot=false`\n\n      if (frame.file) {\n        // SSR will sometimes have absolute paths followed by `.bundle?...`, we need to try and make them relative paths and append a dev server URL.\n        if (frame.file.startsWith('/') && frame.file.includes('bundle?') && !canParse(frame.file)) {\n          // Malformed stack file from SSR. Attempt to repair.\n          frame.file = 'https://localhost:8081/' + path.relative(serverRoot, frame.file);\n        }\n      }\n\n      return {\n        ...frame,\n        column: frame.column != null ? frame.column - 1 : null,\n      };\n    })\n    .filter((frame) => frame.file && !frame.file.includes('node_modules'));\n}\n\nfunction canParse(url: string): boolean {\n  try {\n    // eslint-disable-next-line no-new\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": ["IS_METRO_BUNDLE_ERROR_SYMBOL", "getErrorOverlayHtmlAsync", "getStackAsFormattedLog", "logMetroError", "logMetroErrorAsync", "logMetroErrorWithStack", "fill", "width", "Array", "join", "formatPaths", "config", "filePath", "chalk", "reset", "dim", "line", "col", "filter", "Boolean", "projectRoot", "stack", "codeFrame", "error", "SilentError", "Log", "log", "red", "message", "CommandError", "showCollapsedFrames", "env", "EXPO_DEBUG", "logs", "hasCodeFramePresented", "maxWarning<PERSON>ine<PERSON><PERSON><PERSON>", "Math", "max", "process", "stdout", "columns", "lineText", "content", "isPreviewTooLong", "split", "some", "length", "column", "location", "previewLine", "cursorLine", "formattedPath", "fileName", "row", "rangeWindow", "round", "minBounds", "maxBounds", "min", "slice", "push", "stackProps", "map", "frame", "title", "methodName", "subtitle", "getStackFormattedLocation", "collapse", "stackLines", "backupStackLines", "for<PERSON>ach", "shouldShow", "position", "terminalLink", "isSupported", "lineItem", "gray", "isMetroRuntime", "test", "bold", "displayStack", "Symbol", "HAS_LOGGED_SYMBOL", "parseError<PERSON>tack", "LogBoxLog", "level", "substitutions", "isComponentError", "category", "componentStack", "Promise", "res", "symbolicate", "symbolicated", "isTransformError", "type", "logFromError", "filename", "file", "path", "arguments", "lineNumber", "originModulePath", "routerRoot", "stripAnsi", "logBoxContext", "selectedLogIndex", "isDisabled", "html", "JSON", "stringify", "errorOverlayEntry", "createMetroEndpointAsync", "resolveFrom", "mode", "platform", "minify", "optimize", "usedExports", "baseUrl", "isExporting", "reactCompiler", "htmlWithJs", "replace", "isArray", "serverRoot", "getMetroServerRoot", "parse", "startsWith", "includes", "canParse", "relative", "url", "URL"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAyLYA,4BAA4B;eAA5BA;;IAgHSC,wBAAwB;eAAxBA;;IA3ONC,sBAAsB;eAAtBA;;IA8HMC,aAAa;eAAbA;;IA0FAC,kBAAkB;eAAlBA;;IAxPAC,sBAAsB;eAAtBA;;;;yBA7Ba;;;;;;;gEACjB;;;;;;;gEACD;;;;;;;gEACO;;;;;;;yBACU;;;;;;;gEACT;;;;;;2BAEC;uCAEgB;qBACtB;sBACM;qBACN;wBACsB;0CACD;;;;;;AAEzC,SAASC,KAAKC,KAAa;IACzB,OAAOC,MAAMD,OAAOE,IAAI,CAAC;AAC3B;AAEA,SAASC,YAAYC,MAAgE;IACnF,MAAMC,WAAWC,gBAAK,CAACC,KAAK,CAACH,OAAOC,QAAQ;IAC5C,OACEC,gBAAK,CAACE,GAAG,CAAC,OACVH,WACAC,gBAAK,CAACE,GAAG,CAAC,CAAC,CAAC,EAAE;QAACJ,OAAOK,IAAI;QAAEL,OAAOM,GAAG;KAAC,CAACC,MAAM,CAACC,SAASV,IAAI,CAAC,KAAK,CAAC,CAAC;AAExE;AAEO,eAAeJ,uBACpBe,WAAmB,EACnB,EACEC,KAAK,EACLC,SAAS,EACTC,KAAK,EAKN;IAED,IAAIA,iBAAiBC,mBAAW,EAAE;QAChC;IACF;IAEA,yDAAyD;IACzD,yDAAyD;IAEzDC,QAAG,CAACC,GAAG;IACPD,QAAG,CAACC,GAAG,CAACb,gBAAK,CAACc,GAAG,CAAC,mBAAmBJ,MAAMK,OAAO;IAClDH,QAAG,CAACC,GAAG;IAEP,IAAIH,iBAAiBM,oBAAY,EAAE;QACjC;IACF;IAEAJ,QAAG,CAACC,GAAG,CACLxB,uBAAuBkB,aAAa;QAAEC;QAAOC;QAAWC;QAAOO,qBAAqB;IAAK;AAE7F;AAEO,SAAS5B,uBACdkB,WAAmB,EACnB,EACEC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLO,sBAAsBC,QAAG,CAACC,UAAU,EAMrC;IAED,MAAMC,OAAiB,EAAE;IACzB,IAAIC,wBAAwB;IAC5B,IAAIZ,WAAW;YAOEA;QANf,MAAMa,uBAAuBC,KAAKC,GAAG,CAAC,KAAKC,QAAQC,MAAM,CAACC,OAAO;QAEjE,MAAMC,WAAWnB,UAAUoB,OAAO;QAClC,MAAMC,mBAAmBrB,UAAUoB,OAAO,CACvCE,KAAK,CAAC,MACNC,IAAI,CAAC,CAAC7B,OAASA,KAAK8B,MAAM,GAAGX;QAChC,MAAMY,UAASzB,sBAAAA,UAAU0B,QAAQ,qBAAlB1B,oBAAoByB,MAAM;QACzC,iFAAiF;QACjF,uDAAuD;QACvD,IAAIJ,kBAAkB;gBAMZrB,sBACDA;YANP,IAAI2B,cAAc;YAClB,IAAIC,aAAa;YAEjB,MAAMC,gBAAgBzC,YAAY;gBAChCE,UAAUU,UAAU8B,QAAQ;gBAC5BpC,IAAI,GAAEM,uBAAAA,UAAU0B,QAAQ,qBAAlB1B,qBAAoB+B,GAAG;gBAC7BpC,GAAG,GAAEK,uBAAAA,UAAU0B,QAAQ,qBAAlB1B,qBAAoByB,MAAM;YACjC;YACA,wCAAwC;YACxC,kHAAkH;YAClH,sEAAsE;YACtE,IAAIN,YAAYM,UAAU,MAAM;oBAEnBzB;gBADX,MAAMgC,cAAclB,KAAKmB,KAAK,CAC5BnB,KAAKC,GAAG,CAACf,EAAAA,sBAAAA,UAAU8B,QAAQ,qBAAlB9B,oBAAoBwB,MAAM,KAAI,GAAGV,KAAKC,GAAG,CAAC,IAAIC,QAAQC,MAAM,CAACC,OAAO,KAAK;gBAEpF,IAAIgB,YAAYpB,KAAKC,GAAG,CAAC,GAAGU,SAASO;gBACrC,MAAMG,YAAYrB,KAAKsB,GAAG,CAACF,YAAYF,cAAc,GAAGb,SAASK,MAAM;gBACvEG,cAAcR,SAASkB,KAAK,CAACH,WAAWC;gBAExC,mEAAmE;gBACnE,iEAAiE;gBACjE,IAAID,YAAY,GAAG;oBACjB,wEAAwE;oBACxEA,aAAa;oBACbP,cAAcpC,gBAAK,CAACE,GAAG,CAAC,SAASkC;gBACnC;gBACA,IAAIQ,YAAYhB,SAASK,MAAM,EAAE;oBAC/BG,eAAepC,gBAAK,CAACE,GAAG,CAAC;gBAC3B;gBAEA,kHAAkH;gBAClHmC,aAAa,AAACH,CAAAA,UAAU,OAAO,KAAKzC,KAAKyC,UAAUlC,gBAAK,CAACC,KAAK,CAAC,IAAG,EAAG6C,KAAK,CAACH;gBAE3EvB,KAAK2B,IAAI,CAACT,eAAe,IAAIF,aAAaC,YAAYrC,gBAAK,CAACE,GAAG,CAAC;gBAChEmB,wBAAwB;YAC1B;QACF,OAAO;YACLD,KAAK2B,IAAI,CAACtC,UAAUoB,OAAO;YAC3BR,wBAAwB;QAC1B;IACF;IAEA,IAAIb,yBAAAA,MAAOyB,MAAM,EAAE;QACjB,MAAMe,aAAaxC,MAAMyC,GAAG,CAAC,CAACC;YAC5B,OAAO;gBACLC,OAAOD,MAAME,UAAU;gBACvBC,UAAUC,IAAAA,gDAAyB,EAAC/C,aAAa2C;gBACjDK,UAAUL,MAAMK,QAAQ;YAC1B;QACF;QAEA,MAAMC,aAAuB,EAAE;QAC/B,MAAMC,mBAA6B,EAAE;QAErCT,WAAWU,OAAO,CAAC,CAACR;YAClB,MAAMS,aAAa,CAACT,MAAMK,QAAQ,IAAItC;YAEtC,MAAM2C,WAAWC,uBAAY,CAACC,WAAW,GACrCD,IAAAA,uBAAY,EAACX,MAAMG,QAAQ,EAAEH,MAAMG,QAAQ,IAC3CH,MAAMG,QAAQ;YAClB,IAAIU,WAAW/D,gBAAK,CAACgE,IAAI,CAAC,CAAC,EAAE,EAAEd,MAAMC,KAAK,CAAC,EAAE,EAAES,SAAS,CAAC,CAAC;YAE1D,IAAIV,MAAMK,QAAQ,EAAE;gBAClBQ,WAAW/D,gBAAK,CAACE,GAAG,CAAC6D;YACvB;YACA,yCAAyC;YACzC,MAAME,iBACJ,+CAA+CC,IAAI,CAAChB,MAAMG,QAAQ,KAClE,+BAA+Ba,IAAI,CAAChB,MAAMG,QAAQ;YACpD,IAAI,CAACY,gBAAgB;gBACnB,IAAIN,YAAY;oBACdH,WAAWT,IAAI,CAACgB;gBAClB;gBACAN,iBAAiBV,IAAI,CAACgB;YACxB;QACF;QAEA,IAAI1C,uBAAuB;YACzBD,KAAK2B,IAAI,CAAC;QACZ;QACA3B,KAAK2B,IAAI,CAAC/C,gBAAK,CAACmE,IAAI,CAAC,UAAU,CAAC;QAEhC,IAAI,CAACV,iBAAiBxB,MAAM,EAAE;YAC5Bb,KAAK2B,IAAI,CAAC/C,gBAAK,CAACgE,IAAI,CAAC;QACvB,OAAO;YACL,+IAA+I;YAC/I,oDAAoD;YACpD,MAAMI,eAAeZ,WAAWvB,MAAM,GAAGuB,aAAaC;YACtDrC,KAAK2B,IAAI,CAACqB,aAAaxE,IAAI,CAAC;QAC9B;IACF,OAAO,IAAIc,OAAO;QAChBU,KAAK2B,IAAI,CAAC/C,gBAAK,CAACgE,IAAI,CAAC,CAAC,EAAE,EAAEtD,MAAMF,KAAK,EAAE;IACzC;IACA,OAAOY,KAAKxB,IAAI,CAAC;AACnB;AAEO,MAAMT,+BAA+BkF,OAAO;AACnD,MAAMC,oBAAoBD,OAAO;AAE1B,eAAe/E,cACpBiB,WAAmB,EACnB,EACEG,KAAK,EAKN;QAwBQG,yBAAAA;IAtBT,IAAIH,iBAAiBC,mBAAW,IAAID,KAAK,CAAC4D,kBAAkB,EAAE;QAC5D;IACF;IACA5D,KAAK,CAAC4D,kBAAkB,GAAG;IAE3B,MAAM9D,QAAQ+D,gBAAgBhE,aAAaG,MAAMF,KAAK;IAEtD,MAAMK,MAAM,IAAI2D,oBAAS,CAAC;QACxBC,OAAO;QACP1D,SAAS;YACPc,SAASnB,MAAMK,OAAO;YACtB2D,eAAe,EAAE;QACnB;QACAC,kBAAkB;QAClBnE;QACAoE,UAAU;QACVC,gBAAgB,EAAE;IACpB;IAEA,MAAM,IAAIC,QAAQ,CAACC,MAAQlE,IAAImE,WAAW,CAAC,SAASD;IAEpDvF,uBAAuBe,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIoE,YAAY,sBAAhBpE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;AACF;AAEA,SAASwE,iBACPxE,KAAU;IAEV,OAAOA,MAAMyE,IAAI,KAAK;AACxB;AAEA,2EAA2E,GAC3E,SAASC,aAAa,EAAE1E,KAAK,EAAEH,WAAW,EAAyC;IACjF,wGAAwG;IACxG,IAAIC;IACJ,IAAI0E,iBAAiBxE,UAAUA,MAAM2E,QAAQ,EAAE;QAC7C,qCAAqC;QACrC7E,QAAQ;YACN;gBACE8E,MAAMC,eAAI,CAAC3F,IAAI,CAACW,aAAaG,MAAM2E,QAAQ;gBAC3CjC,YAAY;gBACZoC,WAAW,EAAE;gBACb,qBAAqB;gBACrBC,YAAY/E,MAAM+E,UAAU;gBAC5BvD,QAAQxB,MAAMwB,MAAM;YACtB;SACD;IACH,OAAO,IAAI,sBAAsBxB,SAAS,OAAOA,MAAMgF,gBAAgB,KAAK,UAAU;QACpF,kEAAkE;QAClElF,QAAQ;YACN;gBACE8E,MAAM5E,MAAMgF,gBAAgB;gBAC5BtC,YAAY;gBACZoC,WAAW,EAAE;gBACb,qBAAqB;gBACrBC,YAAY;gBACZvD,QAAQ;YACV;SACD;IACH,OAAO;QACL1B,QAAQ+D,gBAAgBhE,aAAaG,MAAMF,KAAK;IAClD;IAEA,OAAO,IAAIgE,oBAAS,CAAC;QACnBC,OAAO;QACP1D,SAAS;YACPc,SAASnB,MAAMK,OAAO;YACtB2D,eAAe,EAAE;QACnB;QACAC,kBAAkB;QAClBnE;QACAoE,UAAU;QACVC,gBAAgB,EAAE;IACpB;AACF;AAGO,eAAetF,mBAAmB,EACvCmB,KAAK,EACLH,WAAW,EAIZ;QAMUM,yBAAAA;IALT,MAAMA,MAAMuE,aAAa;QAAE7E;QAAaG;IAAM;IAE9C,MAAM,IAAIoE,QAAc,CAACC,MAAQlE,IAAImE,WAAW,CAAC,SAAS,IAAMD;IAEhEvF,uBAAuBe,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIoE,YAAY,sBAAhBpE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;AACF;AAGO,eAAetB,yBAAyB,EAC7CsB,KAAK,EACLH,WAAW,EACXoF,UAAU,EAKX;QAMU9E,yBAAAA;IALT,MAAMA,MAAMuE,aAAa;QAAE7E;QAAaG;IAAM;IAE9C,MAAM,IAAIoE,QAAc,CAACC,MAAQlE,IAAImE,WAAW,CAAC,SAAS,IAAMD;IAEhEvF,uBAAuBe,aAAa;QAClCC,OAAOK,EAAAA,oBAAAA,IAAIoE,YAAY,sBAAhBpE,0BAAAA,kBAAkBL,KAAK,qBAAvBK,wBAAyBL,KAAK,KAAI,EAAE;QAC3CC,WAAWI,IAAIJ,SAAS;QACxBC;IACF;IAEA,IAAI,aAAaG,OAAO,aAAaA,IAAIE,OAAO,IAAI,OAAOF,IAAIE,OAAO,CAACc,OAAO,KAAK,UAAU;QAC3FhB,IAAIE,OAAO,CAACc,OAAO,GAAG+D,IAAAA,eAAS,EAAC/E,IAAIE,OAAO,CAACc,OAAO;IACrD;IAEA,MAAMgE,gBAAgB;QACpBC,kBAAkB;QAClBC,YAAY;QACZ3E,MAAM;YAACP;SAAI;IACb;IACA,MAAMmF,OAAO,CAAC,yLAAyL,EAAEC,KAAKC,SAAS,CACrNL,eACA,uBAAuB,CAAC;IAE1B,MAAMM,oBAAoB,MAAMC,IAAAA,kDAAwB,EACtD7F,aACA,wBAAwB;IACxB,IACA8F,IAAAA,sBAAW,EAAC9F,aAAa,uBACzB;QACE+F,MAAM;QACNC,UAAU;QACVC,QAAQ;QACRC,UAAU;QACVC,aAAa;QACbC,SAAS;QACThB;QACAiB,aAAa;QACbC,eAAe;IACjB;IAGF,MAAMC,aAAad,KAAKe,OAAO,CAAC,WAAW,CAAC,YAAY,EAAEZ,kBAAkB,iBAAiB,CAAC;IAC9F,OAAOW;AACT;AAEA,SAASvC,gBACPhE,WAAmB,EACnBC,KAAc;IAEd,IAAIA,SAAS,MAAM;QACjB,OAAO,EAAE;IACX;IACA,IAAIb,MAAMqH,OAAO,CAACxG,QAAQ;QACxB,OAAOA;IACT;IAEA,MAAMyG,aAAaC,IAAAA,2BAAkB,EAAC3G;IAEtC,OAAO4G,IAAAA,yBAAK,EAAC3G,OACVyC,GAAG,CAAC,CAACC;QACJ,wGAAwG;QAExG,IAAIA,MAAMoC,IAAI,EAAE;YACd,6IAA6I;YAC7I,IAAIpC,MAAMoC,IAAI,CAAC8B,UAAU,CAAC,QAAQlE,MAAMoC,IAAI,CAAC+B,QAAQ,CAAC,cAAc,CAACC,SAASpE,MAAMoC,IAAI,GAAG;gBACzF,oDAAoD;gBACpDpC,MAAMoC,IAAI,GAAG,4BAA4BC,eAAI,CAACgC,QAAQ,CAACN,YAAY/D,MAAMoC,IAAI;YAC/E;QACF;QAEA,OAAO;YACL,GAAGpC,KAAK;YACRhB,QAAQgB,MAAMhB,MAAM,IAAI,OAAOgB,MAAMhB,MAAM,GAAG,IAAI;QACpD;IACF,GACC7B,MAAM,CAAC,CAAC6C,QAAUA,MAAMoC,IAAI,IAAI,CAACpC,MAAMoC,IAAI,CAAC+B,QAAQ,CAAC;AAC1D;AAEA,SAASC,SAASE,GAAW;IAC3B,IAAI;QACF,kCAAkC;QAClC,IAAIC,IAAID;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}