import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { UserRole } from '@prisma/client';

export interface UpdateUserDto {
  phone?: string;
  email?: string;
  fullName?: string;
}

export interface CreateDriverProfileDto {
  userId: string;
  vehicleType?: string;
  licensePlate?: string;
}

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findAll(role?: UserRole) {
    const where = role ? { role } : {};
    
    return this.prisma.user.findMany({
      where,
      select: {
        id: true,
        phone: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        clientProfile: {
          select: {
            id: true,
            fullName: true,
          },
        },
        driverProfile: {
          select: {
            id: true,
            isVerified: true,
            isOnline: true,
            vehicle: true,
          },
        },
        partnerStore: {
          select: {
            id: true,
            name: true,
            isOpen: true,
          },
        },
      },
    });
  }

  async findOne(id: string) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        phone: true,
        email: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        clientProfile: {
          select: {
            id: true,
            fullName: true,
          },
        },
        driverProfile: {
          select: {
            id: true,
            isVerified: true,
            isOnline: true,
            currentLocation: true,
            vehicle: true,
            wallet: true,
            documents: true,
          },
        },
        partnerStore: {
          select: {
            id: true,
            name: true,
            address: true,
            isOpen: true,
            products: {
              take: 10,
              select: {
                id: true,
                name: true,
                price: true,
                inStock: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    return user;
  }

  async updateProfile(userId: string, updateData: UpdateUserDto, currentUserId: string, currentUserRole: UserRole) {
    // Vérifier les permissions
    if (currentUserRole !== UserRole.ADMIN && currentUserId !== userId) {
      throw new ForbiddenException('Vous ne pouvez modifier que votre propre profil');
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('Utilisateur non trouvé');
    }

    // Mettre à jour l'utilisateur
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        phone: updateData.phone,
        email: updateData.email,
      },
      select: {
        id: true,
        phone: true,
        email: true,
        role: true,
        updatedAt: true,
      },
    });

    // Mettre à jour le profil client si nécessaire
    if (user.role === UserRole.CLIENT && updateData.fullName) {
      await this.prisma.clientProfile.upsert({
        where: { userId },
        update: { fullName: updateData.fullName },
        create: { userId, fullName: updateData.fullName },
      });
    }

    return updatedUser;
  }

  async createDriverProfile(createDriverDto: CreateDriverProfileDto) {
    const { userId, vehicleType, licensePlate } = createDriverDto;

    // Vérifier que l'utilisateur existe et est un driver
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== UserRole.DRIVER) {
      throw new NotFoundException('Utilisateur driver non trouvé');
    }

    // Créer le profil driver
    const driverProfile = await this.prisma.driverProfile.create({
      data: {
        userId,
      },
    });

    // Créer le véhicule si les données sont fournies
    if (vehicleType && licensePlate) {
      await this.prisma.vehicle.create({
        data: {
          driverId: driverProfile.id,
          type: vehicleType,
          licensePlate,
        },
      });
    }

    // Créer le wallet
    await this.prisma.wallet.create({
      data: {
        driverId: driverProfile.id,
      },
    });

    return driverProfile;
  }

  async updateDriverStatus(driverId: string, isOnline: boolean, currentLocation?: any) {
    const driverProfile = await this.prisma.driverProfile.findUnique({
      where: { id: driverId },
    });

    if (!driverProfile) {
      throw new NotFoundException('Profil driver non trouvé');
    }

    return this.prisma.driverProfile.update({
      where: { id: driverId },
      data: {
        isOnline,
        currentLocation,
      },
    });
  }

  async getDrivers(isOnline?: boolean) {
    const where = isOnline !== undefined ? { isOnline } : {};

    return this.prisma.driverProfile.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            phone: true,
            email: true,
          },
        },
        vehicle: true,
        wallet: true,
      },
    });
  }
}
