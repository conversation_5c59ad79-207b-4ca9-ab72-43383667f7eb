{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/pageIsSupported.ts"], "sourcesContent": ["import { Page } from './types';\n\ntype DevicePageInternal = Pick<Page, 'title' | 'capabilities'>;\ntype DevicePageResponse = {\n  title: string;\n  reactNative?: {\n    logicalDeviceId: string;\n    capabilities: Page['capabilities'];\n  };\n};\n\n/**\n * Determine if a page debug target is supported by our debugging extensions.\n * If it's not, the extended CDP handlers will not be enabled.\n */\nexport function pageIsSupported(page: DevicePageInternal | DevicePageResponse): boolean {\n  // @ts-expect-error No good way to filter this properly in TypeScript\n  const capabilities = page.capabilities ?? page.reactNative?.capabilities ?? {};\n\n  return (\n    page.title === 'React Native Experimental (Improved Chrome Reloads)' ||\n    capabilities.nativePageReloads === true\n  );\n}\n"], "names": ["pageIsSupported", "page", "capabilities", "reactNative", "title", "nativePageReloads"], "mappings": ";;;;+BAegBA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,IAA6C;QAEjCA;IAD1C,qEAAqE;IACrE,MAAMC,eAAeD,KAAKC,YAAY,MAAID,oBAAAA,KAAKE,WAAW,qBAAhBF,kBAAkBC,YAAY,KAAI,CAAC;IAE7E,OACED,KAAKG,KAAK,KAAK,yDACfF,aAAaG,iBAAiB,KAAK;AAEvC"}