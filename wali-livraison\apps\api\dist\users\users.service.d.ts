import { PrismaService } from '../prisma/prisma.service';
import { UserRole } from '@prisma/client';
export interface UpdateUserDto {
    phone?: string;
    email?: string;
    fullName?: string;
}
export interface CreateDriverProfileDto {
    userId: string;
    vehicleType?: string;
    licensePlate?: string;
}
export declare class UsersService {
    private prisma;
    constructor(prisma: PrismaService);
    findAll(role?: UserRole): Promise<{
        clientProfile: {
            id: string;
            fullName: string;
        } | null;
        driverProfile: {
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            id: string;
            isVerified: boolean;
            isOnline: boolean;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        partnerStore: {
            id: string;
            name: string;
            isOpen: boolean;
        } | null;
    }[]>;
    findOne(id: string): Promise<{
        clientProfile: {
            id: string;
            fullName: string;
        } | null;
        driverProfile: {
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            wallet: {
                id: string;
                driverId: string;
                balance: number;
            } | null;
            id: string;
            isVerified: boolean;
            isOnline: boolean;
            currentLocation: import("@prisma/client/runtime/library").JsonValue;
            documents: {
                id: string;
                driverId: string;
                type: string;
                url: string;
                isValidated: boolean;
            }[];
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        partnerStore: {
            id: string;
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
            isOpen: boolean;
            products: {
                id: string;
                price: number;
                name: string;
                inStock: boolean;
            }[];
        } | null;
    }>;
    updateProfile(userId: string, updateData: UpdateUserDto, currentUserId: string, currentUserRole: UserRole): Promise<{
        id: string;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
    }>;
    createDriverProfile(createDriverDto: CreateDriverProfileDto): Promise<{
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
    updateDriverStatus(driverId: string, isOnline: boolean, currentLocation?: any): Promise<{
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
    getDrivers(isOnline?: boolean): Promise<({
        user: {
            id: string;
            phone: string | null;
            email: string | null;
        };
        vehicle: {
            id: string;
            driverId: string;
            type: string;
            licensePlate: string;
        } | null;
        wallet: {
            id: string;
            driverId: string;
            balance: number;
        } | null;
    } & {
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    })[]>;
}
