{"version": 3, "names": ["_context", "require", "_index", "_t", "_context2", "VISITOR_KEYS", "_visitPaths", "ctx", "paths", "queue", "priorityQueue", "visited", "Set", "stop", "visitIndex", "length", "path", "resync", "call", "contexts", "pushContext", "key", "node", "has", "add", "_visit", "i", "popContext", "_opts$denylist", "opts", "denylist", "blacklist", "includes", "type", "shouldSkip", "shouldStop", "_call", "enter", "_opts$node$type", "_traverse", "scope", "state", "<PERSON><PERSON><PERSON><PERSON>", "exit", "_opts$node$type2", "visitSelf", "keys", "TraversalContext", "parent<PERSON><PERSON>", "prop", "Array", "isArray", "child<PERSON><PERSON>", "NodePath", "get", "parentPath", "parent", "container", "<PERSON><PERSON><PERSON>", "push", "traverseNode", "context", "visitQueue", "visit"], "sources": ["../src/traverse-node.ts"], "sourcesContent": ["import TraversalContext from \"./context.ts\";\nimport type { ExplodedTraverseOptions } from \"./index.ts\";\nimport NodePath from \"./path/index.ts\";\nimport type Scope from \"./scope/index.ts\";\nimport type * as t from \"@babel/types\";\nimport { VISITOR_KEYS } from \"@babel/types\";\nimport { _call, popContext, pushContext, resync } from \"./path/context.ts\";\n\nfunction _visitPaths(ctx: TraversalContext, paths: NodePath[]): boolean {\n  // set queue\n  ctx.queue = paths;\n  ctx.priorityQueue = [];\n\n  const visited = new Set();\n  let stop = false;\n  let visitIndex = 0;\n\n  for (; visitIndex < paths.length; ) {\n    const path = paths[visitIndex];\n    visitIndex++;\n\n    resync.call(path);\n\n    if (\n      path.contexts.length === 0 ||\n      path.contexts[path.contexts.length - 1] !== ctx\n    ) {\n      // The context might already have been pushed when this path was inserted and queued.\n      // If we always re-pushed here, we could get duplicates and risk leaving contexts\n      // on the stack after the traversal has completed, which could break things.\n      pushContext.call(path, ctx);\n    }\n\n    // this path no longer belongs to the tree\n    if (path.key === null) continue;\n\n    // ensure we don't visit the same node twice\n    const { node } = path;\n    if (visited.has(node)) continue;\n    if (node) visited.add(node);\n\n    if (_visit(ctx, path)) {\n      stop = true;\n      break;\n    }\n\n    if (ctx.priorityQueue.length) {\n      stop = _visitPaths(ctx, ctx.priorityQueue);\n      ctx.priorityQueue = [];\n      ctx.queue = paths;\n      if (stop) break;\n    }\n  }\n\n  // pop contexts\n  for (let i = 0; i < visitIndex; i++) {\n    popContext.call(paths[i]);\n  }\n\n  // clear queue\n  ctx.queue = null;\n\n  return stop;\n}\n\nfunction _visit(ctx: TraversalContext, path: NodePath) {\n  const node = path.node;\n  if (!node) {\n    return false;\n  }\n  const opts = ctx.opts;\n\n  // @ts-expect-error TODO(Babel 8): Remove blacklist\n  const denylist = opts.denylist ?? opts.blacklist;\n  if (denylist?.includes(node.type)) {\n    return false;\n  }\n\n  if (opts.shouldSkip?.(path)) {\n    return false;\n  }\n\n  // Note: We need to check \"this.shouldSkip\" first because\n  // another visitor can set it to true. Usually .shouldSkip is false\n  // before calling the enter visitor, but it can be true in case of\n  // a requeued node (e.g. by .replaceWith()) that is then marked\n  // with .skip().\n  if (path.shouldSkip) return path.shouldStop;\n\n  if (_call.call(path, opts.enter)) return path.shouldStop;\n  if (path.node) {\n    if (_call.call(path, opts[node.type]?.enter)) return path.shouldStop;\n  }\n\n  path.shouldStop = _traverse(\n    path.node,\n    opts,\n    path.scope,\n    ctx.state,\n    path,\n    path.skipKeys,\n  );\n\n  if (path.node) {\n    if (_call.call(path, opts.exit)) return true;\n  }\n  if (path.node) {\n    _call.call(path, opts[node.type]?.exit);\n  }\n\n  return path.shouldStop;\n}\n\nfunction _traverse<S>(\n  node: t.Node,\n  opts: ExplodedTraverseOptions<S>,\n  scope?: Scope,\n  state?: S,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n  visitSelf?: boolean,\n) {\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys?.length) return false;\n\n  const ctx = new TraversalContext(scope, opts, state, path);\n  if (visitSelf) {\n    if (skipKeys?.[path.parentKey]) return false;\n    return _visitPaths(ctx, [path]);\n  }\n\n  for (const key of keys) {\n    if (skipKeys?.[key]) continue;\n    // @ts-expect-error key must present in node\n    const prop = node[key];\n    if (!prop) continue;\n\n    if (Array.isArray(prop)) {\n      if (!prop.length) continue;\n      const paths = [];\n      for (let i = 0; i < prop.length; i++) {\n        const childPath = NodePath.get({\n          parentPath: path,\n          parent: node,\n          container: prop,\n          key: i,\n          listKey: key,\n        });\n        paths.push(childPath);\n      }\n      if (_visitPaths(ctx, paths)) return true;\n    } else {\n      if (\n        _visitPaths(ctx, [\n          NodePath.get({\n            parentPath: path,\n            parent: node,\n            container: node,\n            key,\n            listKey: null,\n          }),\n        ])\n      ) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\n/**\n * Traverse the children of given node\n * @param {Node} node\n * @param {TraverseOptions} opts The traverse options used to create a new traversal context\n * @param {scope} scope A traversal scope used to create a new traversal context. When opts.noScope is true, scope should not be provided\n * @param {any} state A user data storage provided as the second callback argument for traversal visitors\n * @param {NodePath} path A NodePath of given node\n * @param {Record<string, boolean>} skipKeys A map from key names to whether that should be skipped during traversal. The skipKeys are applied to every descendants\n * @returns {boolean} Whether the traversal stops early\n\n * @note This function does not visit the given `node`.\n */\nexport function traverseNode<S = unknown>(\n  node: t.Node,\n  opts: ExplodedTraverseOptions<S>,\n  scope?: Scope,\n  state?: S,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n  visitSelf?: boolean,\n): boolean {\n  if (process.env.BABEL_8_BREAKING) {\n    return _traverse(node, opts, scope, state, path, skipKeys, visitSelf);\n  }\n\n  const keys = VISITOR_KEYS[node.type];\n  if (!keys) return false;\n\n  const context = new TraversalContext<S>(scope, opts, state, path);\n  if (visitSelf) {\n    if (skipKeys?.[path.parentKey]) return false;\n    return context.visitQueue([path]);\n  }\n\n  for (const key of keys) {\n    if (skipKeys?.[key]) continue;\n    if (context.visit(node, key)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAGA,IAAAE,EAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AAA2E;EADlEI;AAAY,IAAAF,EAAA;AAGrB,SAASG,WAAWA,CAACC,GAAqB,EAAEC,KAAiB,EAAW;EAEtED,GAAG,CAACE,KAAK,GAAGD,KAAK;EACjBD,GAAG,CAACG,aAAa,GAAG,EAAE;EAEtB,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB,IAAIC,IAAI,GAAG,KAAK;EAChB,IAAIC,UAAU,GAAG,CAAC;EAElB,OAAOA,UAAU,GAAGN,KAAK,CAACO,MAAM,GAAI;IAClC,MAAMC,IAAI,GAAGR,KAAK,CAACM,UAAU,CAAC;IAC9BA,UAAU,EAAE;IAEZG,gBAAM,CAACC,IAAI,CAACF,IAAI,CAAC;IAEjB,IACEA,IAAI,CAACG,QAAQ,CAACJ,MAAM,KAAK,CAAC,IAC1BC,IAAI,CAACG,QAAQ,CAACH,IAAI,CAACG,QAAQ,CAACJ,MAAM,GAAG,CAAC,CAAC,KAAKR,GAAG,EAC/C;MAIAa,qBAAW,CAACF,IAAI,CAACF,IAAI,EAAET,GAAG,CAAC;IAC7B;IAGA,IAAIS,IAAI,CAACK,GAAG,KAAK,IAAI,EAAE;IAGvB,MAAM;MAAEC;IAAK,CAAC,GAAGN,IAAI;IACrB,IAAIL,OAAO,CAACY,GAAG,CAACD,IAAI,CAAC,EAAE;IACvB,IAAIA,IAAI,EAAEX,OAAO,CAACa,GAAG,CAACF,IAAI,CAAC;IAE3B,IAAIG,MAAM,CAAClB,GAAG,EAAES,IAAI,CAAC,EAAE;MACrBH,IAAI,GAAG,IAAI;MACX;IACF;IAEA,IAAIN,GAAG,CAACG,aAAa,CAACK,MAAM,EAAE;MAC5BF,IAAI,GAAGP,WAAW,CAACC,GAAG,EAAEA,GAAG,CAACG,aAAa,CAAC;MAC1CH,GAAG,CAACG,aAAa,GAAG,EAAE;MACtBH,GAAG,CAACE,KAAK,GAAGD,KAAK;MACjB,IAAIK,IAAI,EAAE;IACZ;EACF;EAGA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,UAAU,EAAEY,CAAC,EAAE,EAAE;IACnCC,oBAAU,CAACT,IAAI,CAACV,KAAK,CAACkB,CAAC,CAAC,CAAC;EAC3B;EAGAnB,GAAG,CAACE,KAAK,GAAG,IAAI;EAEhB,OAAOI,IAAI;AACb;AAEA,SAASY,MAAMA,CAAClB,GAAqB,EAAES,IAAc,EAAE;EAAA,IAAAY,cAAA;EACrD,MAAMN,IAAI,GAAGN,IAAI,CAACM,IAAI;EACtB,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,KAAK;EACd;EACA,MAAMO,IAAI,GAAGtB,GAAG,CAACsB,IAAI;EAGrB,MAAMC,QAAQ,IAAAF,cAAA,GAAGC,IAAI,CAACC,QAAQ,YAAAF,cAAA,GAAIC,IAAI,CAACE,SAAS;EAChD,IAAID,QAAQ,YAARA,QAAQ,CAAEE,QAAQ,CAACV,IAAI,CAACW,IAAI,CAAC,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,IAAIJ,IAAI,CAACK,UAAU,YAAfL,IAAI,CAACK,UAAU,CAAGlB,IAAI,CAAC,EAAE;IAC3B,OAAO,KAAK;EACd;EAOA,IAAIA,IAAI,CAACkB,UAAU,EAAE,OAAOlB,IAAI,CAACmB,UAAU;EAE3C,IAAIC,eAAK,CAAClB,IAAI,CAACF,IAAI,EAAEa,IAAI,CAACQ,KAAK,CAAC,EAAE,OAAOrB,IAAI,CAACmB,UAAU;EACxD,IAAInB,IAAI,CAACM,IAAI,EAAE;IAAA,IAAAgB,eAAA;IACb,IAAIF,eAAK,CAAClB,IAAI,CAACF,IAAI,GAAAsB,eAAA,GAAET,IAAI,CAACP,IAAI,CAACW,IAAI,CAAC,qBAAfK,eAAA,CAAiBD,KAAK,CAAC,EAAE,OAAOrB,IAAI,CAACmB,UAAU;EACtE;EAEAnB,IAAI,CAACmB,UAAU,GAAGI,SAAS,CACzBvB,IAAI,CAACM,IAAI,EACTO,IAAI,EACJb,IAAI,CAACwB,KAAK,EACVjC,GAAG,CAACkC,KAAK,EACTzB,IAAI,EACJA,IAAI,CAAC0B,QACP,CAAC;EAED,IAAI1B,IAAI,CAACM,IAAI,EAAE;IACb,IAAIc,eAAK,CAAClB,IAAI,CAACF,IAAI,EAAEa,IAAI,CAACc,IAAI,CAAC,EAAE,OAAO,IAAI;EAC9C;EACA,IAAI3B,IAAI,CAACM,IAAI,EAAE;IAAA,IAAAsB,gBAAA;IACbR,eAAK,CAAClB,IAAI,CAACF,IAAI,GAAA4B,gBAAA,GAAEf,IAAI,CAACP,IAAI,CAACW,IAAI,CAAC,qBAAfW,gBAAA,CAAiBD,IAAI,CAAC;EACzC;EAEA,OAAO3B,IAAI,CAACmB,UAAU;AACxB;AAEA,SAASI,SAASA,CAChBjB,IAAY,EACZO,IAAgC,EAChCW,KAAa,EACbC,KAAS,EACTzB,IAAe,EACf0B,QAAkC,EAClCG,SAAmB,EACnB;EACA,MAAMC,IAAI,GAAGzC,YAAY,CAACiB,IAAI,CAACW,IAAI,CAAC;EACpC,IAAI,EAACa,IAAI,YAAJA,IAAI,CAAE/B,MAAM,GAAE,OAAO,KAAK;EAE/B,MAAMR,GAAG,GAAG,IAAIwC,gBAAgB,CAACP,KAAK,EAAEX,IAAI,EAAEY,KAAK,EAAEzB,IAAI,CAAC;EAC1D,IAAI6B,SAAS,EAAE;IACb,IAAIH,QAAQ,YAARA,QAAQ,CAAG1B,IAAI,CAACgC,SAAS,CAAC,EAAE,OAAO,KAAK;IAC5C,OAAO1C,WAAW,CAACC,GAAG,EAAE,CAACS,IAAI,CAAC,CAAC;EACjC;EAEA,KAAK,MAAMK,GAAG,IAAIyB,IAAI,EAAE;IACtB,IAAIJ,QAAQ,YAARA,QAAQ,CAAGrB,GAAG,CAAC,EAAE;IAErB,MAAM4B,IAAI,GAAG3B,IAAI,CAACD,GAAG,CAAC;IACtB,IAAI,CAAC4B,IAAI,EAAE;IAEX,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACvB,IAAI,CAACA,IAAI,CAAClC,MAAM,EAAE;MAClB,MAAMP,KAAK,GAAG,EAAE;MAChB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,IAAI,CAAClC,MAAM,EAAEW,CAAC,EAAE,EAAE;QACpC,MAAM0B,SAAS,GAAGC,cAAQ,CAACC,GAAG,CAAC;UAC7BC,UAAU,EAAEvC,IAAI;UAChBwC,MAAM,EAAElC,IAAI;UACZmC,SAAS,EAAER,IAAI;UACf5B,GAAG,EAAEK,CAAC;UACNgC,OAAO,EAAErC;QACX,CAAC,CAAC;QACFb,KAAK,CAACmD,IAAI,CAACP,SAAS,CAAC;MACvB;MACA,IAAI9C,WAAW,CAACC,GAAG,EAAEC,KAAK,CAAC,EAAE,OAAO,IAAI;IAC1C,CAAC,MAAM;MACL,IACEF,WAAW,CAACC,GAAG,EAAE,CACf8C,cAAQ,CAACC,GAAG,CAAC;QACXC,UAAU,EAAEvC,IAAI;QAChBwC,MAAM,EAAElC,IAAI;QACZmC,SAAS,EAAEnC,IAAI;QACfD,GAAG;QACHqC,OAAO,EAAE;MACX,CAAC,CAAC,CACH,CAAC,EACF;QACA,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,KAAK;AACd;AAcO,SAASE,YAAYA,CAC1BtC,IAAY,EACZO,IAAgC,EAChCW,KAAa,EACbC,KAAS,EACTzB,IAAe,EACf0B,QAAkC,EAClCG,SAAmB,EACV;EAAA;EAKT,MAAMC,IAAI,GAAGzC,YAAY,CAACiB,IAAI,CAACW,IAAI,CAAC;EACpC,IAAI,CAACa,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAMe,OAAO,GAAG,IAAId,gBAAgB,CAAIP,KAAK,EAAEX,IAAI,EAAEY,KAAK,EAAEzB,IAAI,CAAC;EACjE,IAAI6B,SAAS,EAAE;IACb,IAAIH,QAAQ,YAARA,QAAQ,CAAG1B,IAAI,CAACgC,SAAS,CAAC,EAAE,OAAO,KAAK;IAC5C,OAAOa,OAAO,CAACC,UAAU,CAAC,CAAC9C,IAAI,CAAC,CAAC;EACnC;EAEA,KAAK,MAAMK,GAAG,IAAIyB,IAAI,EAAE;IACtB,IAAIJ,QAAQ,YAARA,QAAQ,CAAGrB,GAAG,CAAC,EAAE;IACrB,IAAIwC,OAAO,CAACE,KAAK,CAACzC,IAAI,EAAED,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}