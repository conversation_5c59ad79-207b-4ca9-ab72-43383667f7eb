{"version": 3, "sources": ["../../../../src/run/android/runAndroidAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { resolveInstallApkNameAsync } from './resolveInstallApkName';\nimport { Options, ResolvedOptions, resolveOptionsAsync } from './resolveOptions';\nimport { exportEagerAsync } from '../../export/embed/exportEager';\nimport { Log } from '../../log';\nimport type { AndroidOpenInCustomProps } from '../../start/platforms/android/AndroidPlatformManager';\nimport { assembleAsync, installAsync } from '../../start/platforms/android/gradle';\nimport { resolveBuildCache, uploadBuildCache } from '../../utils/build-cache-providers';\nimport { CommandError } from '../../utils/errors';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { ensurePortAvailabilityAsync } from '../../utils/port';\nimport { getSchemesForAndroidAsync } from '../../utils/scheme';\nimport { ensureNativeProjectAsync } from '../ensureNativeProject';\nimport { logProjectLogsLocation } from '../hints';\nimport { startBundlerAsync } from '../startBundler';\n\nconst debug = require('debug')('expo:run:android');\n\nexport async function runAndroidAsync(projectRoot: string, { install, ...options }: Options) {\n  // NOTE: This is a guess, the developer can overwrite with `NODE_ENV`.\n  const isProduction = options.variant?.toLowerCase().endsWith('release');\n  setNodeEnv(isProduction ? 'production' : 'development');\n  require('@expo/env').load(projectRoot);\n\n  await ensureNativeProjectAsync(projectRoot, { platform: 'android', install });\n\n  const props = await resolveOptionsAsync(projectRoot, options);\n\n  if (!options.binary && props.buildCacheProvider) {\n    const localPath = await resolveBuildCache({\n      projectRoot,\n      platform: 'android',\n      provider: props.buildCacheProvider,\n      runOptions: options,\n    });\n    if (localPath) {\n      options.binary = localPath;\n    }\n  }\n\n  debug('Package name: ' + props.packageName);\n  Log.log('› Building app...');\n\n  const androidProjectRoot = path.join(projectRoot, 'android');\n\n  let shouldUpdateBuildCache = false;\n  if (!options.binary) {\n    let eagerBundleOptions: string | undefined;\n\n    if (isProduction) {\n      eagerBundleOptions = JSON.stringify(\n        await exportEagerAsync(projectRoot, {\n          dev: false,\n          platform: 'android',\n        })\n      );\n    }\n\n    await assembleAsync(androidProjectRoot, {\n      variant: props.variant,\n      port: props.port,\n      appName: props.appName,\n      buildCache: props.buildCache,\n      architectures: props.architectures,\n      eagerBundleOptions,\n    });\n    shouldUpdateBuildCache = true;\n\n    // Ensure the port hasn't become busy during the build.\n    if (props.shouldStartBundler && !(await ensurePortAvailabilityAsync(projectRoot, props))) {\n      props.shouldStartBundler = false;\n    }\n  }\n\n  const manager = await startBundlerAsync(projectRoot, {\n    port: props.port,\n    // If a scheme is specified then use that instead of the package name.\n    scheme: (await getSchemesForAndroidAsync(projectRoot))?.[0],\n    headless: !props.shouldStartBundler,\n  });\n\n  if (!options.binary) {\n    // Find the APK file path\n    const apkFile = await resolveInstallApkNameAsync(props.device.device, props);\n    if (apkFile) {\n      // Attempt to install the APK from the file path\n      options.binary = path.join(props.apkVariantDirectory, apkFile);\n    }\n  }\n\n  if (options.binary) {\n    // Attempt to install the APK from the file path\n    const binaryPath = path.join(options.binary);\n\n    if (!fs.existsSync(binaryPath)) {\n      throw new CommandError(`The path to the custom Android binary does not exist: ${binaryPath}`);\n    }\n    Log.log(chalk.gray`\\u203A Installing ${binaryPath}`);\n    await props.device.installAppAsync(binaryPath);\n  } else {\n    await installAppAsync(androidProjectRoot, props);\n  }\n\n  await manager.getDefaultDevServer().openCustomRuntimeAsync<AndroidOpenInCustomProps>(\n    'emulator',\n    {\n      applicationId: props.packageName,\n      customAppId: props.customAppId,\n      launchActivity: props.launchActivity,\n    },\n    { device: props.device.device }\n  );\n\n  if (props.shouldStartBundler) {\n    logProjectLogsLocation();\n  } else {\n    await manager.stopAsync();\n  }\n\n  if (options.binary && shouldUpdateBuildCache && props.buildCacheProvider) {\n    await uploadBuildCache({\n      projectRoot,\n      platform: 'android',\n      provider: props.buildCacheProvider,\n      buildPath: options.binary,\n      runOptions: options,\n    });\n  }\n}\n\nasync function installAppAsync(androidProjectRoot: string, props: ResolvedOptions) {\n  // If we cannot resolve the APK file path then we can attempt to install using Gradle.\n  // This offers more advanced resolution that we may not have first class support for.\n  Log.log('› Failed to locate binary file, installing with Gradle...');\n  await installAsync(androidProjectRoot, {\n    variant: props.variant ?? 'debug',\n    appName: props.appName ?? 'app',\n    port: props.port,\n  });\n}\n"], "names": ["runAndroidAsync", "debug", "require", "projectRoot", "install", "options", "isProduction", "variant", "toLowerCase", "endsWith", "setNodeEnv", "load", "ensureNativeProjectAsync", "platform", "props", "resolveOptionsAsync", "binary", "buildCacheProvider", "localPath", "resolveBuildCache", "provider", "runOptions", "packageName", "Log", "log", "androidProjectRoot", "path", "join", "shouldUpdateBuildCache", "eagerBundleOptions", "JSON", "stringify", "exportEagerAsync", "dev", "assembleAsync", "port", "appName", "buildCache", "architectures", "shouldStartBundler", "ensurePortAvailabilityAsync", "manager", "startBundlerAsync", "scheme", "getSchemesForAndroidAsync", "headless", "apkFile", "resolveInstallApkNameAsync", "device", "apkVariantDirectory", "binaryPath", "fs", "existsSync", "CommandError", "chalk", "gray", "installAppAsync", "getDefaultDevServer", "openCustomRuntimeAsync", "applicationId", "customAppId", "launchActivity", "logProjectLogsLocation", "stopAsync", "uploadBuildCache", "buildPath", "installAsync"], "mappings": ";;;;+BAqBsBA;;;eAAAA;;;;gEArBJ;;;;;;;gEACH;;;;;;;gEACE;;;;;;uCAE0B;gCACmB;6BAC7B;qBACb;wBAEwB;qCACQ;wBACvB;yBACF;sBACiB;wBACF;qCACD;uBACF;8BACL;;;;;;AAElC,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,gBAAgBG,WAAmB,EAAE,EAAEC,OAAO,EAAE,GAAGC,SAAkB;QAEpEA,kBAyDV;IA1DX,sEAAsE;IACtE,MAAMC,gBAAeD,mBAAAA,QAAQE,OAAO,qBAAfF,iBAAiBG,WAAW,GAAGC,QAAQ,CAAC;IAC7DC,IAAAA,mBAAU,EAACJ,eAAe,eAAe;IACzCJ,QAAQ,aAAaS,IAAI,CAACR;IAE1B,MAAMS,IAAAA,6CAAwB,EAACT,aAAa;QAAEU,UAAU;QAAWT;IAAQ;IAE3E,MAAMU,QAAQ,MAAMC,IAAAA,mCAAmB,EAACZ,aAAaE;IAErD,IAAI,CAACA,QAAQW,MAAM,IAAIF,MAAMG,kBAAkB,EAAE;QAC/C,MAAMC,YAAY,MAAMC,IAAAA,sCAAiB,EAAC;YACxChB;YACAU,UAAU;YACVO,UAAUN,MAAMG,kBAAkB;YAClCI,YAAYhB;QACd;QACA,IAAIa,WAAW;YACbb,QAAQW,MAAM,GAAGE;QACnB;IACF;IAEAjB,MAAM,mBAAmBa,MAAMQ,WAAW;IAC1CC,QAAG,CAACC,GAAG,CAAC;IAER,MAAMC,qBAAqBC,eAAI,CAACC,IAAI,CAACxB,aAAa;IAElD,IAAIyB,yBAAyB;IAC7B,IAAI,CAACvB,QAAQW,MAAM,EAAE;QACnB,IAAIa;QAEJ,IAAIvB,cAAc;YAChBuB,qBAAqBC,KAAKC,SAAS,CACjC,MAAMC,IAAAA,6BAAgB,EAAC7B,aAAa;gBAClC8B,KAAK;gBACLpB,UAAU;YACZ;QAEJ;QAEA,MAAMqB,IAAAA,qBAAa,EAACT,oBAAoB;YACtClB,SAASO,MAAMP,OAAO;YACtB4B,MAAMrB,MAAMqB,IAAI;YAChBC,SAAStB,MAAMsB,OAAO;YACtBC,YAAYvB,MAAMuB,UAAU;YAC5BC,eAAexB,MAAMwB,aAAa;YAClCT;QACF;QACAD,yBAAyB;QAEzB,uDAAuD;QACvD,IAAId,MAAMyB,kBAAkB,IAAI,CAAE,MAAMC,IAAAA,iCAA2B,EAACrC,aAAaW,QAAS;YACxFA,MAAMyB,kBAAkB,GAAG;QAC7B;IACF;IAEA,MAAME,UAAU,MAAMC,IAAAA,+BAAiB,EAACvC,aAAa;QACnDgC,MAAMrB,MAAMqB,IAAI;QAChB,sEAAsE;QACtEQ,MAAM,GAAG,QAAA,MAAMC,IAAAA,iCAAyB,EAACzC,iCAAjC,AAAC,KAA+C,CAAC,EAAE;QAC3D0C,UAAU,CAAC/B,MAAMyB,kBAAkB;IACrC;IAEA,IAAI,CAAClC,QAAQW,MAAM,EAAE;QACnB,yBAAyB;QACzB,MAAM8B,UAAU,MAAMC,IAAAA,iDAA0B,EAACjC,MAAMkC,MAAM,CAACA,MAAM,EAAElC;QACtE,IAAIgC,SAAS;YACX,gDAAgD;YAChDzC,QAAQW,MAAM,GAAGU,eAAI,CAACC,IAAI,CAACb,MAAMmC,mBAAmB,EAAEH;QACxD;IACF;IAEA,IAAIzC,QAAQW,MAAM,EAAE;QAClB,gDAAgD;QAChD,MAAMkC,aAAaxB,eAAI,CAACC,IAAI,CAACtB,QAAQW,MAAM;QAE3C,IAAI,CAACmC,aAAE,CAACC,UAAU,CAACF,aAAa;YAC9B,MAAM,IAAIG,oBAAY,CAAC,CAAC,sDAAsD,EAAEH,YAAY;QAC9F;QACA3B,QAAG,CAACC,GAAG,CAAC8B,gBAAK,CAACC,IAAI,CAAC,kBAAkB,EAAEL,WAAW,CAAC;QACnD,MAAMpC,MAAMkC,MAAM,CAACQ,eAAe,CAACN;IACrC,OAAO;QACL,MAAMM,gBAAgB/B,oBAAoBX;IAC5C;IAEA,MAAM2B,QAAQgB,mBAAmB,GAAGC,sBAAsB,CACxD,YACA;QACEC,eAAe7C,MAAMQ,WAAW;QAChCsC,aAAa9C,MAAM8C,WAAW;QAC9BC,gBAAgB/C,MAAM+C,cAAc;IACtC,GACA;QAAEb,QAAQlC,MAAMkC,MAAM,CAACA,MAAM;IAAC;IAGhC,IAAIlC,MAAMyB,kBAAkB,EAAE;QAC5BuB,IAAAA,6BAAsB;IACxB,OAAO;QACL,MAAMrB,QAAQsB,SAAS;IACzB;IAEA,IAAI1D,QAAQW,MAAM,IAAIY,0BAA0Bd,MAAMG,kBAAkB,EAAE;QACxE,MAAM+C,IAAAA,qCAAgB,EAAC;YACrB7D;YACAU,UAAU;YACVO,UAAUN,MAAMG,kBAAkB;YAClCgD,WAAW5D,QAAQW,MAAM;YACzBK,YAAYhB;QACd;IACF;AACF;AAEA,eAAemD,gBAAgB/B,kBAA0B,EAAEX,KAAsB;IAC/E,sFAAsF;IACtF,qFAAqF;IACrFS,QAAG,CAACC,GAAG,CAAC;IACR,MAAM0C,IAAAA,oBAAY,EAACzC,oBAAoB;QACrClB,SAASO,MAAMP,OAAO,IAAI;QAC1B6B,SAAStB,MAAMsB,OAAO,IAAI;QAC1BD,MAAMrB,MAAMqB,IAAI;IAClB;AACF"}