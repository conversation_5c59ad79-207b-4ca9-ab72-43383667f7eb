{"version": 3, "sources": ["../../../../src/api/user/UserSettings.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport crypto from 'crypto';\nimport { boolish } from 'getenv';\nimport { homedir } from 'os';\nimport * as path from 'path';\n\ntype SessionData = {\n  sessionSecret?: string;\n  userId?: string;\n  username?: string;\n  currentConnection?: 'Username-Password-Authentication' | 'Browser-Flow-Authentication';\n};\n\nexport type UserSettingsData = {\n  auth?: SessionData | null;\n  ignoreBundledBinaries?: string[];\n  PATH?: string;\n  /** Last development code signing ID used for `npx expo run:ios`. */\n  developmentCodeSigningId?: string;\n  /** Unique user ID which is generated anonymously and can be cleared locally. */\n  uuid?: string;\n};\n\n// The ~/.expo directory is used to store authentication sessions,\n// which are shared between EAS CLI and Expo CLI.\nexport function getExpoHomeDirectory() {\n  const home = homedir();\n\n  if (process.env.__UNSAFE_EXPO_HOME_DIRECTORY) {\n    return process.env.__UNSAFE_EXPO_HOME_DIRECTORY;\n  } else if (boolish('EXPO_STAGING', false)) {\n    return path.join(home, '.expo-staging');\n  } else if (boolish('EXPO_LOCAL', false)) {\n    return path.join(home, '.expo-local');\n  }\n  return path.join(home, '.expo');\n}\n\n/** Return the user cache directory. */\nexport function getSettingsDirectory() {\n  return getExpoHomeDirectory();\n}\n\n/** Return the file path of the settings file */\nexport function getSettingsFilePath(): string {\n  return path.join(getExpoHomeDirectory(), 'state.json');\n}\n\n/** Get a new JsonFile instance pointed towards the settings file */\nexport function getSettings(): JsonFile<UserSettingsData> {\n  return new JsonFile<UserSettingsData>(getSettingsFilePath(), {\n    ensureDir: true,\n    jsonParseErrorDefault: {},\n    // This will ensure that an error isn't thrown if the file doesn't exist.\n    cantReadFileDefault: {},\n  });\n}\n\nexport function getAccessToken(): string | null {\n  return process.env.EXPO_TOKEN ?? null;\n}\n\nexport function getSession() {\n  return getSettings().get('auth', null);\n}\n\nexport async function setSessionAsync(sessionData?: SessionData) {\n  await getSettings().setAsync('auth', sessionData, {\n    default: {},\n    ensureDir: true,\n  });\n}\n\n/**\n * Check if there are credentials available, without fetching the user information.\n * This can be used as a faster check to see if users are authenticated.\n * Note, this isn't checking the validity of the credentials.\n */\nexport function hasCredentials() {\n  return !!getAccessToken() || !!getSession();\n}\n\n/**\n * Get an anonymous and randomly generated identifier.\n * This is used to group telemetry event by unknown actor,\n * and cannot be used to identify a single user.\n */\nexport async function getAnonymousIdAsync(): Promise<string> {\n  const settings = getSettings();\n  let id = await settings.getAsync('uuid', null);\n\n  if (!id) {\n    id = crypto.randomUUID();\n    await settings.setAsync('uuid', id);\n  }\n\n  return id;\n}\n\n/**\n * Get an anonymous and randomly generated identifier.\n * This is used to group telemetry event by unknown actor,\n * and cannot be used to identify a single user.\n */\nexport function getAnonymousId(): string {\n  const settings = getSettings();\n  let id = settings.get('uuid', null);\n\n  if (!id) {\n    id = crypto.randomUUID();\n    settings.set('uuid', id);\n  }\n\n  return id;\n}\n"], "names": ["getAccessToken", "getAnonymousId", "getAnonymousIdAsync", "getExpoHomeDirectory", "getSession", "getSettings", "getSettingsDirectory", "getSettingsFilePath", "hasCredentials", "setSessionAsync", "home", "homedir", "process", "env", "__UNSAFE_EXPO_HOME_DIRECTORY", "boolish", "path", "join", "JsonFile", "ensureDir", "jsonParseE<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantReadFileDefault", "EXPO_TOKEN", "get", "sessionData", "setAsync", "default", "settings", "id", "getAsync", "crypto", "randomUUID", "set"], "mappings": ";;;;;;;;;;;IA0DgBA,cAAc;eAAdA;;IA8CAC,cAAc;eAAdA;;IAjBMC,mBAAmB;eAAnBA;;IA9DNC,oBAAoB;eAApBA;;IAqCAC,UAAU;eAAVA;;IAbAC,WAAW;eAAXA;;IAVAC,oBAAoB;eAApBA;;IAKAC,mBAAmB;eAAnBA;;IAkCAC,cAAc;eAAdA;;IAZMC,eAAe;eAAfA;;;;gEAlED;;;;;;;gEACF;;;;;;;yBACK;;;;;;;yBACA;;;;;;;iEACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBf,SAASN;IACd,MAAMO,OAAOC,IAAAA,aAAO;IAEpB,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QAC5C,OAAOF,QAAQC,GAAG,CAACC,4BAA4B;IACjD,OAAO,IAAIC,IAAAA,iBAAO,EAAC,gBAAgB,QAAQ;QACzC,OAAOC,QAAKC,IAAI,CAACP,MAAM;IACzB,OAAO,IAAIK,IAAAA,iBAAO,EAAC,cAAc,QAAQ;QACvC,OAAOC,QAAKC,IAAI,CAACP,MAAM;IACzB;IACA,OAAOM,QAAKC,IAAI,CAACP,MAAM;AACzB;AAGO,SAASJ;IACd,OAAOH;AACT;AAGO,SAASI;IACd,OAAOS,QAAKC,IAAI,CAACd,wBAAwB;AAC3C;AAGO,SAASE;IACd,OAAO,IAAIa,CAAAA,WAAO,SAAC,CAAmBX,uBAAuB;QAC3DY,WAAW;QACXC,uBAAuB,CAAC;QACxB,yEAAyE;QACzEC,qBAAqB,CAAC;IACxB;AACF;AAEO,SAASrB;IACd,OAAOY,QAAQC,GAAG,CAACS,UAAU,IAAI;AACnC;AAEO,SAASlB;IACd,OAAOC,cAAckB,GAAG,CAAC,QAAQ;AACnC;AAEO,eAAed,gBAAgBe,WAAyB;IAC7D,MAAMnB,cAAcoB,QAAQ,CAAC,QAAQD,aAAa;QAChDE,SAAS,CAAC;QACVP,WAAW;IACb;AACF;AAOO,SAASX;IACd,OAAO,CAAC,CAACR,oBAAoB,CAAC,CAACI;AACjC;AAOO,eAAeF;IACpB,MAAMyB,WAAWtB;IACjB,IAAIuB,KAAK,MAAMD,SAASE,QAAQ,CAAC,QAAQ;IAEzC,IAAI,CAACD,IAAI;QACPA,KAAKE,iBAAM,CAACC,UAAU;QACtB,MAAMJ,SAASF,QAAQ,CAAC,QAAQG;IAClC;IAEA,OAAOA;AACT;AAOO,SAAS3B;IACd,MAAM0B,WAAWtB;IACjB,IAAIuB,KAAKD,SAASJ,GAAG,CAAC,QAAQ;IAE9B,IAAI,CAACK,IAAI;QACPA,KAAKE,iBAAM,CAACC,UAAU;QACtBJ,SAASK,GAAG,CAAC,QAAQJ;IACvB;IAEA,OAAOA;AACT"}