{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/GDBProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport {\n  ProtocolClient,\n  ProtocolReader,\n  ProtocolReaderCallback,\n  ProtocolReaderFactory,\n  ProtocolWriter,\n} from './AbstractProtocol';\nimport { CommandError } from '../../../../utils/errors';\n\nconst debug = Debug('expo:apple-device:protocol:gdb');\nconst ACK_SUCCESS = '+'.charCodeAt(0);\n\nexport interface GDBMessage {\n  cmd: string;\n  args: string[];\n}\n\nexport class GDBProtocolClient extends ProtocolClient<GDBMessage> {\n  constructor(socket: Socket) {\n    super(socket, new ProtocolReaderFactory(GDBProtocolReader), new GDBProtocolWriter());\n  }\n}\n\nexport class GDBProtocolReader extends ProtocolReader {\n  constructor(callback: ProtocolReaderCallback) {\n    super(1 /* \"Header\" is '+' or '-' */, callback);\n  }\n\n  onData(data?: Buffer) {\n    // the GDB protocol does not support body length in its header so we cannot rely on\n    // the parent implementation to determine when a payload is complete\n    try {\n      // if there's data, add it to the existing buffer\n      this.buffer = data ? Buffer.concat([this.buffer, data]) : this.buffer;\n\n      // do we have enough bytes to proceed\n      if (this.buffer.length < this.headerSize) {\n        return; // incomplete header, wait for more\n      }\n\n      // first, check the header\n      if (this.parseHeader(this.buffer) === -1) {\n        // we have a valid header so check the body. GDB packets will always be a leading '$', data bytes,\n        // a trailing '#', and a two digit checksum. minimum valid body is the empty response '$#00'\n        // https://developer.apple.com/library/archive/documentation/DeveloperTools/gdb/gdb/gdb_33.html\n        const packetData = this.buffer.toString().match('\\\\$.*#[0-9a-f]{2}');\n        if (packetData == null) {\n          return; // incomplete body, wait for more\n        }\n        // extract the body and update the buffer\n        const body = Buffer.from(packetData[0]);\n        this.buffer = this.buffer.slice(this.headerSize + body.length);\n        // parse the payload and recurse if there is more data to process\n        this.callback(this.parseBody(body));\n        if (this.buffer.length) {\n          this.onData();\n        }\n      }\n    } catch (err: any) {\n      this.callback(null, err);\n    }\n  }\n\n  parseHeader(data: Buffer) {\n    if (data[0] !== ACK_SUCCESS) {\n      throw new CommandError('APPLE_DEVICE_GDB', 'Unsuccessful debugserver response');\n    } // TODO: retry?\n    return -1;\n  }\n\n  parseBody(buffer: Buffer) {\n    debug(`Response body: ${buffer.toString()}`);\n    // check for checksum\n    const checksum = buffer.slice(-3).toString();\n    if (checksum.match(/#[0-9a-f]{2}/)) {\n      // remove '$' prefix and checksum\n      const msg = buffer.slice(1, -3).toString();\n      if (validateChecksum(checksum, msg)) {\n        return msg;\n      } else if (msg.startsWith('E')) {\n        if (msg.match(/the device was not, or could not be, unlocked/)) {\n          throw new CommandError('APPLE_DEVICE_LOCKED', 'Device is currently locked.');\n        }\n\n        // Error message from debugserver -- Drop the `E`\n        return msg.slice(1);\n      } else {\n        throw new CommandError(\n          'APPLE_DEVICE_GDB',\n          `Invalid checksum received from debugserver. (checksum: ${checksum}, msg: ${msg})`\n        );\n      }\n    } else {\n      throw new CommandError('APPLE_DEVICE_GDB', \"Didn't receive checksum\");\n    }\n  }\n}\n\nexport class GDBProtocolWriter implements ProtocolWriter {\n  write(socket: Socket, msg: GDBMessage) {\n    const { cmd, args } = msg;\n    debug(`Socket write: ${cmd}, args: ${args}`);\n    // hex encode and concat all args\n    const encodedArgs = args\n      .map((arg) => Buffer.from(arg).toString('hex'))\n      .join()\n      .toUpperCase();\n    const checksumStr = calculateChecksum(cmd + encodedArgs);\n    const formattedCmd = `$${cmd}${encodedArgs}#${checksumStr}`;\n    socket.write(formattedCmd);\n  }\n}\n\n// hex value of (sum of cmd chars mod 256)\nfunction calculateChecksum(cmdStr: string) {\n  let checksum = 0;\n  for (let i = 0; i < cmdStr.length; i++) {\n    checksum += cmdStr.charCodeAt(i);\n  }\n  let result = (checksum % 256).toString(16);\n  // pad if necessary\n  if (result.length === 1) {\n    result = `0${result}`;\n  }\n  return result;\n}\n\nexport function validateChecksum(checksum: string, msg: string) {\n  // remove '#' from checksum\n  const checksumVal = checksum.startsWith('#') ? checksum.slice(1) : checksum;\n  // remove '$' from msg and calculate its checksum\n  const computedChecksum = calculateChecksum(msg);\n  // debug(`Checksum: ${checksumVal}, computed checksum: ${computedChecksum}`);\n  return checksumVal === computedChecksum;\n}\n"], "names": ["GDBProtocolClient", "GDBProtocolReader", "GDBProtocolWriter", "validate<PERSON><PERSON><PERSON><PERSON>", "debug", "Debug", "ACK_SUCCESS", "charCodeAt", "ProtocolClient", "constructor", "socket", "ProtocolReaderFactory", "ProtocolReader", "callback", "onData", "data", "buffer", "<PERSON><PERSON><PERSON>", "concat", "length", "headerSize", "parse<PERSON><PERSON><PERSON>", "packetData", "toString", "match", "body", "from", "slice", "parseBody", "err", "CommandError", "checksum", "msg", "startsWith", "write", "cmd", "args", "encodedArgs", "map", "arg", "join", "toUpperCase", "checksumStr", "calculateChecksum", "formattedCmd", "cmdStr", "i", "result", "checksumVal", "computedChecksum"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IAqBYA,iBAAiB;eAAjBA;;IAMAC,iBAAiB;eAAjBA;;IA2EAC,iBAAiB;eAAjBA;;IA6BGC,gBAAgB;eAAhBA;;;;gEAlIE;;;;;;kCASX;wBACsB;;;;;;AAE7B,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AACpB,MAAMC,cAAc,IAAIC,UAAU,CAAC;AAO5B,MAAMP,0BAA0BQ,gCAAc;IACnDC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,QAAQ,IAAIC,uCAAqB,CAACV,oBAAoB,IAAIC;IAClE;AACF;AAEO,MAAMD,0BAA0BW,gCAAc;IACnDH,YAAYI,QAAgC,CAAE;QAC5C,KAAK,CAAC,EAAE,0BAA0B,KAAIA;IACxC;IAEAC,OAAOC,IAAa,EAAE;QACpB,mFAAmF;QACnF,oEAAoE;QACpE,IAAI;YACF,iDAAiD;YACjD,IAAI,CAACC,MAAM,GAAGD,OAAOE,OAAOC,MAAM,CAAC;gBAAC,IAAI,CAACF,MAAM;gBAAED;aAAK,IAAI,IAAI,CAACC,MAAM;YAErE,qCAAqC;YACrC,IAAI,IAAI,CAACA,MAAM,CAACG,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;gBACxC,QAAQ,mCAAmC;YAC7C;YAEA,0BAA0B;YAC1B,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAACL,MAAM,MAAM,CAAC,GAAG;gBACxC,kGAAkG;gBAClG,4FAA4F;gBAC5F,+FAA+F;gBAC/F,MAAMM,aAAa,IAAI,CAACN,MAAM,CAACO,QAAQ,GAAGC,KAAK,CAAC;gBAChD,IAAIF,cAAc,MAAM;oBACtB,QAAQ,iCAAiC;gBAC3C;gBACA,yCAAyC;gBACzC,MAAMG,OAAOR,OAAOS,IAAI,CAACJ,UAAU,CAAC,EAAE;gBACtC,IAAI,CAACN,MAAM,GAAG,IAAI,CAACA,MAAM,CAACW,KAAK,CAAC,IAAI,CAACP,UAAU,GAAGK,KAAKN,MAAM;gBAC7D,iEAAiE;gBACjE,IAAI,CAACN,QAAQ,CAAC,IAAI,CAACe,SAAS,CAACH;gBAC7B,IAAI,IAAI,CAACT,MAAM,CAACG,MAAM,EAAE;oBACtB,IAAI,CAACL,MAAM;gBACb;YACF;QACF,EAAE,OAAOe,KAAU;YACjB,IAAI,CAAChB,QAAQ,CAAC,MAAMgB;QACtB;IACF;IAEAR,YAAYN,IAAY,EAAE;QACxB,IAAIA,IAAI,CAAC,EAAE,KAAKT,aAAa;YAC3B,MAAM,IAAIwB,oBAAY,CAAC,oBAAoB;QAC7C,EAAE,eAAe;QACjB,OAAO,CAAC;IACV;IAEAF,UAAUZ,MAAc,EAAE;QACxBZ,MAAM,CAAC,eAAe,EAAEY,OAAOO,QAAQ,IAAI;QAC3C,qBAAqB;QACrB,MAAMQ,WAAWf,OAAOW,KAAK,CAAC,CAAC,GAAGJ,QAAQ;QAC1C,IAAIQ,SAASP,KAAK,CAAC,iBAAiB;YAClC,iCAAiC;YACjC,MAAMQ,MAAMhB,OAAOW,KAAK,CAAC,GAAG,CAAC,GAAGJ,QAAQ;YACxC,IAAIpB,iBAAiB4B,UAAUC,MAAM;gBACnC,OAAOA;YACT,OAAO,IAAIA,IAAIC,UAAU,CAAC,MAAM;gBAC9B,IAAID,IAAIR,KAAK,CAAC,kDAAkD;oBAC9D,MAAM,IAAIM,oBAAY,CAAC,uBAAuB;gBAChD;gBAEA,iDAAiD;gBACjD,OAAOE,IAAIL,KAAK,CAAC;YACnB,OAAO;gBACL,MAAM,IAAIG,oBAAY,CACpB,oBACA,CAAC,uDAAuD,EAAEC,SAAS,OAAO,EAAEC,IAAI,CAAC,CAAC;YAEtF;QACF,OAAO;YACL,MAAM,IAAIF,oBAAY,CAAC,oBAAoB;QAC7C;IACF;AACF;AAEO,MAAM5B;IACXgC,MAAMxB,MAAc,EAAEsB,GAAe,EAAE;QACrC,MAAM,EAAEG,GAAG,EAAEC,IAAI,EAAE,GAAGJ;QACtB5B,MAAM,CAAC,cAAc,EAAE+B,IAAI,QAAQ,EAAEC,MAAM;QAC3C,iCAAiC;QACjC,MAAMC,cAAcD,KACjBE,GAAG,CAAC,CAACC,MAAQtB,OAAOS,IAAI,CAACa,KAAKhB,QAAQ,CAAC,QACvCiB,IAAI,GACJC,WAAW;QACd,MAAMC,cAAcC,kBAAkBR,MAAME;QAC5C,MAAMO,eAAe,CAAC,CAAC,EAAET,MAAME,YAAY,CAAC,EAAEK,aAAa;QAC3DhC,OAAOwB,KAAK,CAACU;IACf;AACF;AAEA,0CAA0C;AAC1C,SAASD,kBAAkBE,MAAc;IACvC,IAAId,WAAW;IACf,IAAK,IAAIe,IAAI,GAAGA,IAAID,OAAO1B,MAAM,EAAE2B,IAAK;QACtCf,YAAYc,OAAOtC,UAAU,CAACuC;IAChC;IACA,IAAIC,SAAS,AAAChB,CAAAA,WAAW,GAAE,EAAGR,QAAQ,CAAC;IACvC,mBAAmB;IACnB,IAAIwB,OAAO5B,MAAM,KAAK,GAAG;QACvB4B,SAAS,CAAC,CAAC,EAAEA,QAAQ;IACvB;IACA,OAAOA;AACT;AAEO,SAAS5C,iBAAiB4B,QAAgB,EAAEC,GAAW;IAC5D,2BAA2B;IAC3B,MAAMgB,cAAcjB,SAASE,UAAU,CAAC,OAAOF,SAASJ,KAAK,CAAC,KAAKI;IACnE,iDAAiD;IACjD,MAAMkB,mBAAmBN,kBAAkBX;IAC3C,6EAA6E;IAC7E,OAAOgB,gBAAgBC;AACzB"}