{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/app/geistmono_7c774ef6.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'geist<PERSON>ono';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/GeistMonoVF.woff%22,%22preload%22:true,%22has_size_adjust%22:true}') format('woff');\n    font-display: swap;\n    \n}\n\n@font-face {\n    font-family: 'geistMono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 69.97%;\ndescent-override: 16.73%;\nline-gap-override: 7.61%;\nsize-adjust: 131.49%;\n\n}\n\n.className {\n    font-family: 'geistMono', 'geistMono Fallback';\n    \n}\n.variable {\n    --font-geist-mono: 'geistMono', 'geistMono Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA"}}]}