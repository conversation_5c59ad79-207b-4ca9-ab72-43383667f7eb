{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/simulatorCodeSigning.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport plist from '@expo/plist';\nimport fs from 'fs';\n\nconst debug = require('debug')('expo:run:ios:codeSigning:simulator');\n\n// NOTE(EvanBacon): These are entitlements that work in a simulator\n// but still require the project to have development code signing setup.\n// There may be more, but this is fine for now.\nconst ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING = [\n  'com.apple.developer.associated-domains',\n  'com.apple.developer.applesignin',\n];\n\nfunction getEntitlements(projectRoot: string): Record<string, any> | null {\n  try {\n    const entitlementsPath = IOSConfig.Entitlements.getEntitlementsPath(projectRoot);\n    if (!entitlementsPath || !fs.existsSync(entitlementsPath)) {\n      return null;\n    }\n\n    const entitlementsContents = fs.readFileSync(entitlementsPath, 'utf8');\n    const entitlements = plist.parse(entitlementsContents);\n    return entitlements;\n  } catch (error) {\n    debug('Failed to read entitlements', error);\n  }\n  return null;\n}\n\n/** @returns true if the simulator build should be code signed for development. */\nexport function simulatorBuildRequiresCodeSigning(projectRoot: string): boolean {\n  const entitlements = getEntitlements(projectRoot);\n  if (!entitlements) {\n    return false;\n  }\n  return ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING.some((entitlement) => entitlement in entitlements);\n}\n"], "names": ["simulatorBuildRequiresCodeSigning", "debug", "require", "ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING", "getEntitlements", "projectRoot", "entitlementsPath", "IOSConfig", "Entitlements", "getEntitlementsPath", "fs", "existsSync", "entitlementsContents", "readFileSync", "entitlements", "plist", "parse", "error", "some", "entitlement"], "mappings": ";;;;+BA+BgBA;;;eAAAA;;;;yBA/BU;;;;;;;gEACR;;;;;;;gEACH;;;;;;;;;;;AAEf,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,mEAAmE;AACnE,wEAAwE;AACxE,+CAA+C;AAC/C,MAAMC,yCAAyC;IAC7C;IACA;CACD;AAED,SAASC,gBAAgBC,WAAmB;IAC1C,IAAI;QACF,MAAMC,mBAAmBC,0BAAS,CAACC,YAAY,CAACC,mBAAmB,CAACJ;QACpE,IAAI,CAACC,oBAAoB,CAACI,aAAE,CAACC,UAAU,CAACL,mBAAmB;YACzD,OAAO;QACT;QAEA,MAAMM,uBAAuBF,aAAE,CAACG,YAAY,CAACP,kBAAkB;QAC/D,MAAMQ,eAAeC,gBAAK,CAACC,KAAK,CAACJ;QACjC,OAAOE;IACT,EAAE,OAAOG,OAAO;QACdhB,MAAM,+BAA+BgB;IACvC;IACA,OAAO;AACT;AAGO,SAASjB,kCAAkCK,WAAmB;IACnE,MAAMS,eAAeV,gBAAgBC;IACrC,IAAI,CAACS,cAAc;QACjB,OAAO;IACT;IACA,OAAOX,uCAAuCe,IAAI,CAAC,CAACC,cAAgBA,eAAeL;AACrF"}