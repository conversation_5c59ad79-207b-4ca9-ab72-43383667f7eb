import { UsersService, UpdateUserDto, CreateDriverProfileDto } from './users.service';
import { UserRole } from '@prisma/client';
export declare class UsersController {
    private usersService;
    constructor(usersService: UsersService);
    findAll(role?: UserRole): Promise<{
        clientProfile: {
            id: string;
            fullName: string;
        } | null;
        driverProfile: {
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            id: string;
            isVerified: boolean;
            isOnline: boolean;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        partnerStore: {
            id: string;
            name: string;
            isOpen: boolean;
        } | null;
    }[]>;
    getDrivers(isOnline?: boolean): Promise<({
        user: {
            id: string;
            phone: string | null;
            email: string | null;
        };
        vehicle: {
            id: string;
            driverId: string;
            type: string;
            licensePlate: string;
        } | null;
        wallet: {
            id: string;
            driverId: string;
            balance: number;
        } | null;
    } & {
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    })[]>;
    findOne(id: string): Promise<{
        clientProfile: {
            id: string;
            fullName: string;
        } | null;
        driverProfile: {
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            wallet: {
                id: string;
                driverId: string;
                balance: number;
            } | null;
            id: string;
            isVerified: boolean;
            isOnline: boolean;
            currentLocation: import("@prisma/client/runtime/library").JsonValue;
            documents: {
                id: string;
                driverId: string;
                type: string;
                url: string;
                isValidated: boolean;
            }[];
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        partnerStore: {
            id: string;
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
            isOpen: boolean;
            products: {
                id: string;
                price: number;
                name: string;
                inStock: boolean;
            }[];
        } | null;
    }>;
    updateProfile(id: string, updateData: UpdateUserDto, currentUser: any): Promise<{
        id: string;
        updatedAt: Date;
        phone: string | null;
        email: string | null;
        role: import(".prisma/client").$Enums.UserRole;
    }>;
    createDriverProfile(createDriverDto: CreateDriverProfileDto): Promise<{
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
    updateDriverStatus(driverId: string, statusData: {
        isOnline: boolean;
        currentLocation?: any;
    }): Promise<{
        id: string;
        userId: string;
        isVerified: boolean;
        isOnline: boolean;
        currentLocation: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
}
