{"version": 3, "sources": ["../../../../../src/start/platforms/android/adb.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport os from 'os';\n\nimport { ADBServer } from './ADBServer';\nimport * as Log from '../../../log';\nimport { env } from '../../../utils/env';\nimport { CommandError } from '../../../utils/errors';\nimport { learnMore } from '../../../utils/link';\n\nconst debug = require('debug')('expo:start:platforms:android:adb') as typeof console.log;\n\nexport enum DeviceABI {\n  // The arch specific android target platforms are soft-deprecated.\n  // Instead of using TargetPlatform as a combination arch + platform\n  // the code will be updated to carry arch information in [DarwinArch]\n  // and [AndroidArch].\n  arm = 'arm',\n  arm64 = 'arm64',\n  x64 = 'x64',\n  x86 = 'x86',\n  x8664 = 'x86_64',\n  arm64v8a = 'arm64-v8a',\n  armeabiV7a = 'armeabi-v7a',\n  armeabi = 'armeabi',\n  universal = 'universal',\n}\n\n/** Represents a connected Android device. */\nexport type Device = {\n  /** Process ID. */\n  pid?: string;\n  /** Name of the device, also used as the ID for opening devices. */\n  name: string;\n  /** Is emulator or connected device. */\n  type: 'emulator' | 'device';\n  /** Is the device booted (emulator). */\n  isBooted: boolean;\n  /** Is device authorized for developing. https://expo.fyi/authorize-android-device */\n  isAuthorized: boolean;\n  /** The connection type to ADB, only available when `type: device` */\n  connectionType?: 'USB' | 'Network';\n};\n\ntype DeviceContext = Pick<Device, 'pid'>;\n\ntype DeviceProperties = Record<string, string>;\n\nconst CANT_START_ACTIVITY_ERROR = 'Activity not started, unable to resolve Intent';\n// http://developer.android.com/ndk/guides/abis.html\nconst PROP_CPU_NAME = 'ro.product.cpu.abi';\n\nconst PROP_CPU_ABI_LIST_NAME = 'ro.product.cpu.abilist';\n\n// Can sometimes be null\n// http://developer.android.com/ndk/guides/abis.html\nconst PROP_BOOT_ANIMATION_STATE = 'init.svc.bootanim';\n\nlet _server: ADBServer | null;\n\n/** Return the lazily loaded ADB server instance. */\nexport function getServer() {\n  _server ??= new ADBServer();\n  return _server;\n}\n\n/** Logs an FYI message about authorizing your device. */\nexport function logUnauthorized(device: Device) {\n  Log.warn(\n    `\\nThis computer is not authorized for developing on ${chalk.bold(device.name)}. ${chalk.dim(\n      learnMore('https://expo.fyi/authorize-android-device')\n    )}`\n  );\n}\n\n/** Returns true if the provided package name is installed on the provided Android device. */\nexport async function isPackageInstalledAsync(\n  device: DeviceContext,\n  androidPackage: string\n): Promise<boolean> {\n  const packages = await getServer().runAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'pm',\n      'list',\n      'packages',\n      '--user',\n      env.EXPO_ADB_USER,\n      androidPackage\n    )\n  );\n\n  const lines = packages.split(/\\r?\\n/);\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    if (line === `package:${androidPackage}`) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.launchActivity Activity to launch `[application identifier]/.[main activity name]`, ex: `com.bacon.app/.MainActivity`\n * @param props.url Optional (dev client) URL to launch\n */\nexport async function launchActivityAsync(\n  device: DeviceContext,\n  {\n    launchActivity,\n    url,\n  }: {\n    launchActivity: string;\n    url?: string;\n  }\n) {\n  const args: string[] = [\n    'shell',\n    'am',\n    'start',\n    // FLAG_ACTIVITY_SINGLE_TOP -- If set, the activity will not be launched if it is already running at the top of the history stack.\n    '-f',\n    '0x20000000',\n    // Activity to open first: com.bacon.app/.MainActivity\n    '-n',\n    launchActivity,\n  ];\n\n  if (url) {\n    args.push('-d', url);\n  }\n\n  return openAsync(adbArgs(device.pid, ...args));\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.applicationId package name to launch.\n */\nexport async function openAppIdAsync(\n  device: DeviceContext,\n  {\n    applicationId,\n  }: {\n    applicationId: string;\n  }\n) {\n  return openAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'monkey',\n      '-p',\n      applicationId,\n      '-c',\n      'android.intent.category.LAUNCHER',\n      '1'\n    )\n  );\n}\n\n/**\n * @param device.pid Process ID of the Android device to launch.\n * @param props.url URL to launch.\n */\nexport async function openUrlAsync(\n  device: DeviceContext,\n  {\n    url,\n  }: {\n    url: string;\n  }\n) {\n  return openAsync(\n    adbArgs(\n      device.pid,\n      'shell',\n      'am',\n      'start',\n      '-a',\n      'android.intent.action.VIEW',\n      '-d',\n      // ADB requires ampersands to be escaped.\n      url.replace(/&/g, String.raw`\\&`)\n    )\n  );\n}\n\n/** Runs a generic command watches for common errors in order to throw with an expected code. */\nasync function openAsync(args: string[]): Promise<string> {\n  const results = await getServer().runAsync(args);\n  if (\n    results.includes(CANT_START_ACTIVITY_ERROR) ||\n    results.match(/Error: Activity class .* does not exist\\./g)\n  ) {\n    throw new CommandError('APP_NOT_INSTALLED', results.substring(results.indexOf('Error: ')));\n  }\n  return results;\n}\n\n/** Uninstall an app given its Android package name. */\nexport async function uninstallAsync(\n  device: DeviceContext,\n  { appId }: { appId: string }\n): Promise<string> {\n  return await getServer().runAsync(\n    adbArgs(device.pid, 'uninstall', '--user', env.EXPO_ADB_USER, appId)\n  );\n}\n\n/** Get package info from an app based on its Android package name. */\nexport async function getPackageInfoAsync(\n  device: DeviceContext,\n  { appId }: { appId: string }\n): Promise<string> {\n  return await getServer().runAsync(adbArgs(device.pid, 'shell', 'dumpsys', 'package', appId));\n}\n\n/** Install an app on a connected device. */\nexport async function installAsync(device: DeviceContext, { filePath }: { filePath: string }) {\n  // TODO: Handle the `INSTALL_FAILED_INSUFFICIENT_STORAGE` error.\n  return await getServer().runAsync(\n    adbArgs(device.pid, 'install', '-r', '-d', '--user', env.EXPO_ADB_USER, filePath)\n  );\n}\n\n/** Format ADB args with process ID. */\nexport function adbArgs(pid: Device['pid'], ...options: string[]): string[] {\n  const args = [];\n  if (pid) {\n    args.push('-s', pid);\n  }\n\n  return args.concat(options);\n}\n\n// TODO: This is very expensive for some operations.\nexport async function getAttachedDevicesAsync(): Promise<Device[]> {\n  const output = await getServer().runAsync(['devices', '-l']);\n\n  const splitItems = output\n    .trim()\n    .replace(/\\n$/, '')\n    .split(os.EOL)\n    // Filter ADB trace logs from the output, e.g.\n    // adb D 03-06 15:25:53 63677 4018815 adb_client.cpp:393] adb_query: host:devices-l\n    // 03-04 12:29:44.557 16415 16415 D adb     : commandline.cpp:1646 Using server socket: tcp:************:5037\n    // 03-04 12:29:44.557 16415 16415 D adb     : adb_client.cpp:160 _adb_connect: host:version\n    .filter((line) => !line.match(/\\.cpp:[0-9]+/));\n\n  // First line is `\"List of devices attached\"`, remove it\n  // @ts-ignore: todo\n  const attachedDevices: {\n    props: string[];\n    type: Device['type'];\n    isAuthorized: Device['isAuthorized'];\n    isBooted: Device['isBooted'];\n    connectionType?: Device['connectionType'];\n  }[] = splitItems\n    .slice(1, splitItems.length)\n    .map((line) => {\n      // unauthorized: ['FA8251A00719', 'unauthorized', 'usb:338690048X', 'transport_id:5']\n      // authorized: ['FA8251A00719', 'device', 'usb:336592896X', 'product:walleye', 'model:Pixel_2', 'device:walleye', 'transport_id:4']\n      // emulator: ['emulator-5554', 'offline', 'transport_id:1']\n      const props = line.split(' ').filter(Boolean);\n      const type = line.includes('emulator') ? 'emulator' : 'device';\n\n      let connectionType;\n      if (type === 'device' && line.includes('usb:')) {\n        connectionType = 'USB';\n      } else if (type === 'device' && line.includes('_adb-tls-connect.')) {\n        connectionType = 'Network';\n      }\n\n      const isBooted = type === 'emulator' || props[1] !== 'offline';\n      const isAuthorized =\n        connectionType === 'Network'\n          ? line.includes('model:') // Network connected devices show `model:<name>` when authorized\n          : props[1] !== 'unauthorized';\n\n      return { props, type, isAuthorized, isBooted, connectionType };\n    })\n    .filter(({ props: [pid] }) => !!pid);\n\n  const devicePromises = attachedDevices.map<Promise<Device>>(async (props) => {\n    const {\n      type,\n      props: [pid, ...deviceInfo],\n      isAuthorized,\n      isBooted,\n    } = props;\n\n    let name: string | null = null;\n\n    if (type === 'device') {\n      if (isAuthorized) {\n        // Possibly formatted like `model:Pixel_2`\n        // Transform to `Pixel_2`\n        const modelItem = deviceInfo.find((info) => info.includes('model:'));\n        if (modelItem) {\n          name = modelItem.replace('model:', '');\n        }\n      }\n      // unauthorized devices don't have a name available to read\n      if (!name) {\n        // Device FA8251A00719\n        name = `Device ${pid}`;\n      }\n    } else {\n      // Given an emulator pid, get the emulator name which can be used to start the emulator later.\n      name = (await getAdbNameForDeviceIdAsync({ pid })) ?? '';\n    }\n\n    return props.connectionType\n      ? { pid, name, type, isAuthorized, isBooted, connectionType: props.connectionType }\n      : { pid, name, type, isAuthorized, isBooted };\n  });\n\n  return Promise.all(devicePromises);\n}\n\n/**\n * Return the Emulator name for an emulator ID, this can be used to determine if an emulator is booted.\n *\n * @param device.pid a value like `emulator-5554` from `abd devices`\n */\nexport async function getAdbNameForDeviceIdAsync(device: DeviceContext): Promise<string | null> {\n  const results = await getServer().runAsync(adbArgs(device.pid, 'emu', 'avd', 'name'));\n\n  if (results.match(/could not connect to TCP port .*: Connection refused/)) {\n    // Can also occur when the emulator does not exist.\n    throw new CommandError('EMULATOR_NOT_FOUND', results);\n  }\n\n  return sanitizeAdbDeviceName(results) ?? null;\n}\n\nexport async function isDeviceBootedAsync({\n  name,\n}: { name?: string } = {}): Promise<Device | null> {\n  const devices = await getAttachedDevicesAsync();\n\n  if (!name) {\n    return devices[0] ?? null;\n  }\n\n  return devices.find((device) => device.name === name) ?? null;\n}\n\n/**\n * Returns true when a device's splash screen animation has stopped.\n * This can be used to detect when a device is fully booted and ready to use.\n *\n * @param pid\n */\nexport async function isBootAnimationCompleteAsync(pid?: string): Promise<boolean> {\n  try {\n    const props = await getPropertyDataForDeviceAsync({ pid }, PROP_BOOT_ANIMATION_STATE);\n    return !!props[PROP_BOOT_ANIMATION_STATE].match(/stopped/);\n  } catch {\n    return false;\n  }\n}\n\n/** Get a list of ABIs for the provided device. */\nexport async function getDeviceABIsAsync(\n  device: Pick<Device, 'name' | 'pid'>\n): Promise<DeviceABI[]> {\n  const cpuAbiList = (await getPropertyDataForDeviceAsync(device, PROP_CPU_ABI_LIST_NAME))[\n    PROP_CPU_ABI_LIST_NAME\n  ];\n\n  if (cpuAbiList) {\n    return cpuAbiList.trim().split(',') as DeviceABI[];\n  }\n\n  const abi = (await getPropertyDataForDeviceAsync(device, PROP_CPU_NAME))[\n    PROP_CPU_NAME\n  ] as DeviceABI;\n  return [abi];\n}\n\nexport async function getPropertyDataForDeviceAsync(\n  device: DeviceContext,\n  prop?: string\n): Promise<DeviceProperties> {\n  // @ts-ignore\n  const propCommand = adbArgs(...[device.pid, 'shell', 'getprop', prop].filter(Boolean));\n  try {\n    // Prevent reading as UTF8.\n    const results = await getServer().getFileOutputAsync(propCommand);\n    // Like:\n    // [wifi.direct.interface]: [p2p-dev-wlan0]\n    // [wifi.interface]: [wlan0]\n\n    if (prop) {\n      debug(`Property data: (device pid: ${device.pid}, prop: ${prop}, data: ${results})`);\n      return {\n        [prop]: results,\n      };\n    }\n    const props = parseAdbDeviceProperties(results);\n\n    debug(`Parsed data:`, props);\n\n    return props;\n  } catch (error: any) {\n    // TODO: Ensure error has message and not stderr\n    throw new CommandError(`Failed to get properties for device (${device.pid}): ${error.message}`);\n  }\n}\n\nfunction parseAdbDeviceProperties(devicePropertiesString: string) {\n  const properties: DeviceProperties = {};\n  const propertyExp = /\\[(.*?)\\]: \\[(.*?)\\]/gm;\n  for (const match of devicePropertiesString.matchAll(propertyExp)) {\n    properties[match[1]] = match[2];\n  }\n  return properties;\n}\n\n/**\n * Sanitize the ADB device name to only get the actual device name.\n * On Windows, we need to do \\r, \\n, and \\r\\n filtering to get the name.\n */\nexport function sanitizeAdbDeviceName(deviceName: string) {\n  return deviceName\n    .trim()\n    .split(/[\\r\\n]+/)\n    .shift();\n}\n"], "names": ["DeviceABI", "adbArgs", "getAdbNameForDeviceIdAsync", "getAttachedDevicesAsync", "getDeviceABIsAsync", "getPackageInfoAsync", "getPropertyDataForDeviceAsync", "getServer", "installAsync", "isBootAnimationCompleteAsync", "isDeviceBootedAsync", "isPackageInstalledAsync", "launchActivityAsync", "logUnauthorized", "openAppIdAsync", "openUrlAsync", "sanitizeAdbDeviceName", "uninstallAsync", "debug", "require", "CANT_START_ACTIVITY_ERROR", "PROP_CPU_NAME", "PROP_CPU_ABI_LIST_NAME", "PROP_BOOT_ANIMATION_STATE", "_server", "ADBServer", "device", "Log", "warn", "chalk", "bold", "name", "dim", "learnMore", "androidPackage", "packages", "runAsync", "pid", "env", "EXPO_ADB_USER", "lines", "split", "i", "length", "line", "trim", "launchActivity", "url", "args", "push", "openAsync", "applicationId", "replace", "String", "raw", "results", "includes", "match", "CommandError", "substring", "indexOf", "appId", "filePath", "options", "concat", "output", "splitItems", "os", "EOL", "filter", "attachedDevices", "slice", "map", "props", "Boolean", "type", "connectionType", "isBooted", "isAuthorized", "devicePromises", "deviceInfo", "modelItem", "find", "info", "Promise", "all", "devices", "cpuAbiList", "abi", "prop", "propCommand", "getFileOutputAsync", "parseAdbDeviceProperties", "error", "message", "devicePropertiesString", "properties", "propertyExp", "matchAll", "deviceName", "shift"], "mappings": ";;;;;;;;;;;IAWYA,SAAS;eAATA;;IAyNIC,OAAO;eAAPA;;IAmGMC,0BAA0B;eAA1BA;;IAzFAC,uBAAuB;eAAvBA;;IAgIAC,kBAAkB;eAAlBA;;IA1JAC,mBAAmB;eAAnBA;;IA2KAC,6BAA6B;eAA7BA;;IAnUNC,SAAS;eAATA;;IAgKMC,YAAY;eAAZA;;IAwIAC,4BAA4B;eAA5BA;;IAlBAC,mBAAmB;eAAnBA;;IAvQAC,uBAAuB;eAAvBA;;IAgCAC,mBAAmB;eAAnBA;;IAzCNC,eAAe;eAAfA;;IA0EMC,cAAc;eAAdA;;IA0BAC,YAAY;eAAZA;;IAoQNC,qBAAqB;eAArBA;;IAhOMC,cAAc;eAAdA;;;;gEA1MJ;;;;;;;gEACH;;;;;;2BAEW;6DACL;qBACD;wBACS;sBACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,QAAQC,QAAQ,SAAS;AAExB,IAAA,AAAKnB,mCAAAA;IACV,kEAAkE;IAClE,mEAAmE;IACnE,qEAAqE;IACrE,qBAAqB;;;;;;;;;;WAJXA;;AAoCZ,MAAMoB,4BAA4B;AAClC,oDAAoD;AACpD,MAAMC,gBAAgB;AAEtB,MAAMC,yBAAyB;AAE/B,wBAAwB;AACxB,oDAAoD;AACpD,MAAMC,4BAA4B;AAElC,IAAIC;AAGG,SAASjB;IACdiB,YAAY,IAAIC,oBAAS;IACzB,OAAOD;AACT;AAGO,SAASX,gBAAgBa,MAAc;IAC5CC,KAAIC,IAAI,CACN,CAAC,oDAAoD,EAAEC,gBAAK,CAACC,IAAI,CAACJ,OAAOK,IAAI,EAAE,EAAE,EAAEF,gBAAK,CAACG,GAAG,CAC1FC,IAAAA,eAAS,EAAC,+CACT;AAEP;AAGO,eAAetB,wBACpBe,MAAqB,EACrBQ,cAAsB;IAEtB,MAAMC,WAAW,MAAM5B,YAAY6B,QAAQ,CACzCnC,QACEyB,OAAOW,GAAG,EACV,SACA,MACA,QACA,YACA,UACAC,QAAG,CAACC,aAAa,EACjBL;IAIJ,MAAMM,QAAQL,SAASM,KAAK,CAAC;IAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIF,MAAMG,MAAM,EAAED,IAAK;QACrC,MAAME,OAAOJ,KAAK,CAACE,EAAE,CAACG,IAAI;QAC1B,IAAID,SAAS,CAAC,QAAQ,EAAEV,gBAAgB,EAAE;YACxC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAOO,eAAetB,oBACpBc,MAAqB,EACrB,EACEoB,cAAc,EACdC,GAAG,EAIJ;IAED,MAAMC,OAAiB;QACrB;QACA;QACA;QACA,kIAAkI;QAClI;QACA;QACA,sDAAsD;QACtD;QACAF;KACD;IAED,IAAIC,KAAK;QACPC,KAAKC,IAAI,CAAC,MAAMF;IAClB;IAEA,OAAOG,UAAUjD,QAAQyB,OAAOW,GAAG,KAAKW;AAC1C;AAMO,eAAelC,eACpBY,MAAqB,EACrB,EACEyB,aAAa,EAGd;IAED,OAAOD,UACLjD,QACEyB,OAAOW,GAAG,EACV,SACA,UACA,MACAc,eACA,MACA,oCACA;AAGN;AAMO,eAAepC,aACpBW,MAAqB,EACrB,EACEqB,GAAG,EAGJ;IAED,OAAOG,UACLjD,QACEyB,OAAOW,GAAG,EACV,SACA,MACA,SACA,MACA,8BACA,MACA,yCAAyC;IACzCU,IAAIK,OAAO,CAAC,MAAMC,OAAOC,GAAG,CAAC,EAAE,CAAC;AAGtC;AAEA,8FAA8F,GAC9F,eAAeJ,UAAUF,IAAc;IACrC,MAAMO,UAAU,MAAMhD,YAAY6B,QAAQ,CAACY;IAC3C,IACEO,QAAQC,QAAQ,CAACpC,8BACjBmC,QAAQE,KAAK,CAAC,+CACd;QACA,MAAM,IAAIC,oBAAY,CAAC,qBAAqBH,QAAQI,SAAS,CAACJ,QAAQK,OAAO,CAAC;IAChF;IACA,OAAOL;AACT;AAGO,eAAetC,eACpBS,MAAqB,EACrB,EAAEmC,KAAK,EAAqB;IAE5B,OAAO,MAAMtD,YAAY6B,QAAQ,CAC/BnC,QAAQyB,OAAOW,GAAG,EAAE,aAAa,UAAUC,QAAG,CAACC,aAAa,EAAEsB;AAElE;AAGO,eAAexD,oBACpBqB,MAAqB,EACrB,EAAEmC,KAAK,EAAqB;IAE5B,OAAO,MAAMtD,YAAY6B,QAAQ,CAACnC,QAAQyB,OAAOW,GAAG,EAAE,SAAS,WAAW,WAAWwB;AACvF;AAGO,eAAerD,aAAakB,MAAqB,EAAE,EAAEoC,QAAQ,EAAwB;IAC1F,gEAAgE;IAChE,OAAO,MAAMvD,YAAY6B,QAAQ,CAC/BnC,QAAQyB,OAAOW,GAAG,EAAE,WAAW,MAAM,MAAM,UAAUC,QAAG,CAACC,aAAa,EAAEuB;AAE5E;AAGO,SAAS7D,QAAQoC,GAAkB,EAAE,GAAG0B,OAAiB;IAC9D,MAAMf,OAAO,EAAE;IACf,IAAIX,KAAK;QACPW,KAAKC,IAAI,CAAC,MAAMZ;IAClB;IAEA,OAAOW,KAAKgB,MAAM,CAACD;AACrB;AAGO,eAAe5D;IACpB,MAAM8D,SAAS,MAAM1D,YAAY6B,QAAQ,CAAC;QAAC;QAAW;KAAK;IAE3D,MAAM8B,aAAaD,OAChBpB,IAAI,GACJO,OAAO,CAAC,OAAO,IACfX,KAAK,CAAC0B,aAAE,CAACC,GAAG,CACb,8CAA8C;IAC9C,mFAAmF;IACnF,6GAA6G;IAC7G,2FAA2F;KAC1FC,MAAM,CAAC,CAACzB,OAAS,CAACA,KAAKa,KAAK,CAAC;IAEhC,wDAAwD;IACxD,mBAAmB;IACnB,MAAMa,kBAMAJ,WACHK,KAAK,CAAC,GAAGL,WAAWvB,MAAM,EAC1B6B,GAAG,CAAC,CAAC5B;QACJ,qFAAqF;QACrF,mIAAmI;QACnI,2DAA2D;QAC3D,MAAM6B,QAAQ7B,KAAKH,KAAK,CAAC,KAAK4B,MAAM,CAACK;QACrC,MAAMC,OAAO/B,KAAKY,QAAQ,CAAC,cAAc,aAAa;QAEtD,IAAIoB;QACJ,IAAID,SAAS,YAAY/B,KAAKY,QAAQ,CAAC,SAAS;YAC9CoB,iBAAiB;QACnB,OAAO,IAAID,SAAS,YAAY/B,KAAKY,QAAQ,CAAC,sBAAsB;YAClEoB,iBAAiB;QACnB;QAEA,MAAMC,WAAWF,SAAS,cAAcF,KAAK,CAAC,EAAE,KAAK;QACrD,MAAMK,eACJF,mBAAmB,YACfhC,KAAKY,QAAQ,CAAC,UAAU,gEAAgE;WACxFiB,KAAK,CAAC,EAAE,KAAK;QAEnB,OAAO;YAAEA;YAAOE;YAAMG;YAAcD;YAAUD;QAAe;IAC/D,GACCP,MAAM,CAAC,CAAC,EAAEI,OAAO,CAACpC,IAAI,EAAE,GAAK,CAAC,CAACA;IAElC,MAAM0C,iBAAiBT,gBAAgBE,GAAG,CAAkB,OAAOC;QACjE,MAAM,EACJE,IAAI,EACJF,OAAO,CAACpC,KAAK,GAAG2C,WAAW,EAC3BF,YAAY,EACZD,QAAQ,EACT,GAAGJ;QAEJ,IAAI1C,OAAsB;QAE1B,IAAI4C,SAAS,UAAU;YACrB,IAAIG,cAAc;gBAChB,0CAA0C;gBAC1C,yBAAyB;gBACzB,MAAMG,YAAYD,WAAWE,IAAI,CAAC,CAACC,OAASA,KAAK3B,QAAQ,CAAC;gBAC1D,IAAIyB,WAAW;oBACblD,OAAOkD,UAAU7B,OAAO,CAAC,UAAU;gBACrC;YACF;YACA,2DAA2D;YAC3D,IAAI,CAACrB,MAAM;gBACT,sBAAsB;gBACtBA,OAAO,CAAC,OAAO,EAAEM,KAAK;YACxB;QACF,OAAO;YACL,8FAA8F;YAC9FN,OAAO,AAAC,MAAM7B,2BAA2B;gBAAEmC;YAAI,MAAO;QACxD;QAEA,OAAOoC,MAAMG,cAAc,GACvB;YAAEvC;YAAKN;YAAM4C;YAAMG;YAAcD;YAAUD,gBAAgBH,MAAMG,cAAc;QAAC,IAChF;YAAEvC;YAAKN;YAAM4C;YAAMG;YAAcD;QAAS;IAChD;IAEA,OAAOO,QAAQC,GAAG,CAACN;AACrB;AAOO,eAAe7E,2BAA2BwB,MAAqB;IACpE,MAAM6B,UAAU,MAAMhD,YAAY6B,QAAQ,CAACnC,QAAQyB,OAAOW,GAAG,EAAE,OAAO,OAAO;IAE7E,IAAIkB,QAAQE,KAAK,CAAC,yDAAyD;QACzE,mDAAmD;QACnD,MAAM,IAAIC,oBAAY,CAAC,sBAAsBH;IAC/C;IAEA,OAAOvC,sBAAsBuC,YAAY;AAC3C;AAEO,eAAe7C,oBAAoB,EACxCqB,IAAI,EACc,GAAG,CAAC,CAAC;IACvB,MAAMuD,UAAU,MAAMnF;IAEtB,IAAI,CAAC4B,MAAM;QACT,OAAOuD,OAAO,CAAC,EAAE,IAAI;IACvB;IAEA,OAAOA,QAAQJ,IAAI,CAAC,CAACxD,SAAWA,OAAOK,IAAI,KAAKA,SAAS;AAC3D;AAQO,eAAetB,6BAA6B4B,GAAY;IAC7D,IAAI;QACF,MAAMoC,QAAQ,MAAMnE,8BAA8B;YAAE+B;QAAI,GAAGd;QAC3D,OAAO,CAAC,CAACkD,KAAK,CAAClD,0BAA0B,CAACkC,KAAK,CAAC;IAClD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAerD,mBACpBsB,MAAoC;IAEpC,MAAM6D,aAAa,AAAC,CAAA,MAAMjF,8BAA8BoB,QAAQJ,uBAAsB,CAAE,CACtFA,uBACD;IAED,IAAIiE,YAAY;QACd,OAAOA,WAAW1C,IAAI,GAAGJ,KAAK,CAAC;IACjC;IAEA,MAAM+C,MAAM,AAAC,CAAA,MAAMlF,8BAA8BoB,QAAQL,cAAa,CAAE,CACtEA,cACD;IACD,OAAO;QAACmE;KAAI;AACd;AAEO,eAAelF,8BACpBoB,MAAqB,EACrB+D,IAAa;IAEb,aAAa;IACb,MAAMC,cAAczF,WAAW;QAACyB,OAAOW,GAAG;QAAE;QAAS;QAAWoD;KAAK,CAACpB,MAAM,CAACK;IAC7E,IAAI;QACF,2BAA2B;QAC3B,MAAMnB,UAAU,MAAMhD,YAAYoF,kBAAkB,CAACD;QACrD,QAAQ;QACR,2CAA2C;QAC3C,4BAA4B;QAE5B,IAAID,MAAM;YACRvE,MAAM,CAAC,4BAA4B,EAAEQ,OAAOW,GAAG,CAAC,QAAQ,EAAEoD,KAAK,QAAQ,EAAElC,QAAQ,CAAC,CAAC;YACnF,OAAO;gBACL,CAACkC,KAAK,EAAElC;YACV;QACF;QACA,MAAMkB,QAAQmB,yBAAyBrC;QAEvCrC,MAAM,CAAC,YAAY,CAAC,EAAEuD;QAEtB,OAAOA;IACT,EAAE,OAAOoB,OAAY;QACnB,gDAAgD;QAChD,MAAM,IAAInC,oBAAY,CAAC,CAAC,qCAAqC,EAAEhC,OAAOW,GAAG,CAAC,GAAG,EAAEwD,MAAMC,OAAO,EAAE;IAChG;AACF;AAEA,SAASF,yBAAyBG,sBAA8B;IAC9D,MAAMC,aAA+B,CAAC;IACtC,MAAMC,cAAc;IACpB,KAAK,MAAMxC,SAASsC,uBAAuBG,QAAQ,CAACD,aAAc;QAChED,UAAU,CAACvC,KAAK,CAAC,EAAE,CAAC,GAAGA,KAAK,CAAC,EAAE;IACjC;IACA,OAAOuC;AACT;AAMO,SAAShF,sBAAsBmF,UAAkB;IACtD,OAAOA,WACJtD,IAAI,GACJJ,KAAK,CAAC,WACN2D,KAAK;AACV"}