{"version": 3, "sources": ["../../../../src/start/interface/commandsTable.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport qrcode from 'qrcode-terminal';\nimport wrapAnsi from 'wrap-ansi';\n\nimport * as Log from '../../log';\n\nexport const BLT = '\\u203A';\n\nexport type StartOptions = {\n  isWebSocketsEnabled?: boolean;\n  devClient?: boolean;\n  reset?: boolean;\n  nonPersistent?: boolean;\n  maxWorkers?: number;\n  platforms?: ExpoConfig['platforms'];\n};\n\nexport const printHelp = (): void => {\n  logCommandsTable([{ key: '?', msg: 'show all commands' }]);\n};\n\n/** Print the world famous 'Expo QR Code'. */\nexport function printQRCode(url: string) {\n  qrcode.generate(url, { small: true }, (code) => Log.log(code));\n}\n\nexport const getTerminalColumns = () => process.stdout.columns || 80;\nexport const printItem = (text: string): string =>\n  `${BLT} ` + wrapAnsi(text, getTerminalColumns()).trimStart();\n\nexport function printUsage(\n  options: Pick<StartOptions, 'devClient' | 'isWebSocketsEnabled' | 'platforms'>,\n  { verbose }: { verbose: boolean }\n) {\n  const isMac = process.platform === 'darwin';\n\n  const { platforms = ['ios', 'android', 'web'] } = options;\n\n  const isAndroidDisabled = !platforms.includes('android');\n  const isIosDisabled = !platforms.includes('ios');\n  const isWebDisable = !platforms.includes('web');\n\n  const switchMsg = `switch to ${options.devClient === false ? 'development build' : 'Expo Go'}`;\n  const target = options.devClient === false ? `Expo Go` : 'development build';\n\n  Log.log();\n  Log.log(printItem(chalk`Using {cyan ${target}}`));\n\n  if (verbose) {\n    logCommandsTable([\n      { key: 's', msg: switchMsg },\n      {},\n      { key: 'a', msg: 'open Android', disabled: isAndroidDisabled },\n      { key: 'shift+a', msg: 'select an Android device or emulator', disabled: isAndroidDisabled },\n      isMac && { key: 'i', msg: 'open iOS simulator', disabled: isIosDisabled },\n      isMac && { key: 'shift+i', msg: 'select an iOS simulator', disabled: isIosDisabled },\n      { key: 'w', msg: 'open web', disabled: isWebDisable },\n      {},\n      { key: 'r', msg: 'reload app' },\n      !!options.isWebSocketsEnabled && { key: 'j', msg: 'open debugger' },\n      !!options.isWebSocketsEnabled && { key: 'm', msg: 'toggle menu' },\n      !!options.isWebSocketsEnabled && { key: 'shift+m', msg: 'more tools' },\n      { key: 'o', msg: 'open project code in your editor' },\n      { key: 'c', msg: 'show project QR' },\n      {},\n    ]);\n  } else {\n    logCommandsTable([\n      { key: 's', msg: switchMsg },\n      {},\n      { key: 'a', msg: 'open Android', disabled: isAndroidDisabled },\n      isMac && { key: 'i', msg: 'open iOS simulator', disabled: isIosDisabled },\n      { key: 'w', msg: 'open web', disabled: isWebDisable },\n      {},\n      { key: 'j', msg: 'open debugger' },\n      { key: 'r', msg: 'reload app' },\n      !!options.isWebSocketsEnabled && { key: 'm', msg: 'toggle menu' },\n      !!options.isWebSocketsEnabled && { key: 'shift+m', msg: 'more tools' },\n      { key: 'o', msg: 'open project code in your editor' },\n      {},\n    ]);\n  }\n}\n\nfunction logCommandsTable(\n  ui: (false | { key?: string; msg?: string; status?: string; disabled?: boolean })[]\n) {\n  Log.log(\n    ui\n      .filter(Boolean)\n      // @ts-ignore: filter doesn't work\n      .map(({ key, msg, status, disabled }) => {\n        if (!key) return '';\n        let view = `${BLT} `;\n        if (key.length === 1) view += 'Press ';\n        view += chalk`{bold ${key}} {dim │} `;\n        view += msg;\n        if (status) {\n          view += ` ${chalk.dim(`(${chalk.italic(status)})`)}`;\n        }\n        if (disabled) {\n          view = chalk.dim(view);\n        }\n        return view;\n      })\n      .join('\\n')\n  );\n}\n"], "names": ["BLT", "getTerminalColumns", "printHelp", "printItem", "printQRCode", "printUsage", "logCommandsTable", "key", "msg", "url", "qrcode", "generate", "small", "code", "Log", "log", "process", "stdout", "columns", "text", "wrapAnsi", "trimStart", "options", "verbose", "isMac", "platform", "platforms", "isAndroidDisabled", "includes", "isIosDisabled", "isWebDisable", "switchMsg", "devClient", "target", "chalk", "disabled", "isWebSocketsEnabled", "ui", "filter", "Boolean", "map", "status", "view", "length", "dim", "italic", "join"], "mappings": ";;;;;;;;;;;IAOaA,GAAG;eAAHA;;IAoBAC,kBAAkB;eAAlBA;;IATAC,SAAS;eAATA;;IAUAC,SAAS;eAATA;;IALGC,WAAW;eAAXA;;IAQAC,UAAU;eAAVA;;;;gEA9BE;;;;;;;gEACC;;;;;;;gEACE;;;;;;6DAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,MAAML,MAAM;AAWZ,MAAME,YAAY;IACvBI,iBAAiB;QAAC;YAAEC,KAAK;YAAKC,KAAK;QAAoB;KAAE;AAC3D;AAGO,SAASJ,YAAYK,GAAW;IACrCC,yBAAM,CAACC,QAAQ,CAACF,KAAK;QAAEG,OAAO;IAAK,GAAG,CAACC,OAASC,KAAIC,GAAG,CAACF;AAC1D;AAEO,MAAMZ,qBAAqB,IAAMe,QAAQC,MAAM,CAACC,OAAO,IAAI;AAC3D,MAAMf,YAAY,CAACgB,OACxB,GAAGnB,IAAI,CAAC,CAAC,GAAGoB,IAAAA,mBAAQ,EAACD,MAAMlB,sBAAsBoB,SAAS;AAErD,SAAShB,WACdiB,OAA8E,EAC9E,EAAEC,OAAO,EAAwB;IAEjC,MAAMC,QAAQR,QAAQS,QAAQ,KAAK;IAEnC,MAAM,EAAEC,YAAY;QAAC;QAAO;QAAW;KAAM,EAAE,GAAGJ;IAElD,MAAMK,oBAAoB,CAACD,UAAUE,QAAQ,CAAC;IAC9C,MAAMC,gBAAgB,CAACH,UAAUE,QAAQ,CAAC;IAC1C,MAAME,eAAe,CAACJ,UAAUE,QAAQ,CAAC;IAEzC,MAAMG,YAAY,CAAC,UAAU,EAAET,QAAQU,SAAS,KAAK,QAAQ,sBAAsB,WAAW;IAC9F,MAAMC,SAASX,QAAQU,SAAS,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG;IAEzDlB,KAAIC,GAAG;IACPD,KAAIC,GAAG,CAACZ,UAAU+B,IAAAA,gBAAK,CAAA,CAAC,YAAY,EAAED,OAAO,CAAC,CAAC;IAE/C,IAAIV,SAAS;QACXjB,iBAAiB;YACf;gBAAEC,KAAK;gBAAKC,KAAKuB;YAAU;YAC3B,CAAC;YACD;gBAAExB,KAAK;gBAAKC,KAAK;gBAAgB2B,UAAUR;YAAkB;YAC7D;gBAAEpB,KAAK;gBAAWC,KAAK;gBAAwC2B,UAAUR;YAAkB;YAC3FH,SAAS;gBAAEjB,KAAK;gBAAKC,KAAK;gBAAsB2B,UAAUN;YAAc;YACxEL,SAAS;gBAAEjB,KAAK;gBAAWC,KAAK;gBAA2B2B,UAAUN;YAAc;YACnF;gBAAEtB,KAAK;gBAAKC,KAAK;gBAAY2B,UAAUL;YAAa;YACpD,CAAC;YACD;gBAAEvB,KAAK;gBAAKC,KAAK;YAAa;YAC9B,CAAC,CAACc,QAAQc,mBAAmB,IAAI;gBAAE7B,KAAK;gBAAKC,KAAK;YAAgB;YAClE,CAAC,CAACc,QAAQc,mBAAmB,IAAI;gBAAE7B,KAAK;gBAAKC,KAAK;YAAc;YAChE,CAAC,CAACc,QAAQc,mBAAmB,IAAI;gBAAE7B,KAAK;gBAAWC,KAAK;YAAa;YACrE;gBAAED,KAAK;gBAAKC,KAAK;YAAmC;YACpD;gBAAED,KAAK;gBAAKC,KAAK;YAAkB;YACnC,CAAC;SACF;IACH,OAAO;QACLF,iBAAiB;YACf;gBAAEC,KAAK;gBAAKC,KAAKuB;YAAU;YAC3B,CAAC;YACD;gBAAExB,KAAK;gBAAKC,KAAK;gBAAgB2B,UAAUR;YAAkB;YAC7DH,SAAS;gBAAEjB,KAAK;gBAAKC,KAAK;gBAAsB2B,UAAUN;YAAc;YACxE;gBAAEtB,KAAK;gBAAKC,KAAK;gBAAY2B,UAAUL;YAAa;YACpD,CAAC;YACD;gBAAEvB,KAAK;gBAAKC,KAAK;YAAgB;YACjC;gBAAED,KAAK;gBAAKC,KAAK;YAAa;YAC9B,CAAC,CAACc,QAAQc,mBAAmB,IAAI;gBAAE7B,KAAK;gBAAKC,KAAK;YAAc;YAChE,CAAC,CAACc,QAAQc,mBAAmB,IAAI;gBAAE7B,KAAK;gBAAWC,KAAK;YAAa;YACrE;gBAAED,KAAK;gBAAKC,KAAK;YAAmC;YACpD,CAAC;SACF;IACH;AACF;AAEA,SAASF,iBACP+B,EAAmF;IAEnFvB,KAAIC,GAAG,CACLsB,GACGC,MAAM,CAACC,QACR,kCAAkC;KACjCC,GAAG,CAAC,CAAC,EAAEjC,GAAG,EAAEC,GAAG,EAAEiC,MAAM,EAAEN,QAAQ,EAAE;QAClC,IAAI,CAAC5B,KAAK,OAAO;QACjB,IAAImC,OAAO,GAAG1C,IAAI,CAAC,CAAC;QACpB,IAAIO,IAAIoC,MAAM,KAAK,GAAGD,QAAQ;QAC9BA,QAAQR,IAAAA,gBAAK,CAAA,CAAC,MAAM,EAAE3B,IAAI,UAAU,CAAC;QACrCmC,QAAQlC;QACR,IAAIiC,QAAQ;YACVC,QAAQ,CAAC,CAAC,EAAER,gBAAK,CAACU,GAAG,CAAC,CAAC,CAAC,EAAEV,gBAAK,CAACW,MAAM,CAACJ,QAAQ,CAAC,CAAC,GAAG;QACtD;QACA,IAAIN,UAAU;YACZO,OAAOR,gBAAK,CAACU,GAAG,CAACF;QACnB;QACA,OAAOA;IACT,GACCI,IAAI,CAAC;AAEZ"}