{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/AbstractProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport { CommandError } from '../../../../utils/errors';\nimport { parsePlistBuffer } from '../../../../utils/plist';\n\nconst BPLIST_MAGIC = Buffer.from('bplist00');\nconst debug = Debug('expo:apple-device:protocol');\n\nexport class ProtocolClientError extends CommandError {\n  constructor(\n    msg: string,\n    public error: Error,\n    public protocolMessage?: any\n  ) {\n    super(msg);\n  }\n}\n\nexport type ProtocolReaderCallback = (resp: any, err?: Error) => void;\n\nexport class ProtocolReaderFactory<T> {\n  constructor(private ProtocolReader: new (callback: ProtocolReaderCallback) => T) {}\n\n  create(callback: (resp: any, err?: Error) => void): T {\n    return new this.ProtocolReader(callback);\n  }\n}\n\nexport abstract class ProtocolReader {\n  protected body!: Buffer; // TODO: ! -> ?\n  protected bodyLength!: number; // TODO: ! -> ?\n  protected buffer = Buffer.alloc(0);\n  constructor(\n    protected headerSize: number,\n    protected callback: ProtocolReaderCallback\n  ) {\n    this.onData = this.onData.bind(this);\n  }\n\n  /** Returns length of body, or -1 if header doesn't contain length */\n  protected abstract parseHeader(data: Buffer): number;\n  protected abstract parseBody(data: Buffer): any;\n\n  onData(data?: Buffer) {\n    try {\n      // if there's data, add it on to existing buffer\n      this.buffer = data ? Buffer.concat([this.buffer, data]) : this.buffer;\n      // we haven't gotten the body length from the header yet\n      if (!this.bodyLength) {\n        if (this.buffer.length < this.headerSize) {\n          // partial header, wait for rest\n          return;\n        }\n        this.bodyLength = this.parseHeader(this.buffer);\n        // move on to body\n        this.buffer = this.buffer.slice(this.headerSize);\n        if (!this.buffer.length) {\n          // only got header, wait for body\n          return;\n        }\n      }\n      if (this.buffer.length < this.bodyLength) {\n        // wait for rest of body\n        return;\n      }\n\n      if (this.bodyLength === -1) {\n        this.callback(this.parseBody(this.buffer));\n        this.buffer = Buffer.alloc(0);\n      } else {\n        this.body = this.buffer.slice(0, this.bodyLength);\n        this.bodyLength -= this.body.length;\n        if (!this.bodyLength) {\n          this.callback(this.parseBody(this.body));\n        }\n        this.buffer = this.buffer.slice(this.body.length);\n        // There are multiple messages here, call parse again\n        if (this.buffer.length) {\n          this.onData();\n        }\n      }\n    } catch (err: any) {\n      this.callback(null, err);\n    }\n  }\n}\n\nexport abstract class PlistProtocolReader extends ProtocolReader {\n  protected parseBody(body: Buffer) {\n    if (BPLIST_MAGIC.compare(body, 0, 8) === 0) {\n      return parsePlistBuffer(body);\n    } else {\n      return plist.parse(body.toString('utf8'));\n    }\n  }\n}\n\nexport interface ProtocolWriter {\n  write(sock: Socket, msg: any): void;\n}\n\nexport abstract class ProtocolClient<MessageType = any> {\n  constructor(\n    public socket: Socket,\n    protected readerFactory: ProtocolReaderFactory<ProtocolReader>,\n    protected writer: ProtocolWriter\n  ) {}\n\n  sendMessage<ResponseType = any>(msg: MessageType): Promise<ResponseType>;\n  sendMessage<CallbackType = void, ResponseType = any>(\n    msg: MessageType,\n    callback: (response: ResponseType, resolve: any, reject: any) => void\n  ): Promise<CallbackType>;\n  sendMessage<CallbackType = void, ResponseType = any>(\n    msg: MessageType,\n    callback?: (response: ResponseType, resolve: any, reject: any) => void\n  ): Promise<CallbackType | ResponseType> {\n    const onError = (error: Error) => {\n      debug('Unexpected protocol socket error encountered: %s', error);\n      throw new ProtocolClientError(\n        `Unexpected protocol error encountered: ${error.message}`,\n        error,\n        msg\n      );\n    };\n\n    return new Promise<ResponseType | CallbackType>((resolve, reject) => {\n      const reader = this.readerFactory.create(async (response: ResponseType, error?: Error) => {\n        if (error) {\n          reject(error);\n          return;\n        }\n        if (callback) {\n          callback(\n            response,\n            (value: any) => {\n              this.socket.removeListener('data', reader.onData);\n              this.socket.removeListener('error', onError);\n              resolve(value);\n            },\n            reject\n          );\n        } else {\n          this.socket.removeListener('data', reader.onData);\n          this.socket.removeListener('error', onError);\n          resolve(response);\n        }\n      });\n      this.socket.on('data', reader.onData);\n      this.socket.on('error', onError);\n      this.writer.write(this.socket, msg);\n    });\n  }\n}\n"], "names": ["PlistProtocolReader", "ProtocolClient", "ProtocolClientError", "ProtocolReader", "ProtocolReaderFactory", "BPLIST_MAGIC", "<PERSON><PERSON><PERSON>", "from", "debug", "Debug", "CommandError", "constructor", "msg", "error", "protocolMessage", "create", "callback", "headerSize", "buffer", "alloc", "onData", "bind", "data", "concat", "<PERSON><PERSON><PERSON><PERSON>", "length", "parse<PERSON><PERSON><PERSON>", "slice", "parseBody", "body", "err", "compare", "parsePlist<PERSON><PERSON><PERSON>", "plist", "parse", "toString", "socket", "readerFactory", "writer", "sendMessage", "onError", "message", "Promise", "resolve", "reject", "reader", "response", "value", "removeListener", "on", "write"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IA0FqBA,mBAAmB;eAAnBA;;IAcAC,cAAc;eAAdA;;IA7FTC,mBAAmB;eAAnBA;;IAoBSC,cAAc;eAAdA;;IARTC,qBAAqB;eAArBA;;;;gEAtBK;;;;;;;gEACA;;;;;;wBAGW;wBACI;;;;;;AAEjC,MAAMC,eAAeC,OAAOC,IAAI,CAAC;AACjC,MAAMC,QAAQC,IAAAA,gBAAK,EAAC;AAEb,MAAMP,4BAA4BQ,oBAAY;IACnDC,YACEC,GAAW,EACX,AAAOC,KAAY,EACnB,AAAOC,eAAqB,CAC5B;QACA,KAAK,CAACF,WAHCC,QAAAA,YACAC,kBAAAA;IAGT;AACF;AAIO,MAAMV;IACXO,YAAY,AAAQR,cAA2D,CAAE;aAA7DA,iBAAAA;IAA8D;IAElFY,OAAOC,QAA0C,EAAK;QACpD,OAAO,IAAI,IAAI,CAACb,cAAc,CAACa;IACjC;AACF;AAEO,MAAeb;IAIpBQ,YACE,AAAUM,UAAkB,EAC5B,AAAUD,QAAgC,CAC1C;aAFUC,aAAAA;aACAD,WAAAA;aAHFE,SAASZ,OAAOa,KAAK,CAAC;QAK9B,IAAI,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,IAAI,CAAC,IAAI;IACrC;IAMAD,OAAOE,IAAa,EAAE;QACpB,IAAI;YACF,gDAAgD;YAChD,IAAI,CAACJ,MAAM,GAAGI,OAAOhB,OAAOiB,MAAM,CAAC;gBAAC,IAAI,CAACL,MAAM;gBAAEI;aAAK,IAAI,IAAI,CAACJ,MAAM;YACrE,wDAAwD;YACxD,IAAI,CAAC,IAAI,CAACM,UAAU,EAAE;gBACpB,IAAI,IAAI,CAACN,MAAM,CAACO,MAAM,GAAG,IAAI,CAACR,UAAU,EAAE;oBACxC,gCAAgC;oBAChC;gBACF;gBACA,IAAI,CAACO,UAAU,GAAG,IAAI,CAACE,WAAW,CAAC,IAAI,CAACR,MAAM;gBAC9C,kBAAkB;gBAClB,IAAI,CAACA,MAAM,GAAG,IAAI,CAACA,MAAM,CAACS,KAAK,CAAC,IAAI,CAACV,UAAU;gBAC/C,IAAI,CAAC,IAAI,CAACC,MAAM,CAACO,MAAM,EAAE;oBACvB,iCAAiC;oBACjC;gBACF;YACF;YACA,IAAI,IAAI,CAACP,MAAM,CAACO,MAAM,GAAG,IAAI,CAACD,UAAU,EAAE;gBACxC,wBAAwB;gBACxB;YACF;YAEA,IAAI,IAAI,CAACA,UAAU,KAAK,CAAC,GAAG;gBAC1B,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACY,SAAS,CAAC,IAAI,CAACV,MAAM;gBACxC,IAAI,CAACA,MAAM,GAAGZ,OAAOa,KAAK,CAAC;YAC7B,OAAO;gBACL,IAAI,CAACU,IAAI,GAAG,IAAI,CAACX,MAAM,CAACS,KAAK,CAAC,GAAG,IAAI,CAACH,UAAU;gBAChD,IAAI,CAACA,UAAU,IAAI,IAAI,CAACK,IAAI,CAACJ,MAAM;gBACnC,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;oBACpB,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACY,SAAS,CAAC,IAAI,CAACC,IAAI;gBACxC;gBACA,IAAI,CAACX,MAAM,GAAG,IAAI,CAACA,MAAM,CAACS,KAAK,CAAC,IAAI,CAACE,IAAI,CAACJ,MAAM;gBAChD,qDAAqD;gBACrD,IAAI,IAAI,CAACP,MAAM,CAACO,MAAM,EAAE;oBACtB,IAAI,CAACL,MAAM;gBACb;YACF;QACF,EAAE,OAAOU,KAAU;YACjB,IAAI,CAACd,QAAQ,CAAC,MAAMc;QACtB;IACF;AACF;AAEO,MAAe9B,4BAA4BG;IACtCyB,UAAUC,IAAY,EAAE;QAChC,IAAIxB,aAAa0B,OAAO,CAACF,MAAM,GAAG,OAAO,GAAG;YAC1C,OAAOG,IAAAA,wBAAgB,EAACH;QAC1B,OAAO;YACL,OAAOI,gBAAK,CAACC,KAAK,CAACL,KAAKM,QAAQ,CAAC;QACnC;IACF;AACF;AAMO,MAAelC;IACpBU,YACE,AAAOyB,MAAc,EACrB,AAAUC,aAAoD,EAC9D,AAAUC,MAAsB,CAChC;aAHOF,SAAAA;aACGC,gBAAAA;aACAC,SAAAA;IACT;IAOHC,YACE3B,GAAgB,EAChBI,QAAsE,EAChC;QACtC,MAAMwB,UAAU,CAAC3B;YACfL,MAAM,oDAAoDK;YAC1D,MAAM,IAAIX,oBACR,CAAC,uCAAuC,EAAEW,MAAM4B,OAAO,EAAE,EACzD5B,OACAD;QAEJ;QAEA,OAAO,IAAI8B,QAAqC,CAACC,SAASC;YACxD,MAAMC,SAAS,IAAI,CAACR,aAAa,CAACtB,MAAM,CAAC,OAAO+B,UAAwBjC;gBACtE,IAAIA,OAAO;oBACT+B,OAAO/B;oBACP;gBACF;gBACA,IAAIG,UAAU;oBACZA,SACE8B,UACA,CAACC;wBACC,IAAI,CAACX,MAAM,CAACY,cAAc,CAAC,QAAQH,OAAOzB,MAAM;wBAChD,IAAI,CAACgB,MAAM,CAACY,cAAc,CAAC,SAASR;wBACpCG,QAAQI;oBACV,GACAH;gBAEJ,OAAO;oBACL,IAAI,CAACR,MAAM,CAACY,cAAc,CAAC,QAAQH,OAAOzB,MAAM;oBAChD,IAAI,CAACgB,MAAM,CAACY,cAAc,CAAC,SAASR;oBACpCG,QAAQG;gBACV;YACF;YACA,IAAI,CAACV,MAAM,CAACa,EAAE,CAAC,QAAQJ,OAAOzB,MAAM;YACpC,IAAI,CAACgB,MAAM,CAACa,EAAE,CAAC,SAAST;YACxB,IAAI,CAACF,MAAM,CAACY,KAAK,CAAC,IAAI,CAACd,MAAM,EAAExB;QACjC;IACF;AACF"}