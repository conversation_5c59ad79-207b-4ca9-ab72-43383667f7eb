import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

const OrderFoodScreen = () => {
  const restaurants = [
    {
      id: '1',
      name: 'Restaurant Chez Tante Marie',
      cuisine: 'Cuisine ivoirienne',
      rating: 4.5,
      deliveryTime: '30-45 min',
      deliveryFee: 1000,
      image: 'https://via.placeholder.com/150x100',
    },
    {
      id: '2',
      name: 'Pizza Palace',
      cuisine: 'Pizza, Italien',
      rating: 4.2,
      deliveryTime: '25-35 min',
      deliveryFee: 1500,
      image: 'https://via.placeholder.com/150x100',
    },
    {
      id: '3',
      name: 'Burger King',
      cuisine: 'Fast Food',
      rating: 4.0,
      deliveryTime: '20-30 min',
      deliveryFee: 800,
      image: 'https://via.placeholder.com/150x100',
    },
  ];

  const categories = [
    { id: '1', name: 'Tout', icon: 'grid-outline' },
    { id: '2', name: 'Ivoiri<PERSON>', icon: 'restaurant-outline' },
    { id: '3', name: 'Pizza', icon: 'pizza-outline' },
    { id: '4', name: 'Burger', icon: 'fast-food-outline' },
    { id: '5', name: 'Desserts', icon: 'ice-cream-outline' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color="#9CA3AF" />
            <Text style={styles.searchPlaceholder}>Rechercher un restaurant...</Text>
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <Ionicons name="options" size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Categories */}
        <View style={styles.categoriesContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map((category) => (
              <TouchableOpacity key={category.id} style={styles.categoryItem}>
                <View style={styles.categoryIcon}>
                  <Ionicons name={category.icon as any} size={24} color="#3B82F6" />
                </View>
                <Text style={styles.categoryText}>{category.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Restaurants List */}
        <View style={styles.restaurantsContainer}>
          <Text style={styles.sectionTitle}>Restaurants disponibles</Text>
          
          {restaurants.map((restaurant) => (
            <TouchableOpacity key={restaurant.id} style={styles.restaurantCard}>
              <View style={styles.restaurantImage}>
                <Ionicons name="restaurant" size={32} color="#9CA3AF" />
              </View>
              
              <View style={styles.restaurantInfo}>
                <Text style={styles.restaurantName}>{restaurant.name}</Text>
                <Text style={styles.restaurantCuisine}>{restaurant.cuisine}</Text>
                
                <View style={styles.restaurantMeta}>
                  <View style={styles.ratingContainer}>
                    <Ionicons name="star" size={14} color="#F59E0B" />
                    <Text style={styles.rating}>{restaurant.rating}</Text>
                  </View>
                  
                  <View style={styles.metaItem}>
                    <Ionicons name="time" size={14} color="#6B7280" />
                    <Text style={styles.metaText}>{restaurant.deliveryTime}</Text>
                  </View>
                  
                  <View style={styles.metaItem}>
                    <Ionicons name="bicycle" size={14} color="#6B7280" />
                    <Text style={styles.metaText}>{restaurant.deliveryFee} FCFA</Text>
                  </View>
                </View>
              </View>
              
              <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Empty State for now */}
        <View style={styles.comingSoonContainer}>
          <Ionicons name="construct-outline" size={64} color="#9CA3AF" />
          <Text style={styles.comingSoonTitle}>Bientôt disponible</Text>
          <Text style={styles.comingSoonText}>
            La commande de nourriture sera bientôt disponible dans votre région.
            Restez connecté !
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  filterButton: {
    width: 48,
    height: 48,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesContainer: {
    paddingVertical: 16,
  },
  categoryItem: {
    alignItems: 'center',
    marginLeft: 20,
    width: 70,
  },
  categoryIcon: {
    width: 56,
    height: 56,
    backgroundColor: '#EBF4FF',
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 12,
    color: '#374151',
    textAlign: 'center',
  },
  restaurantsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
  },
  restaurantCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  restaurantImage: {
    width: 60,
    height: 60,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  restaurantInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  restaurantCuisine: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
    color: '#6B7280',
  },
  comingSoonContainer: {
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 40,
  },
  comingSoonTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  comingSoonText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default OrderFoodScreen;
