{"version": 3, "sources": ["../../../../src/run/android/resolveOptions.ts"], "sourcesContent": ["import { BuildCacheProvider, getConfig } from '@expo/config';\n\nimport { resolveDeviceAsync } from './resolveDevice';\nimport { GradleProps, resolveGradlePropsAsync } from './resolveGradlePropsAsync';\nimport { LaunchProps, resolveLaunchPropsAsync } from './resolveLaunchProps';\nimport { AndroidDeviceManager } from '../../start/platforms/android/AndroidDeviceManager';\nimport { resolveBuildCacheProvider } from '../../utils/build-cache-providers';\nimport { BundlerProps, resolveBundlerPropsAsync } from '../resolveBundlerProps';\n\nexport type Options = {\n  variant?: string;\n  device?: boolean | string;\n  port?: number;\n  bundler?: boolean;\n  install?: boolean;\n  buildCache?: boolean;\n  allArch?: boolean;\n  binary?: string;\n  appId?: string;\n};\n\nexport type ResolvedOptions = GradleProps &\n  BundlerProps &\n  LaunchProps & {\n    variant: string;\n    buildCache: boolean;\n    device: AndroidDeviceManager;\n    install: boolean;\n    architectures?: string;\n    appId?: string;\n    buildCacheProvider?: BuildCacheProvider;\n  };\n\nexport async function resolveOptionsAsync(\n  projectRoot: string,\n  options: Options\n): Promise<ResolvedOptions> {\n  // Resolve the device before the gradle props because we need the device to be running to get the ABI.\n  const device = await resolveDeviceAsync(options.device);\n\n  const projectConfig = getConfig(projectRoot);\n  const buildCacheProvider = await resolveBuildCacheProvider(\n    projectConfig.exp.experiments?.buildCacheProvider ??\n      projectConfig.exp.experiments?.remoteBuildCache?.provider,\n    projectRoot\n  );\n\n  return {\n    ...(await resolveBundlerPropsAsync(projectRoot, options)),\n    ...(await resolveGradlePropsAsync(projectRoot, options, device.device)),\n    ...(await resolveLaunchPropsAsync(projectRoot, options)),\n    variant: options.variant ?? 'debug',\n    // Resolve the device based on the provided device id or prompt\n    // from a list of devices (connected or simulated) that are filtered by the scheme.\n    device,\n    buildCache: !!options.buildCache,\n    install: !!options.install,\n    buildCacheProvider,\n  };\n}\n"], "names": ["resolveOptionsAsync", "projectRoot", "options", "projectConfig", "device", "resolveDeviceAsync", "getConfig", "buildCacheProvider", "resolveBuildCacheProvider", "exp", "experiments", "remoteBuildCache", "provider", "resolveBundlerPropsAsync", "resolveGradlePropsAsync", "resolveLaunchPropsAsync", "variant", "buildCache", "install"], "mappings": ";;;;+BAi<PERSON><PERSON>;;;eAAAA;;;;yBAjCwB;;;;;;+BAEX;yCACkB;oCACA;qCAEX;qCACa;AA0BhD,eAAeA,oBACpBC,WAAmB,EACnBC,OAAgB;QAOdC,gCACEA,iDAAAA;IANJ,sGAAsG;IACtG,MAAMC,SAAS,MAAMC,IAAAA,iCAAkB,EAACH,QAAQE,MAAM;IAEtD,MAAMD,gBAAgBG,IAAAA,mBAAS,EAACL;IAChC,MAAMM,qBAAqB,MAAMC,IAAAA,8CAAyB,EACxDL,EAAAA,iCAAAA,cAAcM,GAAG,CAACC,WAAW,qBAA7BP,+BAA+BI,kBAAkB,OAC/CJ,kCAAAA,cAAcM,GAAG,CAACC,WAAW,sBAA7BP,kDAAAA,gCAA+BQ,gBAAgB,qBAA/CR,gDAAiDS,QAAQ,GAC3DX;IAGF,OAAO;QACL,GAAI,MAAMY,IAAAA,6CAAwB,EAACZ,aAAaC,QAAQ;QACxD,GAAI,MAAMY,IAAAA,gDAAuB,EAACb,aAAaC,SAASE,OAAOA,MAAM,CAAC;QACtE,GAAI,MAAMW,IAAAA,2CAAuB,EAACd,aAAaC,QAAQ;QACvDc,SAASd,QAAQc,OAAO,IAAI;QAC5B,+DAA+D;QAC/D,mFAAmF;QACnFZ;QACAa,YAAY,CAAC,CAACf,QAAQe,UAAU;QAChCC,SAAS,CAAC,CAAChB,QAAQgB,OAAO;QAC1BX;IACF;AACF"}