{"version": 3, "sources": ["../../../../../src/start/platforms/ios/AppleDeviceManager.ts"], "sourcesContent": ["import * as osascript from '@expo/osascript';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { assertSystemRequirementsAsync } from './assertSystemRequirements';\nimport { ensureSimulatorAppRunningAsync } from './ensureSimulatorAppRunning';\nimport {\n  getBestBootedSimulatorAsync,\n  getBestUnbootedSimulatorAsync,\n  getSelectableSimulatorsAsync,\n} from './getBestSimulator';\nimport { promptAppleDeviceAsync } from './promptAppleDevice';\nimport * as SimControl from './simctl';\nimport { delayAsync, waitForActionAsync } from '../../../utils/delay';\nimport { CommandError } from '../../../utils/errors';\nimport { parsePlistAsync } from '../../../utils/plist';\nimport { validateUrl } from '../../../utils/url';\nimport { DeviceManager } from '../DeviceManager';\nimport { ExpoGoInstaller } from '../ExpoGoInstaller';\nimport { BaseResolveDeviceProps } from '../PlatformManager';\n\nconst debug = require('debug')('expo:start:platforms:ios:AppleDeviceManager') as typeof console.log;\n\nconst EXPO_GO_BUNDLE_IDENTIFIER = 'host.exp.Exponent';\n\n/**\n * Ensure a simulator is booted and the Simulator app is opened.\n * This is where any timeout related error handling should live.\n */\nexport async function ensureSimulatorOpenAsync(\n  { udid, osType }: Partial<Pick<SimControl.Device, 'udid' | 'osType'>> = {},\n  tryAgain: boolean = true\n): Promise<SimControl.Device> {\n  // Use a default simulator if none was specified\n  if (!udid) {\n    // If a simulator is open, side step the entire booting sequence.\n    const simulatorOpenedByApp = await getBestBootedSimulatorAsync({ osType });\n    if (simulatorOpenedByApp) {\n      return simulatorOpenedByApp;\n    }\n\n    // Otherwise, find the best possible simulator from user defaults and continue\n    const bestUdid = await getBestUnbootedSimulatorAsync({ osType });\n    if (!bestUdid) {\n      throw new CommandError('No simulators found.');\n    }\n    udid = bestUdid;\n  }\n\n  const bootedDevice = await waitForActionAsync({\n    action: () => {\n      // Just for the type check.\n      assert(udid);\n      return SimControl.bootAsync({ udid });\n    },\n  });\n\n  if (!bootedDevice) {\n    // Give it a second chance, this might not be needed but it could potentially lead to a better UX on slower devices.\n    if (tryAgain) {\n      return await ensureSimulatorOpenAsync({ udid, osType }, false);\n    }\n    // TODO: We should eliminate all needs for a timeout error, it's bad UX to get an error about the simulator not starting while the user can clearly see it starting on their slow computer.\n    throw new CommandError(\n      'SIMULATOR_TIMEOUT',\n      `Simulator didn't boot fast enough. Try opening Simulator first, then running your app.`\n    );\n  }\n  return bootedDevice;\n}\nexport class AppleDeviceManager extends DeviceManager<SimControl.Device> {\n  static assertSystemRequirementsAsync = assertSystemRequirementsAsync;\n\n  static async resolveAsync({\n    device,\n    shouldPrompt,\n  }: BaseResolveDeviceProps<\n    Partial<Pick<SimControl.Device, 'udid' | 'osType'>>\n  > = {}): Promise<AppleDeviceManager> {\n    if (shouldPrompt) {\n      const devices = await getSelectableSimulatorsAsync(device);\n      device = await promptAppleDeviceAsync(devices, device?.osType);\n    }\n\n    const booted = await ensureSimulatorOpenAsync(device);\n    return new AppleDeviceManager(booted);\n  }\n\n  get name() {\n    return this.device.name;\n  }\n\n  get identifier(): string {\n    return this.device.udid;\n  }\n\n  async getAppVersionAsync(\n    appId: string,\n    { containerPath }: { containerPath?: string } = {}\n  ): Promise<string | null> {\n    return await SimControl.getInfoPlistValueAsync(this.device, {\n      appId,\n      key: 'CFBundleShortVersionString',\n      containerPath,\n    });\n  }\n\n  async startAsync(): Promise<SimControl.Device> {\n    return ensureSimulatorOpenAsync({ osType: this.device.osType, udid: this.device.udid });\n  }\n\n  async launchApplicationIdAsync(appId: string) {\n    try {\n      const result = await SimControl.openAppIdAsync(this.device, {\n        appId,\n      });\n      if (result.status === 0) {\n        await this.activateWindowAsync();\n      } else {\n        throw new CommandError(result.stderr);\n      }\n    } catch (error: any) {\n      let errorMessage = `Couldn't open iOS app with ID \"${appId}\" on device \"${this.name}\".`;\n      if (error instanceof CommandError && error.code === 'APP_NOT_INSTALLED') {\n        if (appId === EXPO_GO_BUNDLE_IDENTIFIER) {\n          errorMessage = `Couldn't open Expo Go app on device \"${this.name}\". Install it: https://expo.dev/go.`;\n        } else {\n          errorMessage += `\\nThe app might not be installed, try installing it with: ${chalk.bold(\n            `npx expo run:ios -d ${this.device.udid}`\n          )}`;\n        }\n      }\n      if (error.stderr) {\n        errorMessage += chalk.gray(`\\n${error.stderr}`);\n      } else if (error.message) {\n        errorMessage += chalk.gray(`\\n${error.message}`);\n      }\n      throw new CommandError(errorMessage);\n    }\n  }\n\n  async installAppAsync(filePath: string) {\n    await SimControl.installAsync(this.device, {\n      filePath,\n    });\n\n    await this.waitForAppInstalledAsync(await this.getApplicationIdFromBundle(filePath));\n  }\n\n  private async getApplicationIdFromBundle(filePath: string): Promise<string> {\n    debug('getApplicationIdFromBundle:', filePath);\n    const builtInfoPlistPath = path.join(filePath, 'Info.plist');\n    if (fs.existsSync(builtInfoPlistPath)) {\n      const { CFBundleIdentifier } = await parsePlistAsync(builtInfoPlistPath);\n      debug('getApplicationIdFromBundle: using built Info.plist', CFBundleIdentifier);\n      return CFBundleIdentifier;\n    }\n    debug('getApplicationIdFromBundle: no Info.plist found');\n    return EXPO_GO_BUNDLE_IDENTIFIER;\n  }\n\n  private async waitForAppInstalledAsync(applicationId: string): Promise<boolean> {\n    while (true) {\n      if (await this.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId)) {\n        return true;\n      }\n      await delayAsync(100);\n    }\n  }\n\n  async uninstallAppAsync(appId: string) {\n    await SimControl.uninstallAsync(this.device, {\n      appId,\n    });\n  }\n\n  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(appId: string) {\n    return (\n      (await SimControl.getContainerPathAsync(this.device, {\n        appId,\n      })) ?? false\n    );\n  }\n\n  async openUrlAsync(url: string, options: { appId?: string } = {}) {\n    // Non-compliant URLs will be treated as application identifiers.\n    if (!validateUrl(url, { requireProtocol: true })) {\n      return await this.launchApplicationIdAsync(url);\n    }\n\n    try {\n      await SimControl.openUrlAsync(this.device, { url, appId: options.appId });\n    } catch (error: any) {\n      // 194 means the device does not conform to a given URL, in this case we'll assume that the desired app is not installed.\n      if (error.status === 194) {\n        // An error was encountered processing the command (domain=NSOSStatusErrorDomain, code=-10814):\n        // The operation couldn’t be completed. (OSStatus error -10814.)\n        //\n        // This can be thrown when no app conforms to the URI scheme that we attempted to open.\n        throw new CommandError(\n          'APP_NOT_INSTALLED',\n          `Device ${this.device.name} (${this.device.udid}) has no app to handle the URI: ${url}`\n        );\n      }\n      throw error;\n    }\n  }\n\n  async activateWindowAsync() {\n    await ensureSimulatorAppRunningAsync(this.device);\n    // TODO: Focus the individual window\n    await osascript.execAsync(`tell application \"Simulator\" to activate`);\n  }\n\n  getExpoGoAppId(): string {\n    return EXPO_GO_BUNDLE_IDENTIFIER;\n  }\n\n  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {\n    const installer = new ExpoGoInstaller('ios', EXPO_GO_BUNDLE_IDENTIFIER, sdkVersion);\n    return installer.ensureAsync(this);\n  }\n}\n"], "names": ["AppleDeviceManager", "ensureSimulatorOpenAsync", "debug", "require", "EXPO_GO_BUNDLE_IDENTIFIER", "udid", "osType", "try<PERSON><PERSON>n", "simulatorOpenedByApp", "getBestBootedSimulatorAsync", "bestUdid", "getBestUnbootedSimulatorAsync", "CommandError", "bootedDevice", "waitForActionAsync", "action", "assert", "SimControl", "bootAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assertSystemRequirementsAsync", "resolveAsync", "device", "should<PERSON>rompt", "devices", "getSelectableSimulatorsAsync", "promptAppleDeviceAsync", "booted", "name", "identifier", "getAppVersionAsync", "appId", "containerPath", "getInfoPlistValueAsync", "key", "startAsync", "launchApplicationIdAsync", "result", "openAppIdAsync", "status", "activateWindowAsync", "stderr", "error", "errorMessage", "code", "chalk", "bold", "gray", "message", "installAppAsync", "filePath", "installAsync", "waitForAppInstalledAsync", "getApplicationIdFromBundle", "builtInfoPlistPath", "path", "join", "fs", "existsSync", "CFBundleIdentifier", "parsePlistAsync", "applicationId", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "delayAsync", "uninstallAppAsync", "uninstallAsync", "getContainerPathAsync", "openUrlAsync", "url", "options", "validateUrl", "requireProtocol", "ensureSimulatorAppRunningAsync", "osascript", "execAsync", "getExpoGoAppId", "ensureExpoGoAsync", "sdkVersion", "installer", "ExpoGoInstaller", "ensureAsync"], "mappings": ";;;;;;;;;;;IAwEaA,kBAAkB;eAAlBA;;IAzCSC,wBAAwB;eAAxBA;;;;iEA/BK;;;;;;;gEACR;;;;;;;gEACD;;;;;;;gEACH;;;;;;;gEACE;;;;;;0CAE6B;2CACC;kCAKxC;mCACgC;gEACX;uBACmB;wBAClB;uBACG;qBACJ;+BACE;iCACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhC,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,4BAA4B;AAM3B,eAAeH,yBACpB,EAAEI,IAAI,EAAEC,MAAM,EAAuD,GAAG,CAAC,CAAC,EAC1EC,WAAoB,IAAI;IAExB,gDAAgD;IAChD,IAAI,CAACF,MAAM;QACT,iEAAiE;QACjE,MAAMG,uBAAuB,MAAMC,IAAAA,6CAA2B,EAAC;YAAEH;QAAO;QACxE,IAAIE,sBAAsB;YACxB,OAAOA;QACT;QAEA,8EAA8E;QAC9E,MAAME,WAAW,MAAMC,IAAAA,+CAA6B,EAAC;YAAEL;QAAO;QAC9D,IAAI,CAACI,UAAU;YACb,MAAM,IAAIE,oBAAY,CAAC;QACzB;QACAP,OAAOK;IACT;IAEA,MAAMG,eAAe,MAAMC,IAAAA,yBAAkB,EAAC;QAC5CC,QAAQ;YACN,2BAA2B;YAC3BC,IAAAA,iBAAM,EAACX;YACP,OAAOY,QAAWC,SAAS,CAAC;gBAAEb;YAAK;QACrC;IACF;IAEA,IAAI,CAACQ,cAAc;QACjB,oHAAoH;QACpH,IAAIN,UAAU;YACZ,OAAO,MAAMN,yBAAyB;gBAAEI;gBAAMC;YAAO,GAAG;QAC1D;QACA,2LAA2L;QAC3L,MAAM,IAAIM,oBAAY,CACpB,qBACA,CAAC,sFAAsF,CAAC;IAE5F;IACA,OAAOC;AACT;AACO,MAAMb,2BAA2BmB,4BAAa;qBAC5CC,gCAAgCA,uDAA6B;IAEpE,aAAaC,aAAa,EACxBC,MAAM,EACNC,YAAY,EAGb,GAAG,CAAC,CAAC,EAA+B;QACnC,IAAIA,cAAc;YAChB,MAAMC,UAAU,MAAMC,IAAAA,8CAA4B,EAACH;YACnDA,SAAS,MAAMI,IAAAA,yCAAsB,EAACF,SAASF,0BAAAA,OAAQhB,MAAM;QAC/D;QAEA,MAAMqB,SAAS,MAAM1B,yBAAyBqB;QAC9C,OAAO,IAAItB,mBAAmB2B;IAChC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACN,MAAM,CAACM,IAAI;IACzB;IAEA,IAAIC,aAAqB;QACvB,OAAO,IAAI,CAACP,MAAM,CAACjB,IAAI;IACzB;IAEA,MAAMyB,mBACJC,KAAa,EACb,EAAEC,aAAa,EAA8B,GAAG,CAAC,CAAC,EAC1B;QACxB,OAAO,MAAMf,QAAWgB,sBAAsB,CAAC,IAAI,CAACX,MAAM,EAAE;YAC1DS;YACAG,KAAK;YACLF;QACF;IACF;IAEA,MAAMG,aAAyC;QAC7C,OAAOlC,yBAAyB;YAAEK,QAAQ,IAAI,CAACgB,MAAM,CAAChB,MAAM;YAAED,MAAM,IAAI,CAACiB,MAAM,CAACjB,IAAI;QAAC;IACvF;IAEA,MAAM+B,yBAAyBL,KAAa,EAAE;QAC5C,IAAI;YACF,MAAMM,SAAS,MAAMpB,QAAWqB,cAAc,CAAC,IAAI,CAAChB,MAAM,EAAE;gBAC1DS;YACF;YACA,IAAIM,OAAOE,MAAM,KAAK,GAAG;gBACvB,MAAM,IAAI,CAACC,mBAAmB;YAChC,OAAO;gBACL,MAAM,IAAI5B,oBAAY,CAACyB,OAAOI,MAAM;YACtC;QACF,EAAE,OAAOC,OAAY;YACnB,IAAIC,eAAe,CAAC,+BAA+B,EAAEZ,MAAM,aAAa,EAAE,IAAI,CAACH,IAAI,CAAC,EAAE,CAAC;YACvF,IAAIc,iBAAiB9B,oBAAY,IAAI8B,MAAME,IAAI,KAAK,qBAAqB;gBACvE,IAAIb,UAAU3B,2BAA2B;oBACvCuC,eAAe,CAAC,qCAAqC,EAAE,IAAI,CAACf,IAAI,CAAC,mCAAmC,CAAC;gBACvG,OAAO;oBACLe,gBAAgB,CAAC,0DAA0D,EAAEE,gBAAK,CAACC,IAAI,CACrF,CAAC,oBAAoB,EAAE,IAAI,CAACxB,MAAM,CAACjB,IAAI,EAAE,GACxC;gBACL;YACF;YACA,IAAIqC,MAAMD,MAAM,EAAE;gBAChBE,gBAAgBE,gBAAK,CAACE,IAAI,CAAC,CAAC,EAAE,EAAEL,MAAMD,MAAM,EAAE;YAChD,OAAO,IAAIC,MAAMM,OAAO,EAAE;gBACxBL,gBAAgBE,gBAAK,CAACE,IAAI,CAAC,CAAC,EAAE,EAAEL,MAAMM,OAAO,EAAE;YACjD;YACA,MAAM,IAAIpC,oBAAY,CAAC+B;QACzB;IACF;IAEA,MAAMM,gBAAgBC,QAAgB,EAAE;QACtC,MAAMjC,QAAWkC,YAAY,CAAC,IAAI,CAAC7B,MAAM,EAAE;YACzC4B;QACF;QAEA,MAAM,IAAI,CAACE,wBAAwB,CAAC,MAAM,IAAI,CAACC,0BAA0B,CAACH;IAC5E;IAEA,MAAcG,2BAA2BH,QAAgB,EAAmB;QAC1EhD,MAAM,+BAA+BgD;QACrC,MAAMI,qBAAqBC,eAAI,CAACC,IAAI,CAACN,UAAU;QAC/C,IAAIO,aAAE,CAACC,UAAU,CAACJ,qBAAqB;YACrC,MAAM,EAAEK,kBAAkB,EAAE,GAAG,MAAMC,IAAAA,sBAAe,EAACN;YACrDpD,MAAM,sDAAsDyD;YAC5D,OAAOA;QACT;QACAzD,MAAM;QACN,OAAOE;IACT;IAEA,MAAcgD,yBAAyBS,aAAqB,EAAoB;QAC9E,MAAO,KAAM;YACX,IAAI,MAAM,IAAI,CAACC,mDAAmD,CAACD,gBAAgB;gBACjF,OAAO;YACT;YACA,MAAME,IAAAA,iBAAU,EAAC;QACnB;IACF;IAEA,MAAMC,kBAAkBjC,KAAa,EAAE;QACrC,MAAMd,QAAWgD,cAAc,CAAC,IAAI,CAAC3C,MAAM,EAAE;YAC3CS;QACF;IACF;IAEA,MAAM+B,oDAAoD/B,KAAa,EAAE;QACvE,OACE,AAAC,MAAMd,QAAWiD,qBAAqB,CAAC,IAAI,CAAC5C,MAAM,EAAE;YACnDS;QACF,MAAO;IAEX;IAEA,MAAMoC,aAAaC,GAAW,EAAEC,UAA8B,CAAC,CAAC,EAAE;QAChE,iEAAiE;QACjE,IAAI,CAACC,IAAAA,gBAAW,EAACF,KAAK;YAAEG,iBAAiB;QAAK,IAAI;YAChD,OAAO,MAAM,IAAI,CAACnC,wBAAwB,CAACgC;QAC7C;QAEA,IAAI;YACF,MAAMnD,QAAWkD,YAAY,CAAC,IAAI,CAAC7C,MAAM,EAAE;gBAAE8C;gBAAKrC,OAAOsC,QAAQtC,KAAK;YAAC;QACzE,EAAE,OAAOW,OAAY;YACnB,yHAAyH;YACzH,IAAIA,MAAMH,MAAM,KAAK,KAAK;gBACxB,+FAA+F;gBAC/F,gEAAgE;gBAChE,EAAE;gBACF,uFAAuF;gBACvF,MAAM,IAAI3B,oBAAY,CACpB,qBACA,CAAC,OAAO,EAAE,IAAI,CAACU,MAAM,CAACM,IAAI,CAAC,EAAE,EAAE,IAAI,CAACN,MAAM,CAACjB,IAAI,CAAC,gCAAgC,EAAE+D,KAAK;YAE3F;YACA,MAAM1B;QACR;IACF;IAEA,MAAMF,sBAAsB;QAC1B,MAAMgC,IAAAA,yDAA8B,EAAC,IAAI,CAAClD,MAAM;QAChD,oCAAoC;QACpC,MAAMmD,aAAUC,SAAS,CAAC,CAAC,wCAAwC,CAAC;IACtE;IAEAC,iBAAyB;QACvB,OAAOvE;IACT;IAEA,MAAMwE,kBAAkBC,UAAkB,EAAoB;QAC5D,MAAMC,YAAY,IAAIC,gCAAe,CAAC,OAAO3E,2BAA2ByE;QACxE,OAAOC,UAAUE,WAAW,CAAC,IAAI;IACnC;AACF"}