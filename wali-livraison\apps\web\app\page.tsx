import { <PERSON><PERSON> } from "@repo/ui/button";
import { Card } from "@repo/ui/card";
import { Truck, Package, ShoppingBag, Users } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Truck className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Wali Livraison</h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="/auth/login" className="text-gray-500 hover:text-gray-900">
                Connexion
              </Link>
              <Link href="/auth/register">
                <Button>S'inscrire</Button>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
            <span className="block">Livraison rapide</span>
            <span className="block text-blue-600">en Côte d'Ivoire</span>
          </h1>
          <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
            Envoyez vos colis, commandez vos repas et faites vos courses en ligne.
            Livraison rapide et sécurisée partout en Côte d'Ivoire.
          </p>
          <div className="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
            <div className="rounded-md shadow">
              <Link href="/auth/register">
                <Button size="lg" className="w-full">
                  Commencer maintenant
                </Button>
              </Link>
            </div>
            <div className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
              <Button variant="outline" size="lg" className="w-full">
                En savoir plus
              </Button>
            </div>
          </div>
        </div>

        {/* Services Section */}
        <div className="mt-20">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Nos Services
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              Une plateforme complète pour tous vos besoins de livraison
            </p>
          </div>

          <div className="mt-12 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {/* Service 1: Colis */}
            <Card className="hover:shadow-lg transition-shadow">
              <div className="p-6 text-center">
                <div className="w-12 h-12 mx-auto bg-blue-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">
                  Envoi de Colis
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Envoyez vos colis rapidement et en toute sécurité partout en Côte d'Ivoire
                </p>
                <Button variant="outline" className="mt-4">
                  En savoir plus
                </Button>
              </div>
            </Card>

            {/* Service 2: Repas */}
            <Card className="hover:shadow-lg transition-shadow">
              <div className="p-6 text-center">
                <div className="w-12 h-12 mx-auto bg-green-100 rounded-lg flex items-center justify-center">
                  <ShoppingBag className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">
                  Livraison de Repas
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Commandez vos repas préférés depuis nos restaurants partenaires
                </p>
                <Button variant="outline" className="mt-4">
                  Commander
                </Button>
              </div>
            </Card>

            {/* Service 3: Courses */}
            <Card className="hover:shadow-lg transition-shadow">
              <div className="p-6 text-center">
                <div className="w-12 h-12 mx-auto bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">
                  Courses & Shopping
                </h3>
                <p className="mt-2 text-base text-gray-500">
                  Faites vos courses en ligne et recevez-les à domicile
                </p>
                <Button variant="outline" className="mt-4">
                  Faire ses courses
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 bg-blue-600 rounded-lg">
          <div className="px-6 py-12 sm:px-12 sm:py-16 lg:px-16">
            <div className="text-center">
              <h2 className="text-3xl font-extrabold text-white">
                Prêt à commencer ?
              </h2>
              <p className="mt-4 text-lg text-blue-100">
                Rejoignez des milliers d'utilisateurs qui font confiance à Wali Livraison
              </p>
              <div className="mt-8">
                <Link href="/auth/register">
                  <Button size="lg" variant="secondary">
                    Créer un compte gratuit
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center">
              <Truck className="h-6 w-6 text-blue-600 mr-2" />
              <span className="text-lg font-semibold text-gray-900">Wali Livraison</span>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              © 2024 Wali Livraison. Tous droits réservés.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
