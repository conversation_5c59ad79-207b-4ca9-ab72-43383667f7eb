{"version": 3, "names": ["require", "visitors", "exports", "_t", "cache", "_traverseNode", "_index", "_index2", "_hub", "VISITOR_KEYS", "removeProperties", "traverseFast", "traverse", "parent", "opts", "scope", "state", "parentPath", "visitSelf", "noScope", "type", "Error", "explode", "traverseNode", "_default", "default", "verify", "cheap", "node", "enter", "path", "<PERSON><PERSON><PERSON><PERSON>", "clearNode", "tree", "hasType", "denylistTypes", "includes", "skip", "stop"], "sources": ["../src/index.ts"], "sourcesContent": ["import \"./path/context.ts\"; // We have some cycles, this ensures correct order to avoid TDZ\nimport * as visitors from \"./visitors.ts\";\nimport {\n  VISITOR_KEYS,\n  removeProperties,\n  type RemovePropertiesOptions,\n  traverseFast,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as cache from \"./cache.ts\";\nimport type NodePath from \"./path/index.ts\";\nimport type { default as Scope, Binding } from \"./scope/index.ts\";\nimport type { ExplodedVisitor, Visitor, VisitorBase } from \"./types.ts\";\nimport { traverseNode } from \"./traverse-node.ts\";\n\nexport type { ExplodedVisitor, Visitor, VisitorBase, Binding };\nexport { default as NodePath } from \"./path/index.ts\";\nexport { default as Scope } from \"./scope/index.ts\";\nexport { default as Hub } from \"./hub.ts\";\nexport type { HubInterface } from \"./hub.ts\";\n\nexport { visitors };\n\nexport type TraverseOptions<S = t.Node> = {\n  scope?: Scope;\n  noScope?: boolean;\n  denylist?: string[];\n  shouldSkip?: (node: NodePath) => boolean;\n} & Visitor<S>;\n\nexport type ExplodedTraverseOptions<S = t.Node> = TraverseOptions<S> &\n  ExplodedVisitor<S>;\n\nfunction traverse<S>(\n  parent: t.Node,\n  opts: TraverseOptions<S>,\n  scope: Scope | undefined,\n  state: S,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n): void;\n\nfunction traverse(\n  parent: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n): void;\n\nfunction traverse<Options extends TraverseOptions>(\n  parent: t.Node,\n  // @ts-expect-error provide {} as default value for Options\n  opts: Options = {},\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n  visitSelf?: boolean,\n) {\n  if (!parent) return;\n\n  if (!opts.noScope && !scope) {\n    if (parent.type !== \"Program\" && parent.type !== \"File\") {\n      throw new Error(\n        \"You must pass a scope and parentPath unless traversing a Program/File. \" +\n          `Instead of that you tried to traverse a ${parent.type} node without ` +\n          \"passing scope and parentPath.\",\n      );\n    }\n  }\n\n  if (!parentPath && visitSelf) {\n    throw new Error(\"visitSelf can only be used when providing a NodePath.\");\n  }\n\n  if (!VISITOR_KEYS[parent.type]) {\n    return;\n  }\n\n  visitors.explode(opts as Visitor);\n\n  traverseNode(\n    parent,\n    opts as ExplodedVisitor,\n    scope,\n    state,\n    parentPath,\n    /* skipKeys */ null,\n    visitSelf,\n  );\n}\n\nexport default traverse;\n\ntraverse.visitors = visitors;\ntraverse.verify = visitors.verify;\ntraverse.explode = visitors.explode;\n\ntraverse.cheap = function (node: t.Node, enter: (node: t.Node) => void) {\n  traverseFast(node, enter);\n  return;\n};\n\ntraverse.node = function (\n  node: t.Node,\n  opts: ExplodedTraverseOptions,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n) {\n  traverseNode(node, opts, scope, state, path, skipKeys);\n  // traverse.node always returns undefined\n};\n\ntraverse.clearNode = function (node: t.Node, opts?: RemovePropertiesOptions) {\n  removeProperties(node, opts);\n};\n\ntraverse.removeProperties = function (\n  tree: t.Node,\n  opts?: RemovePropertiesOptions,\n) {\n  traverseFast(tree, traverse.clearNode, opts);\n  return tree;\n};\n\ntraverse.hasType = function (\n  tree: t.Node,\n  type: t.Node[\"type\"],\n  denylistTypes?: Array<string>,\n): boolean {\n  // the node we're searching in is denylisted\n  if (denylistTypes?.includes(tree.type)) return false;\n\n  // the type we're looking for is the same as the passed node\n  if (tree.type === type) return true;\n\n  return traverseFast(tree, function (node) {\n    if (denylistTypes?.includes(node.type)) {\n      return traverseFast.skip;\n    }\n    if (node.type === type) {\n      return traverseFast.stop;\n    }\n  });\n};\n\ntraverse.cache = cache;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAAA,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAA0CE,OAAA,CAAAD,QAAA,GAAAA,QAAA;AAC1C,IAAAE,EAAA,GAAAH,OAAA;AAOA,IAAAI,KAAA,GAAAJ,OAAA;AAIA,IAAAK,aAAA,GAAAL,OAAA;AAGA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,IAAA,GAAAR,OAAA;AAA0C;EAfxCS,YAAY;EACZC,gBAAgB;EAEhBC;AAAY,IAAAR,EAAA;AA6Cd,SAASS,QAAQA,CACfC,MAAc,EAEdC,IAAa,GAAG,CAAC,CAAC,EAClBC,KAAa,EACbC,KAAW,EACXC,UAAqB,EACrBC,SAAmB,EACnB;EACA,IAAI,CAACL,MAAM,EAAE;EAEb,IAAI,CAACC,IAAI,CAACK,OAAO,IAAI,CAACJ,KAAK,EAAE;IAC3B,IAAIF,MAAM,CAACO,IAAI,KAAK,SAAS,IAAIP,MAAM,CAACO,IAAI,KAAK,MAAM,EAAE;MACvD,MAAM,IAAIC,KAAK,CACb,yEAAyE,GACvE,2CAA2CR,MAAM,CAACO,IAAI,gBAAgB,GACtE,+BACJ,CAAC;IACH;EACF;EAEA,IAAI,CAACH,UAAU,IAAIC,SAAS,EAAE;IAC5B,MAAM,IAAIG,KAAK,CAAC,uDAAuD,CAAC;EAC1E;EAEA,IAAI,CAACZ,YAAY,CAACI,MAAM,CAACO,IAAI,CAAC,EAAE;IAC9B;EACF;EAEAnB,QAAQ,CAACqB,OAAO,CAACR,IAAe,CAAC;EAEjC,IAAAS,0BAAY,EACVV,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,UAAU,EACK,IAAI,EACnBC,SACF,CAAC;AACH;AAAC,IAAAM,QAAA,GAAAtB,OAAA,CAAAuB,OAAA,GAEcb,QAAQ;AAEvBA,QAAQ,CAACX,QAAQ,GAAGA,QAAQ;AAC5BW,QAAQ,CAACc,MAAM,GAAGzB,QAAQ,CAACyB,MAAM;AACjCd,QAAQ,CAACU,OAAO,GAAGrB,QAAQ,CAACqB,OAAO;AAEnCV,QAAQ,CAACe,KAAK,GAAG,UAAUC,IAAY,EAAEC,KAA6B,EAAE;EACtElB,YAAY,CAACiB,IAAI,EAAEC,KAAK,CAAC;EACzB;AACF,CAAC;AAEDjB,QAAQ,CAACgB,IAAI,GAAG,UACdA,IAAY,EACZd,IAA6B,EAC7BC,KAAa,EACbC,KAAW,EACXc,IAAe,EACfC,QAAkC,EAClC;EACA,IAAAR,0BAAY,EAACK,IAAI,EAAEd,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEc,IAAI,EAAEC,QAAQ,CAAC;AAExD,CAAC;AAEDnB,QAAQ,CAACoB,SAAS,GAAG,UAAUJ,IAAY,EAAEd,IAA8B,EAAE;EAC3EJ,gBAAgB,CAACkB,IAAI,EAAEd,IAAI,CAAC;AAC9B,CAAC;AAEDF,QAAQ,CAACF,gBAAgB,GAAG,UAC1BuB,IAAY,EACZnB,IAA8B,EAC9B;EACAH,YAAY,CAACsB,IAAI,EAAErB,QAAQ,CAACoB,SAAS,EAAElB,IAAI,CAAC;EAC5C,OAAOmB,IAAI;AACb,CAAC;AAEDrB,QAAQ,CAACsB,OAAO,GAAG,UACjBD,IAAY,EACZb,IAAoB,EACpBe,aAA6B,EACpB;EAET,IAAIA,aAAa,YAAbA,aAAa,CAAEC,QAAQ,CAACH,IAAI,CAACb,IAAI,CAAC,EAAE,OAAO,KAAK;EAGpD,IAAIa,IAAI,CAACb,IAAI,KAAKA,IAAI,EAAE,OAAO,IAAI;EAEnC,OAAOT,YAAY,CAACsB,IAAI,EAAE,UAAUL,IAAI,EAAE;IACxC,IAAIO,aAAa,YAAbA,aAAa,CAAEC,QAAQ,CAACR,IAAI,CAACR,IAAI,CAAC,EAAE;MACtC,OAAOT,YAAY,CAAC0B,IAAI;IAC1B;IACA,IAAIT,IAAI,CAACR,IAAI,KAAKA,IAAI,EAAE;MACtB,OAAOT,YAAY,CAAC2B,IAAI;IAC1B;EACF,CAAC,CAAC;AACJ,CAAC;AAED1B,QAAQ,CAACR,KAAK,GAAGA,KAAK", "ignoreList": []}