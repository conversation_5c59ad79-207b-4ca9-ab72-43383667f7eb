import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { PaymentMethod } from '@prisma/client';
export interface CreatePaymentDto {
    orderId: string;
    amount: number;
    paymentMethod: PaymentMethod;
    phoneNumber?: string;
    currency?: string;
}
export interface PaymentResponse {
    success: boolean;
    transactionId: string;
    providerId?: string;
    message?: string;
}
export declare class PaymentsService {
    private readonly configService;
    private readonly prisma;
    private readonly stripe;
    constructor(configService: ConfigService, prisma: PrismaService);
    processPayment(createPaymentDto: CreatePaymentDto, userId: string): Promise<PaymentResponse>;
    private processStripePayment;
    private processOrangeMoneyPayment;
    private processMTNMoneyPayment;
    private processWavePayment;
    private processCashPayment;
    getPaymentStatus(transactionId: string, paymentMethod: PaymentMethod): Promise<{
        transactionId: string;
        status: string;
        amount: number;
        paymentMethod: import(".prisma/client").$Enums.PaymentMethod;
        createdAt: Date;
    }>;
}
