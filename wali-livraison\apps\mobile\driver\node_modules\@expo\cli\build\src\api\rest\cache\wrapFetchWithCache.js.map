{"version": 3, "sources": ["../../../../../src/api/rest/cache/wrapFetchWithCache.ts"], "sourcesContent": ["import { Response, type RequestInfo, type RequestInit } from 'undici';\n\nimport { getRequestCacheKey, getResponseInfo, type ResponseCache } from './ResponseCache';\nimport type { FetchLike } from '../client.types';\n\nconst debug = require('debug')('expo:undici-cache');\n\nexport function wrapFetchWithCache(fetch: Fetch<PERSON>ike, cache: ResponseCache): Fetch<PERSON>ike {\n  return async function cachedFetch(url: RequestInfo, init?: RequestInit) {\n    const cacheKey = getRequestCacheKey(url, init);\n    const cachedResponse = await cache.get(cacheKey);\n    if (cachedResponse) {\n      return new Response(cachedResponse.body, cachedResponse.info);\n    }\n\n    await lock(cacheKey);\n\n    try {\n      // Retry loading from cache, in case it was stored during the lock\n      let cachedResponse = await cache.get(cacheKey);\n      if (cachedResponse) {\n        return new Response(cachedResponse.body, cachedResponse.info);\n      }\n\n      // Execute the fetch request\n      const response = await fetch(url, init);\n      if (!response.ok || !response.body) {\n        return response;\n      }\n\n      // Cache the response\n      cachedResponse = await cache.set(cacheKey, {\n        body: response.body,\n        info: getResponseInfo(response),\n      });\n\n      // Warn through debug logs that caching failed\n      if (!cachedResponse) {\n        debug(`Failed to cache response for: ${url}`);\n        await cache.remove(cacheKey);\n        return response;\n      }\n\n      // Return the cached response\n      return new Response(cachedResponse.body, cachedResponse.info);\n    } finally {\n      unlock(cacheKey);\n    }\n  };\n}\n\nconst lockPromiseForKey: Record<string, Promise<any>> = {};\nconst unlockFunctionForKey: Record<string, any> = {};\n\nasync function lock(key: string) {\n  if (!lockPromiseForKey[key]) {\n    lockPromiseForKey[key] = Promise.resolve();\n  }\n\n  const takeLockPromise = lockPromiseForKey[key];\n  lockPromiseForKey[key] = takeLockPromise.then(\n    () =>\n      new Promise((fulfill) => {\n        unlockFunctionForKey[key] = fulfill;\n      })\n  );\n\n  return takeLockPromise;\n}\n\nfunction unlock(key: string) {\n  if (unlockFunctionForKey[key]) {\n    unlockFunctionForKey[key]();\n    delete unlockFunctionForKey[key];\n  }\n}\n"], "names": ["wrapFetchWithCache", "debug", "require", "fetch", "cache", "cachedFetch", "url", "init", "cache<PERSON>ey", "getRequestCacheKey", "cachedResponse", "get", "Response", "body", "info", "lock", "response", "ok", "set", "getResponseInfo", "remove", "unlock", "lockPromiseFor<PERSON>ey", "unlockFunctionForKey", "key", "Promise", "resolve", "takeLockPromise", "then", "fulfill"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;;yBAP6C;;;;;;+BAEW;AAGxE,MAAMC,QAAQC,QAAQ,SAAS;AAExB,SAASF,mBAAmBG,KAAgB,EAAEC,KAAoB;IACvE,OAAO,eAAeC,YAAYC,GAAgB,EAAEC,IAAkB;QACpE,MAAMC,WAAWC,IAAAA,iCAAkB,EAACH,KAAKC;QACzC,MAAMG,iBAAiB,MAAMN,MAAMO,GAAG,CAACH;QACvC,IAAIE,gBAAgB;YAClB,OAAO,IAAIE,CAAAA,SAAO,UAAC,CAACF,eAAeG,IAAI,EAAEH,eAAeI,IAAI;QAC9D;QAEA,MAAMC,KAAKP;QAEX,IAAI;YACF,kEAAkE;YAClE,IAAIE,iBAAiB,MAAMN,MAAMO,GAAG,CAACH;YACrC,IAAIE,gBAAgB;gBAClB,OAAO,IAAIE,CAAAA,SAAO,UAAC,CAACF,eAAeG,IAAI,EAAEH,eAAeI,IAAI;YAC9D;YAEA,4BAA4B;YAC5B,MAAME,WAAW,MAAMb,MAAMG,KAAKC;YAClC,IAAI,CAACS,SAASC,EAAE,IAAI,CAACD,SAASH,IAAI,EAAE;gBAClC,OAAOG;YACT;YAEA,qBAAqB;YACrBN,iBAAiB,MAAMN,MAAMc,GAAG,CAACV,UAAU;gBACzCK,MAAMG,SAASH,IAAI;gBACnBC,MAAMK,IAAAA,8BAAe,EAACH;YACxB;YAEA,8CAA8C;YAC9C,IAAI,CAACN,gBAAgB;gBACnBT,MAAM,CAAC,8BAA8B,EAAEK,KAAK;gBAC5C,MAAMF,MAAMgB,MAAM,CAACZ;gBACnB,OAAOQ;YACT;YAEA,6BAA6B;YAC7B,OAAO,IAAIJ,CAAAA,SAAO,UAAC,CAACF,eAAeG,IAAI,EAAEH,eAAeI,IAAI;QAC9D,SAAU;YACRO,OAAOb;QACT;IACF;AACF;AAEA,MAAMc,oBAAkD,CAAC;AACzD,MAAMC,uBAA4C,CAAC;AAEnD,eAAeR,KAAKS,GAAW;IAC7B,IAAI,CAACF,iBAAiB,CAACE,IAAI,EAAE;QAC3BF,iBAAiB,CAACE,IAAI,GAAGC,QAAQC,OAAO;IAC1C;IAEA,MAAMC,kBAAkBL,iBAAiB,CAACE,IAAI;IAC9CF,iBAAiB,CAACE,IAAI,GAAGG,gBAAgBC,IAAI,CAC3C,IACE,IAAIH,QAAQ,CAACI;YACXN,oBAAoB,CAACC,IAAI,GAAGK;QAC9B;IAGJ,OAAOF;AACT;AAEA,SAASN,OAAOG,GAAW;IACzB,IAAID,oBAAoB,CAACC,IAAI,EAAE;QAC7BD,oBAAoB,CAACC,IAAI;QACzB,OAAOD,oBAAoB,CAACC,IAAI;IAClC;AACF"}