"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const stripe_1 = require("stripe");
const axios_1 = require("axios");
const client_1 = require("@prisma/client");
let PaymentsService = class PaymentsService {
    configService;
    prisma;
    stripe;
    constructor(configService, prisma) {
        this.configService = configService;
        this.prisma = prisma;
        this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY') || '', {
            apiVersion: '2025-02-24.acacia',
        });
    }
    async processPayment(createPaymentDto, userId) {
        const { orderId, amount, paymentMethod, phoneNumber, currency = 'XOF' } = createPaymentDto;
        const order = await this.prisma.order.findUnique({
            where: { id: orderId },
        });
        if (!order) {
            throw new common_1.BadRequestException('Commande non trouvée');
        }
        let result;
        switch (paymentMethod) {
            case client_1.PaymentMethod.STRIPE:
                result = await this.processStripePayment(amount, currency);
                break;
            case client_1.PaymentMethod.ORANGE_MONEY:
                result = await this.processOrangeMoneyPayment(amount, phoneNumber || '');
                break;
            case client_1.PaymentMethod.MTN_MONEY:
                result = await this.processMTNMoneyPayment(amount, phoneNumber || '');
                break;
            case client_1.PaymentMethod.WAVE:
                result = await this.processWavePayment(amount, phoneNumber || '');
                break;
            case client_1.PaymentMethod.CASH:
                result = await this.processCashPayment();
                break;
            default:
                throw new common_1.BadRequestException('Méthode de paiement non supportée');
        }
        await this.prisma.transaction.create({
            data: {
                userId,
                orderId,
                amount,
                type: client_1.TransactionType.ORDER_PAYMENT,
                paymentMethod,
                status: result.success ? 'COMPLETED' : 'FAILED',
                providerId: result.providerId,
            },
        });
        return result;
    }
    async processStripePayment(amount, currency) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: Math.round(amount * 100),
                currency: currency.toLowerCase(),
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            return {
                success: true,
                transactionId: paymentIntent.id,
                providerId: paymentIntent.id,
            };
        }
        catch (error) {
            return {
                success: false,
                transactionId: '',
                message: error.message,
            };
        }
    }
    async processOrangeMoneyPayment(amount, phoneNumber) {
        try {
            const apiUrl = this.configService.get('ORANGE_MONEY_API_URL');
            const clientId = this.configService.get('ORANGE_MONEY_CLIENT_ID');
            const clientSecret = this.configService.get('ORANGE_MONEY_CLIENT_SECRET');
            const merchantKey = this.configService.get('ORANGE_MONEY_MERCHANT_KEY');
            const tokenResponse = await axios_1.default.post(`${apiUrl}/oauth/v3/token`, {
                grant_type: 'client_credentials',
            }, {
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            });
            const accessToken = tokenResponse.data.access_token;
            const paymentResponse = await axios_1.default.post(`${apiUrl}/webpayment/v1/transactioninit`, {
                merchant_key: merchantKey,
                currency: 'XOF',
                order_id: `ORDER_${Date.now()}`,
                amount: amount,
                return_url: `${this.configService.get('API_URL')}/payments/orange/callback`,
                cancel_url: `${this.configService.get('API_URL')}/payments/orange/cancel`,
                notif_url: `${this.configService.get('API_URL')}/payments/orange/notify`,
                lang: 'fr',
                reference: `REF_${Date.now()}`,
            }, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
            });
            return {
                success: true,
                transactionId: paymentResponse.data.pay_token,
                providerId: paymentResponse.data.pay_token,
            };
        }
        catch (error) {
            return {
                success: false,
                transactionId: '',
                message: `Erreur Orange Money: ${error.message}`,
            };
        }
    }
    async processMTNMoneyPayment(amount, phoneNumber) {
        try {
            const apiUrl = this.configService.get('MTN_MOMO_API_URL');
            const subscriptionKey = this.configService.get('MTN_MOMO_SUBSCRIPTION_KEY');
            const apiUser = this.configService.get('MTN_MOMO_API_USER');
            const apiKey = this.configService.get('MTN_MOMO_API_KEY');
            const transactionId = `mtn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const paymentResponse = await axios_1.default.post(`${apiUrl}/collection/v1_0/requesttopay`, {
                amount: amount.toString(),
                currency: 'EUR',
                externalId: transactionId,
                payer: {
                    partyIdType: 'MSISDN',
                    partyId: phoneNumber,
                },
                payerMessage: 'Paiement Wali Livraison',
                payeeNote: 'Commande Wali Livraison',
            }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'X-Reference-Id': transactionId,
                    'X-Target-Environment': 'sandbox',
                    'Ocp-Apim-Subscription-Key': subscriptionKey,
                    'Content-Type': 'application/json',
                },
            });
            return {
                success: true,
                transactionId: transactionId,
                providerId: transactionId,
            };
        }
        catch (error) {
            return {
                success: false,
                transactionId: '',
                message: `Erreur MTN Money: ${error.message}`,
            };
        }
    }
    async processWavePayment(amount, phoneNumber) {
        try {
            const apiUrl = this.configService.get('WAVE_API_URL');
            const apiKey = this.configService.get('WAVE_API_KEY');
            const secretKey = this.configService.get('WAVE_SECRET_KEY');
            const transactionId = `wave_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const paymentResponse = await axios_1.default.post(`${apiUrl}/checkout/sessions`, {
                amount: amount,
                currency: 'XOF',
                error_url: `${this.configService.get('API_URL')}/payments/wave/error`,
                success_url: `${this.configService.get('API_URL')}/payments/wave/success`,
                client_reference: transactionId,
            }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                },
            });
            return {
                success: true,
                transactionId: transactionId,
                providerId: paymentResponse.data.id,
            };
        }
        catch (error) {
            return {
                success: false,
                transactionId: '',
                message: `Erreur Wave: ${error.message}`,
            };
        }
    }
    async processCashPayment() {
        return {
            success: true,
            transactionId: `cash_${Date.now()}`,
            message: 'Paiement en espèces - En attente de confirmation',
        };
    }
    async getPaymentStatus(transactionId, paymentMethod) {
        const transaction = await this.prisma.transaction.findFirst({
            where: { providerId: transactionId },
        });
        if (!transaction) {
            throw new common_1.BadRequestException('Transaction non trouvée');
        }
        return {
            transactionId,
            status: transaction.status,
            amount: transaction.amount,
            paymentMethod: transaction.paymentMethod,
            createdAt: transaction.createdAt,
        };
    }
};
exports.PaymentsService = PaymentsService;
exports.PaymentsService = PaymentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], PaymentsService);
//# sourceMappingURL=payments.service.js.map