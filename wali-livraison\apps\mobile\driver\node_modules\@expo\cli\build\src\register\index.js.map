{"version": 3, "sources": ["../../../src/register/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { Command } from '../../bin/cli';\nimport { assertArgs, printHelp } from '../utils/args';\nimport { logCmdError } from '../utils/errors';\n\nexport const expoRegister: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': <PERSON><PERSON><PERSON>,\n      // Aliases\n      '-h': '--help',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Sign up for a new Expo account`,\n      `npx expo register`,\n      // Options\n      `-h, --help    Usage info`\n    );\n  }\n\n  const { registerAsync } = await import('./registerAsync.js');\n  return registerAsync().catch(logCmdError);\n};\n"], "names": ["expoRegister", "argv", "args", "assertArgs", "Boolean", "printHelp", "registerAsync", "catch", "logCmdError"], "mappings": ";;;;;+BAKaA;;;eAAAA;;;sBAHyB;wBACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,eAAwB,OAAOC;IAC1C,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,UAAU;QACV,MAAM;IACR,GACAH;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,8BAA8B,CAAC,EAChC,CAAC,iBAAiB,CAAC,EACnB,UAAU;QACV,CAAC,wBAAwB,CAAC;IAE9B;IAEA,MAAM,EAAEC,aAAa,EAAE,GAAG,MAAM,mEAAA,QAAO;IACvC,OAAOA,gBAAgBC,KAAK,CAACC,mBAAW;AAC1C"}