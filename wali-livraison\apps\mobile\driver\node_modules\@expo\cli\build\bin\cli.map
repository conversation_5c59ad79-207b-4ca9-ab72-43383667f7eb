{"version": 3, "sources": ["../../bin/cli.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport Debug from 'debug';\nimport { boolish } from 'getenv';\n\n// Setup before requiring `debug`.\nif (boolish('EXPO_DEBUG', false)) {\n  Debug.enable('expo:*');\n} else if (Debug.enabled('expo:')) {\n  process.env.EXPO_DEBUG = '1';\n}\n\nconst defaultCmd = 'start';\n\nexport type Command = (argv?: string[]) => void;\n\nconst commands: { [command: string]: () => Promise<Command> } = {\n  // Add a new command here\n  // NOTE(EvanBacon): Ensure every bundler-related command sets `NODE_ENV` as expected for the command.\n  run: () => import('../src/run/index.js').then((i) => i.expoRun),\n  'run:ios': () => import('../src/run/ios/index.js').then((i) => i.expoRunIos),\n  'run:android': () => import('../src/run/android/index.js').then((i) => i.expoRunAndroid),\n  start: () => import('../src/start/index.js').then((i) => i.expoStart),\n  prebuild: () => import('../src/prebuild/index.js').then((i) => i.expoPrebuild),\n  config: () => import('../src/config/index.js').then((i) => i.expoConfig),\n  export: () => import('../src/export/index.js').then((i) => i.expoExport),\n  'export:web': () => import('../src/export/web/index.js').then((i) => i.expoExportWeb),\n  'export:embed': () => import('../src/export/embed/index.js').then((i) => i.expoExportEmbed),\n\n  serve: () => import('../src/serve/index.js').then((i) => i.expoServe),\n\n  // Auxiliary commands\n  install: () => import('../src/install/index.js').then((i) => i.expoInstall),\n  add: () => import('../src/install/index.js').then((i) => i.expoInstall),\n  customize: () => import('../src/customize/index.js').then((i) => i.expoCustomize),\n  lint: () => import('../src/lint/index.js').then((i) => i.expoLint),\n\n  // Auth\n  login: () => import('../src/login/index.js').then((i) => i.expoLogin),\n  logout: () => import('../src/logout/index.js').then((i) => i.expoLogout),\n  register: () => import('../src/register/index.js').then((i) => i.expoRegister),\n  whoami: () => import('../src/whoami/index.js').then((i) => i.expoWhoami),\n};\n\nconst args = arg(\n  {\n    // Types\n    '--version': Boolean,\n    '--help': Boolean,\n    // NOTE(EvanBacon): This is here to silence warnings from processes that\n    // expect the global expo-cli.\n    '--non-interactive': Boolean,\n\n    // Aliases\n    '-v': '--version',\n    '-h': '--help',\n  },\n  {\n    permissive: true,\n  }\n);\n\nif (args['--version']) {\n  // Version is added in the build script.\n  console.log(process.env.__EXPO_VERSION);\n  process.exit(0);\n}\n\nif (args['--non-interactive']) {\n  console.warn(chalk.yellow`  {bold --non-interactive} is not supported, use {bold $CI=1} instead`);\n}\n\n// Check if we are running `npx expo <subcommand>` or `npx expo`\nconst isSubcommand = Boolean(commands[args._[0]]);\n\n// Handle `--help` flag\nif (!isSubcommand && args['--help']) {\n  const {\n    login,\n    logout,\n    whoami,\n    register,\n    start,\n    install,\n    add,\n    export: _export,\n    config,\n    customize,\n    prebuild,\n    'run:ios': runIos,\n    'run:android': runAndroid,\n    // NOTE(EvanBacon): Don't document this command as it's a temporary\n    // workaround until we can use `expo export` for all production bundling.\n    // https://github.com/expo/expo/pull/21396/files#r1121025873\n    'export:embed': exportEmbed_unused,\n    // The export:web command is deprecated. Hide it from the help prompt.\n    'export:web': exportWeb_unused,\n    // Other ignored commands, these are intentially not listed in the `--help` output\n    run: _run,\n    // NOTE(cedric): Still pending the migration to ESLint's flat config\n    lint: _lint,\n    serve,\n    // All other commands\n    ...others\n  } = commands;\n\n  console.log(chalk`\n  {bold Usage}\n    {dim $} npx expo <command>\n\n  {bold Commands}\n    ${Object.keys({ start, export: _export, ...others }).join(', ')}\n    ${Object.keys({ 'run:ios': runIos, 'run:android': runAndroid, prebuild }).join(', ')}\n    ${Object.keys({ install, customize, config, serve }).join(', ')}\n    {dim ${Object.keys({ login, logout, whoami, register }).join(', ')}}\n\n  {bold Options}\n    --version, -v   Version number\n    --help, -h      Usage info\n\n  For more info run a command with the {bold --help} flag\n    {dim $} npx expo start --help\n`);\n\n  process.exit(0);\n}\n\n// NOTE(EvanBacon): Squat some directory names to help with migration,\n// users can still use folders named \"send\" or \"eject\" by using the fully qualified `npx expo start ./send`.\nif (!isSubcommand) {\n  const migrationMap: Record<string, string> = {\n    init: 'npx create-expo-app',\n    eject: 'npx expo prebuild',\n    web: 'npx expo start --web',\n    'start:web': 'npx expo start --web',\n    'build:ios': 'eas build -p ios',\n    'build:android': 'eas build -p android',\n    'client:install:ios': 'npx expo start --ios',\n    'client:install:android': 'npx expo start --android',\n    doctor: 'npx expo-doctor',\n    upgrade: 'https://docs.expo.dev/workflow/upgrading-expo-sdk-walkthrough/',\n    'customize:web': 'npx expo customize',\n\n    publish: 'eas update',\n    'publish:set': 'eas update',\n    'publish:rollback': 'eas update',\n    'publish:history': 'eas update',\n    'publish:details': 'eas update',\n\n    'build:web': 'npx expo export:web',\n\n    'credentials:manager': `eas credentials`,\n    'fetch:ios:certs': `eas credentials`,\n    'fetch:android:keystore': `eas credentials`,\n    'fetch:android:hashes': `eas credentials`,\n    'fetch:android:upload-cert': `eas credentials`,\n    'push:android:upload': `eas credentials`,\n    'push:android:show': `eas credentials`,\n    'push:android:clear': `eas credentials`,\n    url: `eas build:list`,\n    'url:ipa': `eas build:list`,\n    'url:apk': `eas build:list`,\n    webhooks: `eas webhook`,\n    'webhooks:add': `eas webhook:create`,\n    'webhooks:remove': `eas webhook:delete`,\n    'webhooks:update': `eas webhook:update`,\n\n    'build:status': `eas build:list`,\n    'upload:android': `eas submit -p android`,\n    'upload:ios': `eas submit -p ios`,\n  };\n\n  // TODO: Log telemetry about invalid command used.\n  const subcommand = args._[0];\n  if (subcommand in migrationMap) {\n    const replacement = migrationMap[subcommand];\n    console.log();\n    const instruction = subcommand === 'upgrade' ? 'follow this guide' : 'use';\n    console.log(\n      chalk.yellow`  {gray $} {bold expo ${subcommand}} is not supported in the local CLI, please ${instruction} {bold ${replacement}} instead`\n    );\n    console.log();\n    process.exit(1);\n  }\n  const deprecated = ['send', 'client:ios'];\n  if (deprecated.includes(subcommand)) {\n    console.log();\n    console.log(chalk.yellow`  {gray $} {bold expo ${subcommand}} is deprecated`);\n    console.log();\n    process.exit(1);\n  }\n}\n\nconst command = isSubcommand ? args._[0] : defaultCmd;\nconst commandArgs = isSubcommand ? args._.slice(1) : args._;\n\n// Push the help flag to the subcommand args.\nif (args['--help']) {\n  commandArgs.push('--help');\n}\n\n// Install exit hooks\nprocess.on('SIGINT', () => process.exit(0));\nprocess.on('SIGTERM', () => process.exit(0));\n\ncommands[command]().then((exec) => {\n  exec(commandArgs);\n\n  // NOTE(EvanBacon): Track some basic telemetry events indicating the command\n  // that was run. This can be disabled with the $EXPO_NO_TELEMETRY environment variable.\n  // We do this to determine how well deprecations are going before removing a command.\n  if (!boolish('EXPO_NO_TELEMETRY', false)) {\n    const { recordCommand } =\n      require('../src/utils/telemetry') as typeof import('../src/utils/telemetry');\n    recordCommand(command);\n  }\n});\n"], "names": ["boolish", "Debug", "enable", "enabled", "process", "env", "EXPO_DEBUG", "defaultCmd", "commands", "run", "then", "i", "expoRun", "expoRunIos", "expoRunAndroid", "start", "expoStart", "prebuild", "expoPrebuild", "config", "expoConfig", "export", "expoExport", "expoExportWeb", "expoExportEmbed", "serve", "expoServe", "install", "expoInstall", "add", "customize", "expoCustomize", "lint", "expoLint", "login", "expoLogin", "logout", "expoLogout", "register", "expoRegister", "whoami", "expoWhoami", "args", "arg", "Boolean", "permissive", "console", "log", "__EXPO_VERSION", "exit", "warn", "chalk", "yellow", "isSubcommand", "_", "_export", "runIos", "runAndroid", "exportEmbed_unused", "exportWeb_unused", "_run", "_lint", "others", "Object", "keys", "join", "migrationMap", "init", "eject", "web", "doctor", "upgrade", "publish", "url", "webhooks", "subcommand", "replacement", "instruction", "deprecated", "includes", "command", "commandArgs", "slice", "push", "on", "exec", "recordCommand", "require"], "mappings": ";;;;;;gEACgB;;;;;;;gEACE;;;;;;;gEACA;;;;;;;yBACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,kCAAkC;AAClC,IAAIA,IAAAA,iBAAO,EAAC,cAAc,QAAQ;IAChCC,gBAAK,CAACC,MAAM,CAAC;AACf,OAAO,IAAID,gBAAK,CAACE,OAAO,CAAC,UAAU;IACjCC,QAAQC,GAAG,CAACC,UAAU,GAAG;AAC3B;AAEA,MAAMC,aAAa;AAInB,MAAMC,WAA0D;IAC9D,yBAAyB;IACzB,qGAAqG;IACrGC,KAAK,IAAM,mEAAA,QAAO,yBAAuBC,IAAI,CAAC,CAACC,IAAMA,EAAEC,OAAO;IAC9D,WAAW,IAAM,mEAAA,QAAO,6BAA2BF,IAAI,CAAC,CAACC,IAAMA,EAAEE,UAAU;IAC3E,eAAe,IAAM,mEAAA,QAAO,iCAA+BH,IAAI,CAAC,CAACC,IAAMA,EAAEG,cAAc;IACvFC,OAAO,IAAM,mEAAA,QAAO,2BAAyBL,IAAI,CAAC,CAACC,IAAMA,EAAEK,SAAS;IACpEC,UAAU,IAAM,mEAAA,QAAO,8BAA4BP,IAAI,CAAC,CAACC,IAAMA,EAAEO,YAAY;IAC7EC,QAAQ,IAAM,mEAAA,QAAO,4BAA0BT,IAAI,CAAC,CAACC,IAAMA,EAAES,UAAU;IACvEC,QAAQ,IAAM,mEAAA,QAAO,4BAA0BX,IAAI,CAAC,CAACC,IAAMA,EAAEW,UAAU;IACvE,cAAc,IAAM,mEAAA,QAAO,gCAA8BZ,IAAI,CAAC,CAACC,IAAMA,EAAEY,aAAa;IACpF,gBAAgB,IAAM,mEAAA,QAAO,kCAAgCb,IAAI,CAAC,CAACC,IAAMA,EAAEa,eAAe;IAE1FC,OAAO,IAAM,mEAAA,QAAO,2BAAyBf,IAAI,CAAC,CAACC,IAAMA,EAAEe,SAAS;IAEpE,qBAAqB;IACrBC,SAAS,IAAM,mEAAA,QAAO,6BAA2BjB,IAAI,CAAC,CAACC,IAAMA,EAAEiB,WAAW;IAC1EC,KAAK,IAAM,mEAAA,QAAO,6BAA2BnB,IAAI,CAAC,CAACC,IAAMA,EAAEiB,WAAW;IACtEE,WAAW,IAAM,mEAAA,QAAO,+BAA6BpB,IAAI,CAAC,CAACC,IAAMA,EAAEoB,aAAa;IAChFC,MAAM,IAAM,mEAAA,QAAO,0BAAwBtB,IAAI,CAAC,CAACC,IAAMA,EAAEsB,QAAQ;IAEjE,OAAO;IACPC,OAAO,IAAM,mEAAA,QAAO,2BAAyBxB,IAAI,CAAC,CAACC,IAAMA,EAAEwB,SAAS;IACpEC,QAAQ,IAAM,mEAAA,QAAO,4BAA0B1B,IAAI,CAAC,CAACC,IAAMA,EAAE0B,UAAU;IACvEC,UAAU,IAAM,mEAAA,QAAO,8BAA4B5B,IAAI,CAAC,CAACC,IAAMA,EAAE4B,YAAY;IAC7EC,QAAQ,IAAM,mEAAA,QAAO,4BAA0B9B,IAAI,CAAC,CAACC,IAAMA,EAAE8B,UAAU;AACzE;AAEA,MAAMC,OAAOC,IAAAA,cAAG,EACd;IACE,QAAQ;IACR,aAAaC;IACb,UAAUA;IACV,wEAAwE;IACxE,8BAA8B;IAC9B,qBAAqBA;IAErB,UAAU;IACV,MAAM;IACN,MAAM;AACR,GACA;IACEC,YAAY;AACd;AAGF,IAAIH,IAAI,CAAC,YAAY,EAAE;IACrB,wCAAwC;IACxCI,QAAQC,GAAG,CAAC3C,QAAQC,GAAG,CAAC2C,cAAc;IACtC5C,QAAQ6C,IAAI,CAAC;AACf;AAEA,IAAIP,IAAI,CAAC,oBAAoB,EAAE;IAC7BI,QAAQI,IAAI,CAACC,gBAAK,CAACC,MAAM,CAAC,qEAAqE,CAAC;AAClG;AAEA,gEAAgE;AAChE,MAAMC,eAAeT,QAAQpC,QAAQ,CAACkC,KAAKY,CAAC,CAAC,EAAE,CAAC;AAEhD,uBAAuB;AACvB,IAAI,CAACD,gBAAgBX,IAAI,CAAC,SAAS,EAAE;IACnC,MAAM,EACJR,KAAK,EACLE,MAAM,EACNI,MAAM,EACNF,QAAQ,EACRvB,KAAK,EACLY,OAAO,EACPE,GAAG,EACHR,QAAQkC,OAAO,EACfpC,MAAM,EACNW,SAAS,EACTb,QAAQ,EACR,WAAWuC,MAAM,EACjB,eAAeC,UAAU,EACzB,mEAAmE;IACnE,yEAAyE;IACzE,4DAA4D;IAC5D,gBAAgBC,kBAAkB,EAClC,sEAAsE;IACtE,cAAcC,gBAAgB,EAC9B,kFAAkF;IAClFlD,KAAKmD,IAAI,EACT,oEAAoE;IACpE5B,MAAM6B,KAAK,EACXpC,KAAK,EACL,qBAAqB;IACrB,GAAGqC,QACJ,GAAGtD;IAEJsC,QAAQC,GAAG,CAACI,IAAAA,gBAAK,CAAA,CAAC;;;;;IAKhB,EAAEY,OAAOC,IAAI,CAAC;QAAEjD;QAAOM,QAAQkC;QAAS,GAAGO,MAAM;IAAC,GAAGG,IAAI,CAAC,MAAM;IAChE,EAAEF,OAAOC,IAAI,CAAC;QAAE,WAAWR;QAAQ,eAAeC;QAAYxC;IAAS,GAAGgD,IAAI,CAAC,MAAM;IACrF,EAAEF,OAAOC,IAAI,CAAC;QAAErC;QAASG;QAAWX;QAAQM;IAAM,GAAGwC,IAAI,CAAC,MAAM;SAC3D,EAAEF,OAAOC,IAAI,CAAC;QAAE9B;QAAOE;QAAQI;QAAQF;IAAS,GAAG2B,IAAI,CAAC,MAAM;;;;;;;;AAQvE,CAAC;IAEC7D,QAAQ6C,IAAI,CAAC;AACf;AAEA,sEAAsE;AACtE,4GAA4G;AAC5G,IAAI,CAACI,cAAc;IACjB,MAAMa,eAAuC;QAC3CC,MAAM;QACNC,OAAO;QACPC,KAAK;QACL,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,sBAAsB;QACtB,0BAA0B;QAC1BC,QAAQ;QACRC,SAAS;QACT,iBAAiB;QAEjBC,SAAS;QACT,eAAe;QACf,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QAEnB,aAAa;QAEb,uBAAuB,CAAC,eAAe,CAAC;QACxC,mBAAmB,CAAC,eAAe,CAAC;QACpC,0BAA0B,CAAC,eAAe,CAAC;QAC3C,wBAAwB,CAAC,eAAe,CAAC;QACzC,6BAA6B,CAAC,eAAe,CAAC;QAC9C,uBAAuB,CAAC,eAAe,CAAC;QACxC,qBAAqB,CAAC,eAAe,CAAC;QACtC,sBAAsB,CAAC,eAAe,CAAC;QACvCC,KAAK,CAAC,cAAc,CAAC;QACrB,WAAW,CAAC,cAAc,CAAC;QAC3B,WAAW,CAAC,cAAc,CAAC;QAC3BC,UAAU,CAAC,WAAW,CAAC;QACvB,gBAAgB,CAAC,kBAAkB,CAAC;QACpC,mBAAmB,CAAC,kBAAkB,CAAC;QACvC,mBAAmB,CAAC,kBAAkB,CAAC;QAEvC,gBAAgB,CAAC,cAAc,CAAC;QAChC,kBAAkB,CAAC,qBAAqB,CAAC;QACzC,cAAc,CAAC,iBAAiB,CAAC;IACnC;IAEA,kDAAkD;IAClD,MAAMC,aAAajC,KAAKY,CAAC,CAAC,EAAE;IAC5B,IAAIqB,cAAcT,cAAc;QAC9B,MAAMU,cAAcV,YAAY,CAACS,WAAW;QAC5C7B,QAAQC,GAAG;QACX,MAAM8B,cAAcF,eAAe,YAAY,sBAAsB;QACrE7B,QAAQC,GAAG,CACTI,gBAAK,CAACC,MAAM,CAAC,sBAAsB,EAAEuB,WAAW,4CAA4C,EAAEE,YAAY,OAAO,EAAED,YAAY,SAAS,CAAC;QAE3I9B,QAAQC,GAAG;QACX3C,QAAQ6C,IAAI,CAAC;IACf;IACA,MAAM6B,aAAa;QAAC;QAAQ;KAAa;IACzC,IAAIA,WAAWC,QAAQ,CAACJ,aAAa;QACnC7B,QAAQC,GAAG;QACXD,QAAQC,GAAG,CAACI,gBAAK,CAACC,MAAM,CAAC,sBAAsB,EAAEuB,WAAW,eAAe,CAAC;QAC5E7B,QAAQC,GAAG;QACX3C,QAAQ6C,IAAI,CAAC;IACf;AACF;AAEA,MAAM+B,UAAU3B,eAAeX,KAAKY,CAAC,CAAC,EAAE,GAAG/C;AAC3C,MAAM0E,cAAc5B,eAAeX,KAAKY,CAAC,CAAC4B,KAAK,CAAC,KAAKxC,KAAKY,CAAC;AAE3D,6CAA6C;AAC7C,IAAIZ,IAAI,CAAC,SAAS,EAAE;IAClBuC,YAAYE,IAAI,CAAC;AACnB;AAEA,qBAAqB;AACrB/E,QAAQgF,EAAE,CAAC,UAAU,IAAMhF,QAAQ6C,IAAI,CAAC;AACxC7C,QAAQgF,EAAE,CAAC,WAAW,IAAMhF,QAAQ6C,IAAI,CAAC;AAEzCzC,QAAQ,CAACwE,QAAQ,GAAGtE,IAAI,CAAC,CAAC2E;IACxBA,KAAKJ;IAEL,4EAA4E;IAC5E,uFAAuF;IACvF,qFAAqF;IACrF,IAAI,CAACjF,IAAAA,iBAAO,EAAC,qBAAqB,QAAQ;QACxC,MAAM,EAAEsF,aAAa,EAAE,GACrBC,QAAQ;QACVD,cAAcN;IAChB;AACF"}