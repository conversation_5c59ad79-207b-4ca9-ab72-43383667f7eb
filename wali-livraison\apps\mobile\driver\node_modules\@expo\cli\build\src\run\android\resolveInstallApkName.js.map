{"version": 3, "sources": ["../../../../src/run/android/resolveInstallApkName.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { GradleProps } from './resolveGradlePropsAsync';\nimport { Device, DeviceABI, getDeviceABIsAsync } from '../../start/platforms/android/adb';\n\nconst debug = require('debug')('expo:run:android:resolveInstallApkName') as typeof console.log;\n\nexport async function resolveInstallApkNameAsync(\n  device: Pick<Device, 'name' | 'pid'>,\n  { appName, buildType, flavors, apkVariantDirectory }: GradleProps\n) {\n  const availableCPUs = await getDeviceABIsAsync(device);\n  availableCPUs.push(DeviceABI.universal);\n\n  debug('Supported ABIs: ' + availableCPUs.join(', '));\n  debug('Searching for APK: ' + apkVariantDirectory);\n\n  // Check for cpu specific builds first\n  for (const availableCPU of availableCPUs) {\n    const apkName = getApkFileName(appName, buildType, flavors, availableCPU);\n    const apkPath = path.join(apkVariantDirectory, apkName);\n    debug('Checking for APK at:', apkPath);\n    if (fs.existsSync(apkPath)) {\n      return apkName;\n    }\n  }\n\n  // Otherwise use the default apk named after the variant: app-debug.apk\n  const apkName = getApkFileName(appName, buildType, flavors);\n  const apkPath = path.join(apkVariantDirectory, apkName);\n  debug('Checking for fallback APK at:', apkPath);\n  if (fs.existsSync(apkPath)) {\n    return apkName;\n  }\n\n  return null;\n}\n\nfunction getApkFileName(\n  appName: string,\n  buildType: string,\n  flavors?: string[] | null,\n  cpuArch?: string | null\n) {\n  let apkName = `${appName}-`;\n  if (flavors) {\n    apkName += flavors.reduce((rest, flavor) => `${rest}${flavor}-`, '');\n  }\n  if (cpuArch) {\n    apkName += `${cpuArch}-`;\n  }\n  apkName += `${buildType}.apk`;\n\n  return apkName;\n}\n"], "names": ["resolveInstallApkNameAsync", "debug", "require", "device", "appName", "buildType", "flavors", "apkVariantDirectory", "availableCPUs", "getDeviceABIsAsync", "push", "DeviceABI", "universal", "join", "availableCPU", "apkName", "getApkFileName", "apkPath", "path", "fs", "existsSync", "cpuArch", "reduce", "rest", "flavor"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;gEARP;;;;;;;gEACE;;;;;;qBAGqC;;;;;;AAEtD,MAAMC,QAAQC,QAAQ,SAAS;AAExB,eAAeF,2BACpBG,MAAoC,EACpC,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,mBAAmB,EAAe;IAEjE,MAAMC,gBAAgB,MAAMC,IAAAA,uBAAkB,EAACN;IAC/CK,cAAcE,IAAI,CAACC,cAAS,CAACC,SAAS;IAEtCX,MAAM,qBAAqBO,cAAcK,IAAI,CAAC;IAC9CZ,MAAM,wBAAwBM;IAE9B,sCAAsC;IACtC,KAAK,MAAMO,gBAAgBN,cAAe;QACxC,MAAMO,UAAUC,eAAeZ,SAASC,WAAWC,SAASQ;QAC5D,MAAMG,UAAUC,eAAI,CAACL,IAAI,CAACN,qBAAqBQ;QAC/Cd,MAAM,wBAAwBgB;QAC9B,IAAIE,aAAE,CAACC,UAAU,CAACH,UAAU;YAC1B,OAAOF;QACT;IACF;IAEA,uEAAuE;IACvE,MAAMA,UAAUC,eAAeZ,SAASC,WAAWC;IACnD,MAAMW,UAAUC,eAAI,CAACL,IAAI,CAACN,qBAAqBQ;IAC/Cd,MAAM,iCAAiCgB;IACvC,IAAIE,aAAE,CAACC,UAAU,CAACH,UAAU;QAC1B,OAAOF;IACT;IAEA,OAAO;AACT;AAEA,SAASC,eACPZ,OAAe,EACfC,SAAiB,EACjBC,OAAyB,EACzBe,OAAuB;IAEvB,IAAIN,UAAU,GAAGX,QAAQ,CAAC,CAAC;IAC3B,IAAIE,SAAS;QACXS,WAAWT,QAAQgB,MAAM,CAAC,CAACC,MAAMC,SAAW,GAAGD,OAAOC,OAAO,CAAC,CAAC,EAAE;IACnE;IACA,IAAIH,SAAS;QACXN,WAAW,GAAGM,QAAQ,CAAC,CAAC;IAC1B;IACAN,WAAW,GAAGV,UAAU,IAAI,CAAC;IAE7B,OAAOU;AACT"}