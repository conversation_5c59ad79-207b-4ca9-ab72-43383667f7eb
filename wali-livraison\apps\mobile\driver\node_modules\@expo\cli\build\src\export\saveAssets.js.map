{"version": 3, "sources": ["../../../src/export/saveAssets.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport type { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport Metro from 'metro';\nimport path from 'path';\nimport prettyBytes from 'pretty-bytes';\n\nimport { Log } from '../log';\nimport { env } from '../utils/env';\n\nconst BLT = '\\u203A';\n\nexport type BundleOptions = {\n  entryPoint: string;\n  platform: 'android' | 'ios' | 'web';\n  dev?: boolean;\n  minify?: boolean;\n  bytecode: boolean;\n  sourceMapUrl?: string;\n  sourcemaps?: boolean;\n};\n\nexport type BundleAssetWithFileHashes = Metro.AssetData & {\n  fileHashes: string[]; // added by the hashAssets asset plugin\n};\n\nexport type BundleOutput = {\n  artifacts: SerialAsset[];\n  assets: readonly BundleAssetWithFileHashes[];\n};\n\nexport type ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\nexport type ExportAssetDescriptor = {\n  contents: string | Buffer;\n  originFilename?: string;\n  /** An identifier for grouping together variations of the same asset. */\n  assetId?: string;\n  /** Expo Router route path for formatting the HTML output. */\n  routeId?: string;\n  /** Expo Router API route path for formatting the server function output. */\n  apiRouteId?: string;\n  /** Expo Router route path for formatting the RSC output. */\n  rscId?: string;\n  /** A key for grouping together output files by server- or client-side. */\n  targetDomain?: 'server' | 'client';\n};\n\nexport type ExportAssetMap = Map<string, ExportAssetDescriptor>;\n\nexport async function persistMetroFilesAsync(files: ExportAssetMap, outputDir: string) {\n  if (!files.size) {\n    return;\n  }\n  await fs.promises.mkdir(path.join(outputDir), { recursive: true });\n\n  // Test fixtures:\n  // Log.log(\n  //   JSON.stringify(\n  //     Object.fromEntries([...files.entries()].map(([k, v]) => [k, { ...v, contents: '' }]))\n  //   )\n  // );\n\n  const assetEntries: [string, ExportAssetDescriptor][] = [];\n  const apiRouteEntries: [string, ExportAssetDescriptor][] = [];\n  const routeEntries: [string, ExportAssetDescriptor][] = [];\n  const rscEntries: [string, ExportAssetDescriptor][] = [];\n  const remainingEntries: [string, ExportAssetDescriptor][] = [];\n\n  let hasServerOutput = false;\n  for (const asset of files.entries()) {\n    hasServerOutput = hasServerOutput || asset[1].targetDomain === 'server';\n    if (asset[1].assetId) assetEntries.push(asset);\n    else if (asset[1].routeId != null) routeEntries.push(asset);\n    else if (asset[1].apiRouteId != null) apiRouteEntries.push(asset);\n    else if (asset[1].rscId != null) rscEntries.push(asset);\n    else remainingEntries.push(asset);\n  }\n\n  const groups = groupBy(assetEntries, ([, { assetId }]) => assetId!);\n\n  const contentSize = (contents: string | Buffer) => {\n    const length =\n      typeof contents === 'string' ? Buffer.byteLength(contents, 'utf8') : contents.length;\n    return length;\n  };\n\n  const sizeStr = (contents: string | Buffer) => {\n    const length = contentSize(contents);\n    const size = chalk.gray`(${prettyBytes(length)})`;\n    return size;\n  };\n\n  // TODO: If any Expo Router is used, then use a new style which is more simple:\n  // `chalk.gray(/path/to/) + chalk.cyan('route')`\n  // | index.html (1.2kb)\n  // | /path\n  //   | other.html (1.2kb)\n\n  const isExpoRouter = routeEntries.length;\n\n  // Phase out printing all the assets as users can simply check the file system for more info.\n  const showAdditionalInfo = !isExpoRouter || env.EXPO_DEBUG;\n\n  const assetGroups = [...groups.entries()].sort((a, b) => a[0].localeCompare(b[0])) as [\n    string,\n    [string, ExportAssetDescriptor][],\n  ][];\n\n  if (showAdditionalInfo) {\n    if (assetGroups.length) {\n      const totalAssets = assetGroups.reduce((sum, [, assets]) => sum + assets.length, 0);\n\n      Log.log('');\n      Log.log(chalk.bold`${BLT} Assets (${totalAssets}):`);\n\n      for (const [assetId, assets] of assetGroups) {\n        const averageContentSize =\n          assets.reduce((sum, [, { contents }]) => sum + contentSize(contents), 0) / assets.length;\n        Log.log(\n          assetId,\n          chalk.gray(\n            `(${[\n              assets.length > 1 ? `${assets.length} variations` : '',\n              `${prettyBytes(averageContentSize)}`,\n            ]\n              .filter(Boolean)\n              .join(' | ')})`\n          )\n        );\n      }\n    }\n  }\n\n  const bundles: Map<string, [string, ExportAssetDescriptor][]> = new Map();\n  const other: [string, ExportAssetDescriptor][] = [];\n\n  remainingEntries.forEach(([filepath, asset]) => {\n    if (!filepath.match(/_expo\\/static\\//)) {\n      other.push([filepath, asset]);\n    } else {\n      const platform = filepath.match(/_expo\\/static\\/js\\/([^/]+)\\//)?.[1] ?? 'web';\n      if (!bundles.has(platform)) bundles.set(platform, []);\n\n      bundles.get(platform)!.push([filepath, asset]);\n    }\n  });\n\n  [...bundles.entries()].forEach(([platform, assets]) => {\n    Log.log('');\n    Log.log(chalk.bold`${BLT} ${platform} bundles (${assets.length}):`);\n\n    const allAssets = assets.sort((a, b) => a[0].localeCompare(b[0]));\n    while (allAssets.length) {\n      const [filePath, asset] = allAssets.shift()!;\n      Log.log(filePath, sizeStr(asset.contents));\n      if (filePath.match(/\\.(js|hbc)$/)) {\n        // Get source map\n        const sourceMapIndex = allAssets.findIndex(([fp]) => fp === filePath + '.map');\n        if (sourceMapIndex !== -1) {\n          const [sourceMapFilePath, sourceMapAsset] = allAssets.splice(sourceMapIndex, 1)[0];\n          Log.log(chalk.gray(sourceMapFilePath), sizeStr(sourceMapAsset.contents));\n        }\n      }\n    }\n  });\n\n  if (showAdditionalInfo && other.length) {\n    Log.log('');\n    Log.log(chalk.bold`${BLT} Files (${other.length}):`);\n\n    for (const [filePath, asset] of other.sort((a, b) => a[0].localeCompare(b[0]))) {\n      Log.log(filePath, sizeStr(asset.contents));\n    }\n  }\n\n  if (rscEntries.length) {\n    Log.log('');\n    Log.log(chalk.bold`${BLT} React Server Components (${rscEntries.length}):`);\n\n    for (const [filePath, assets] of rscEntries.sort((a, b) => a[0].length - b[0].length)) {\n      const id = assets.rscId!;\n      Log.log(\n        '/' + (id === '' ? chalk.gray(' (index)') : id),\n        sizeStr(assets.contents),\n        chalk.gray(filePath)\n      );\n    }\n  }\n\n  if (routeEntries.length) {\n    Log.log('');\n    Log.log(chalk.bold`${BLT} Static routes (${routeEntries.length}):`);\n\n    for (const [, assets] of routeEntries.sort((a, b) => a[0].length - b[0].length)) {\n      const id = assets.routeId!;\n      Log.log('/' + (id === '' ? chalk.gray(' (index)') : id), sizeStr(assets.contents));\n    }\n  }\n\n  if (apiRouteEntries.length) {\n    const apiRoutesWithoutSourcemaps = apiRouteEntries.filter(\n      (route) => !route[0].endsWith('.map')\n    );\n    Log.log('');\n    Log.log(chalk.bold`${BLT} API routes (${apiRoutesWithoutSourcemaps.length}):`);\n\n    for (const [apiRouteFilename, assets] of apiRoutesWithoutSourcemaps.sort(\n      (a, b) => a[0].length - b[0].length\n    )) {\n      const id = assets.apiRouteId!;\n      const hasSourceMap = apiRouteEntries.find(\n        ([filename, route]) =>\n          filename !== apiRouteFilename &&\n          route.apiRouteId === assets.apiRouteId &&\n          filename.endsWith('.map')\n      );\n      Log.log(\n        id === '' ? chalk.gray(' (index)') : id,\n        sizeStr(assets.contents),\n        hasSourceMap ? chalk.gray(`(source map ${sizeStr(hasSourceMap[1].contents)})`) : ''\n      );\n    }\n  }\n\n  // Decouple logging from writing for better performance.\n\n  await Promise.all(\n    [...files.entries()]\n      .sort(([a], [b]) => a.localeCompare(b))\n      .map(async ([file, { contents, targetDomain }]) => {\n        // NOTE: Only use `targetDomain` if we have at least one server asset\n        const domain = (hasServerOutput && targetDomain) || '';\n        const outputPath = path.join(outputDir, domain, file);\n        await fs.promises.mkdir(path.dirname(outputPath), { recursive: true });\n        await fs.promises.writeFile(outputPath, contents);\n      })\n  );\n\n  Log.log('');\n}\n\nfunction groupBy<T>(array: T[], key: (item: T) => string): Map<string, T[]> {\n  const map = new Map<string, T[]>();\n  array.forEach((item) => {\n    const group = key(item);\n    const list = map.get(group) ?? [];\n    list.push(item);\n    map.set(group, list);\n  });\n  return map;\n}\n\n// TODO: Move source map modification to the serializer\nexport function getFilesFromSerialAssets(\n  resources: SerialAsset[],\n  {\n    includeSourceMaps,\n    files = new Map(),\n    platform,\n    isServerHosted = platform === 'web',\n  }: {\n    includeSourceMaps: boolean;\n    files?: ExportAssetMap;\n    platform?: string;\n    isServerHosted?: boolean;\n  }\n) {\n  resources.forEach((resource) => {\n    if (resource.type === 'css-external') {\n      return;\n    }\n    files.set(resource.filename, {\n      contents: resource.source,\n      originFilename: resource.originFilename,\n      targetDomain: isServerHosted ? 'client' : undefined,\n    });\n  });\n\n  return files;\n}\n"], "names": ["getFilesFromSerialAssets", "persistMetroFilesAsync", "BLT", "files", "outputDir", "size", "fs", "promises", "mkdir", "path", "join", "recursive", "assetEntries", "apiRouteEntries", "routeEntries", "rscEntries", "remainingEntries", "hasServerOutput", "asset", "entries", "targetDomain", "assetId", "push", "routeId", "apiRouteId", "rscId", "groups", "groupBy", "contentSize", "contents", "length", "<PERSON><PERSON><PERSON>", "byteLength", "sizeStr", "chalk", "gray", "prettyBytes", "isExpoRouter", "showAdditionalInfo", "env", "EXPO_DEBUG", "assetGroups", "sort", "a", "b", "localeCompare", "totalAssets", "reduce", "sum", "assets", "Log", "log", "bold", "averageContentSize", "filter", "Boolean", "bundles", "Map", "other", "for<PERSON>ach", "filepath", "match", "platform", "has", "set", "get", "allAssets", "filePath", "shift", "sourceMapIndex", "findIndex", "fp", "sourceMapFilePath", "sourceMapAsset", "splice", "id", "apiRoutesWithoutSourcemaps", "route", "endsWith", "apiRouteFilename", "hasSourceMap", "find", "filename", "Promise", "all", "map", "file", "domain", "outputPath", "dirname", "writeFile", "array", "key", "item", "group", "list", "resources", "includeSourceMaps", "isServerHosted", "resource", "type", "source", "originFilename", "undefined"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAiQeA,wBAAwB;eAAxBA;;IA5MMC,sBAAsB;eAAtBA;;;;gEAnDJ;;;;;;;gEACH;;;;;;;gEAEE;;;;;;;gEACO;;;;;;qBAEJ;qBACA;;;;;;AAEpB,MAAMC,MAAM;AA0CL,eAAeD,uBAAuBE,KAAqB,EAAEC,SAAiB;IACnF,IAAI,CAACD,MAAME,IAAI,EAAE;QACf;IACF;IACA,MAAMC,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACC,IAAI,CAACN,YAAY;QAAEO,WAAW;IAAK;IAEhE,iBAAiB;IACjB,WAAW;IACX,oBAAoB;IACpB,4FAA4F;IAC5F,MAAM;IACN,KAAK;IAEL,MAAMC,eAAkD,EAAE;IAC1D,MAAMC,kBAAqD,EAAE;IAC7D,MAAMC,eAAkD,EAAE;IAC1D,MAAMC,aAAgD,EAAE;IACxD,MAAMC,mBAAsD,EAAE;IAE9D,IAAIC,kBAAkB;IACtB,KAAK,MAAMC,SAASf,MAAMgB,OAAO,GAAI;QACnCF,kBAAkBA,mBAAmBC,KAAK,CAAC,EAAE,CAACE,YAAY,KAAK;QAC/D,IAAIF,KAAK,CAAC,EAAE,CAACG,OAAO,EAAET,aAAaU,IAAI,CAACJ;aACnC,IAAIA,KAAK,CAAC,EAAE,CAACK,OAAO,IAAI,MAAMT,aAAaQ,IAAI,CAACJ;aAChD,IAAIA,KAAK,CAAC,EAAE,CAACM,UAAU,IAAI,MAAMX,gBAAgBS,IAAI,CAACJ;aACtD,IAAIA,KAAK,CAAC,EAAE,CAACO,KAAK,IAAI,MAAMV,WAAWO,IAAI,CAACJ;aAC5CF,iBAAiBM,IAAI,CAACJ;IAC7B;IAEA,MAAMQ,SAASC,QAAQf,cAAc,CAAC,GAAG,EAAES,OAAO,EAAE,CAAC,GAAKA;IAE1D,MAAMO,cAAc,CAACC;QACnB,MAAMC,SACJ,OAAOD,aAAa,WAAWE,OAAOC,UAAU,CAACH,UAAU,UAAUA,SAASC,MAAM;QACtF,OAAOA;IACT;IAEA,MAAMG,UAAU,CAACJ;QACf,MAAMC,SAASF,YAAYC;QAC3B,MAAMxB,OAAO6B,gBAAK,CAACC,IAAI,CAAC,CAAC,EAAEC,IAAAA,sBAAW,EAACN,QAAQ,CAAC,CAAC;QACjD,OAAOzB;IACT;IAEA,+EAA+E;IAC/E,gDAAgD;IAChD,uBAAuB;IACvB,UAAU;IACV,yBAAyB;IAEzB,MAAMgC,eAAevB,aAAagB,MAAM;IAExC,6FAA6F;IAC7F,MAAMQ,qBAAqB,CAACD,gBAAgBE,QAAG,CAACC,UAAU;IAE1D,MAAMC,cAAc;WAAIf,OAAOP,OAAO;KAAG,CAACuB,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACE,aAAa,CAACD,CAAC,CAAC,EAAE;IAKhF,IAAIN,oBAAoB;QACtB,IAAIG,YAAYX,MAAM,EAAE;YACtB,MAAMgB,cAAcL,YAAYM,MAAM,CAAC,CAACC,KAAK,GAAGC,OAAO,GAAKD,MAAMC,OAAOnB,MAAM,EAAE;YAEjFoB,QAAG,CAACC,GAAG,CAAC;YACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,SAAS,EAAE4C,YAAY,EAAE,CAAC;YAEnD,KAAK,MAAM,CAACzB,SAAS4B,OAAO,IAAIR,YAAa;gBAC3C,MAAMY,qBACJJ,OAAOF,MAAM,CAAC,CAACC,KAAK,GAAG,EAAEnB,QAAQ,EAAE,CAAC,GAAKmB,MAAMpB,YAAYC,WAAW,KAAKoB,OAAOnB,MAAM;gBAC1FoB,QAAG,CAACC,GAAG,CACL9B,SACAa,gBAAK,CAACC,IAAI,CACR,CAAC,CAAC,EAAE;oBACFc,OAAOnB,MAAM,GAAG,IAAI,GAAGmB,OAAOnB,MAAM,CAAC,WAAW,CAAC,GAAG;oBACpD,GAAGM,IAAAA,sBAAW,EAACiB,qBAAqB;iBACrC,CACEC,MAAM,CAACC,SACP7C,IAAI,CAAC,OAAO,CAAC,CAAC;YAGvB;QACF;IACF;IAEA,MAAM8C,UAA0D,IAAIC;IACpE,MAAMC,QAA2C,EAAE;IAEnD1C,iBAAiB2C,OAAO,CAAC,CAAC,CAACC,UAAU1C,MAAM;QACzC,IAAI,CAAC0C,SAASC,KAAK,CAAC,oBAAoB;YACtCH,MAAMpC,IAAI,CAAC;gBAACsC;gBAAU1C;aAAM;QAC9B,OAAO;gBACY0C;YAAjB,MAAME,WAAWF,EAAAA,kBAAAA,SAASC,KAAK,CAAC,oDAAfD,eAAgD,CAAC,EAAE,KAAI;YACxE,IAAI,CAACJ,QAAQO,GAAG,CAACD,WAAWN,QAAQQ,GAAG,CAACF,UAAU,EAAE;YAEpDN,QAAQS,GAAG,CAACH,UAAWxC,IAAI,CAAC;gBAACsC;gBAAU1C;aAAM;QAC/C;IACF;IAEA;WAAIsC,QAAQrC,OAAO;KAAG,CAACwC,OAAO,CAAC,CAAC,CAACG,UAAUb,OAAO;QAChDC,QAAG,CAACC,GAAG,CAAC;QACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,CAAC,EAAE4D,SAAS,UAAU,EAAEb,OAAOnB,MAAM,CAAC,EAAE,CAAC;QAElE,MAAMoC,YAAYjB,OAAOP,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACE,aAAa,CAACD,CAAC,CAAC,EAAE;QAC/D,MAAOsB,UAAUpC,MAAM,CAAE;YACvB,MAAM,CAACqC,UAAUjD,MAAM,GAAGgD,UAAUE,KAAK;YACzClB,QAAG,CAACC,GAAG,CAACgB,UAAUlC,QAAQf,MAAMW,QAAQ;YACxC,IAAIsC,SAASN,KAAK,CAAC,gBAAgB;gBACjC,iBAAiB;gBACjB,MAAMQ,iBAAiBH,UAAUI,SAAS,CAAC,CAAC,CAACC,GAAG,GAAKA,OAAOJ,WAAW;gBACvE,IAAIE,mBAAmB,CAAC,GAAG;oBACzB,MAAM,CAACG,mBAAmBC,eAAe,GAAGP,UAAUQ,MAAM,CAACL,gBAAgB,EAAE,CAAC,EAAE;oBAClFnB,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACC,IAAI,CAACqC,oBAAoBvC,QAAQwC,eAAe5C,QAAQ;gBACxE;YACF;QACF;IACF;IAEA,IAAIS,sBAAsBoB,MAAM5B,MAAM,EAAE;QACtCoB,QAAG,CAACC,GAAG,CAAC;QACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,QAAQ,EAAEwD,MAAM5B,MAAM,CAAC,EAAE,CAAC;QAEnD,KAAK,MAAM,CAACqC,UAAUjD,MAAM,IAAIwC,MAAMhB,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACE,aAAa,CAACD,CAAC,CAAC,EAAE,GAAI;YAC9EM,QAAG,CAACC,GAAG,CAACgB,UAAUlC,QAAQf,MAAMW,QAAQ;QAC1C;IACF;IAEA,IAAId,WAAWe,MAAM,EAAE;QACrBoB,QAAG,CAACC,GAAG,CAAC;QACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,0BAA0B,EAAEa,WAAWe,MAAM,CAAC,EAAE,CAAC;QAE1E,KAAK,MAAM,CAACqC,UAAUlB,OAAO,IAAIlC,WAAW2B,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACb,MAAM,GAAGc,CAAC,CAAC,EAAE,CAACd,MAAM,EAAG;YACrF,MAAM6C,KAAK1B,OAAOxB,KAAK;YACvByB,QAAG,CAACC,GAAG,CACL,MAAOwB,CAAAA,OAAO,KAAKzC,gBAAK,CAACC,IAAI,CAAC,cAAcwC,EAAC,GAC7C1C,QAAQgB,OAAOpB,QAAQ,GACvBK,gBAAK,CAACC,IAAI,CAACgC;QAEf;IACF;IAEA,IAAIrD,aAAagB,MAAM,EAAE;QACvBoB,QAAG,CAACC,GAAG,CAAC;QACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,gBAAgB,EAAEY,aAAagB,MAAM,CAAC,EAAE,CAAC;QAElE,KAAK,MAAM,GAAGmB,OAAO,IAAInC,aAAa4B,IAAI,CAAC,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACb,MAAM,GAAGc,CAAC,CAAC,EAAE,CAACd,MAAM,EAAG;YAC/E,MAAM6C,KAAK1B,OAAO1B,OAAO;YACzB2B,QAAG,CAACC,GAAG,CAAC,MAAOwB,CAAAA,OAAO,KAAKzC,gBAAK,CAACC,IAAI,CAAC,cAAcwC,EAAC,GAAI1C,QAAQgB,OAAOpB,QAAQ;QAClF;IACF;IAEA,IAAIhB,gBAAgBiB,MAAM,EAAE;QAC1B,MAAM8C,6BAA6B/D,gBAAgByC,MAAM,CACvD,CAACuB,QAAU,CAACA,KAAK,CAAC,EAAE,CAACC,QAAQ,CAAC;QAEhC5B,QAAG,CAACC,GAAG,CAAC;QACRD,QAAG,CAACC,GAAG,CAACjB,gBAAK,CAACkB,IAAI,CAAC,EAAElD,IAAI,aAAa,EAAE0E,2BAA2B9C,MAAM,CAAC,EAAE,CAAC;QAE7E,KAAK,MAAM,CAACiD,kBAAkB9B,OAAO,IAAI2B,2BAA2BlC,IAAI,CACtE,CAACC,GAAGC,IAAMD,CAAC,CAAC,EAAE,CAACb,MAAM,GAAGc,CAAC,CAAC,EAAE,CAACd,MAAM,EAClC;YACD,MAAM6C,KAAK1B,OAAOzB,UAAU;YAC5B,MAAMwD,eAAenE,gBAAgBoE,IAAI,CACvC,CAAC,CAACC,UAAUL,MAAM,GAChBK,aAAaH,oBACbF,MAAMrD,UAAU,KAAKyB,OAAOzB,UAAU,IACtC0D,SAASJ,QAAQ,CAAC;YAEtB5B,QAAG,CAACC,GAAG,CACLwB,OAAO,KAAKzC,gBAAK,CAACC,IAAI,CAAC,cAAcwC,IACrC1C,QAAQgB,OAAOpB,QAAQ,GACvBmD,eAAe9C,gBAAK,CAACC,IAAI,CAAC,CAAC,YAAY,EAAEF,QAAQ+C,YAAY,CAAC,EAAE,CAACnD,QAAQ,EAAE,CAAC,CAAC,IAAI;QAErF;IACF;IAEA,wDAAwD;IAExD,MAAMsD,QAAQC,GAAG,CACf;WAAIjF,MAAMgB,OAAO;KAAG,CACjBuB,IAAI,CAAC,CAAC,CAACC,EAAE,EAAE,CAACC,EAAE,GAAKD,EAAEE,aAAa,CAACD,IACnCyC,GAAG,CAAC,OAAO,CAACC,MAAM,EAAEzD,QAAQ,EAAET,YAAY,EAAE,CAAC;QAC5C,qEAAqE;QACrE,MAAMmE,SAAS,AAACtE,mBAAmBG,gBAAiB;QACpD,MAAMoE,aAAa/E,eAAI,CAACC,IAAI,CAACN,WAAWmF,QAAQD;QAChD,MAAMhF,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACgF,OAAO,CAACD,aAAa;YAAE7E,WAAW;QAAK;QACpE,MAAML,aAAE,CAACC,QAAQ,CAACmF,SAAS,CAACF,YAAY3D;IAC1C;IAGJqB,QAAG,CAACC,GAAG,CAAC;AACV;AAEA,SAASxB,QAAWgE,KAAU,EAAEC,GAAwB;IACtD,MAAMP,MAAM,IAAI5B;IAChBkC,MAAMhC,OAAO,CAAC,CAACkC;QACb,MAAMC,QAAQF,IAAIC;QAClB,MAAME,OAAOV,IAAIpB,GAAG,CAAC6B,UAAU,EAAE;QACjCC,KAAKzE,IAAI,CAACuE;QACVR,IAAIrB,GAAG,CAAC8B,OAAOC;IACjB;IACA,OAAOV;AACT;AAGO,SAASrF,yBACdgG,SAAwB,EACxB,EACEC,iBAAiB,EACjB9F,QAAQ,IAAIsD,KAAK,EACjBK,QAAQ,EACRoC,iBAAiBpC,aAAa,KAAK,EAMpC;IAEDkC,UAAUrC,OAAO,CAAC,CAACwC;QACjB,IAAIA,SAASC,IAAI,KAAK,gBAAgB;YACpC;QACF;QACAjG,MAAM6D,GAAG,CAACmC,SAASjB,QAAQ,EAAE;YAC3BrD,UAAUsE,SAASE,MAAM;YACzBC,gBAAgBH,SAASG,cAAc;YACvClF,cAAc8E,iBAAiB,WAAWK;QAC5C;IACF;IAEA,OAAOpG;AACT"}