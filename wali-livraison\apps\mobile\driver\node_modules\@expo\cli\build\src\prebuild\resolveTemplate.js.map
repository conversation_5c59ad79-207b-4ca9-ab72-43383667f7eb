{"version": 3, "sources": ["../../../src/prebuild/resolveTemplate.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport { Ora } from 'ora';\nimport semver from 'semver';\n\nimport { type ResolvedTemplateOption } from './resolveOptions';\nimport { fetchAsync } from '../api/rest/client';\nimport * as Log from '../log';\nimport { createGlobFilter } from '../utils/createFileTransform';\nimport { AbortCommandError } from '../utils/errors';\nimport {\n  ExtractProps,\n  downloadAndExtractNpmModuleAsync,\n  extractLocalNpmTarballAsync,\n  extractNpmTarballFromUrlAsync,\n} from '../utils/npm';\nimport { isUrlOk } from '../utils/url';\n\nconst debug = require('debug')('expo:prebuild:resolveTemplate') as typeof console.log;\n\ntype RepoInfo = {\n  username: string;\n  name: string;\n  branch: string;\n  filePath: string;\n};\n\nexport async function cloneTemplateAsync({\n  templateDirectory,\n  template,\n  exp,\n  ora,\n}: {\n  templateDirectory: string;\n  template?: ResolvedTemplateOption;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  ora: Ora;\n}): Promise<string> {\n  if (template) {\n    const appName = exp.name;\n    const { type, uri } = template;\n    if (type === 'file') {\n      return await extractLocalNpmTarballAsync(uri, {\n        cwd: templateDirectory,\n        name: appName,\n      });\n    } else if (type === 'npm') {\n      return await downloadAndExtractNpmModuleAsync(uri, {\n        cwd: templateDirectory,\n        name: appName,\n      });\n    } else if (type === 'repository') {\n      return await resolveAndDownloadRepoTemplateAsync(templateDirectory, ora, appName, uri);\n    } else {\n      throw new Error(`Unknown template type: ${type}`);\n    }\n  } else {\n    const templatePackageName = await getTemplateNpmPackageName(exp.sdkVersion);\n    return await downloadAndExtractNpmModuleAsync(templatePackageName, {\n      cwd: templateDirectory,\n      name: exp.name,\n    });\n  }\n}\n\n/** Given an `sdkVersion` like `44.0.0` return a fully qualified NPM package name like: `expo-template-bare-minimum@sdk-44` */\nfunction getTemplateNpmPackageName(sdkVersion?: string): string {\n  // When undefined or UNVERSIONED, we use the latest version.\n  if (!sdkVersion || sdkVersion === 'UNVERSIONED') {\n    Log.log('Using an unspecified Expo SDK version. The latest template will be used.');\n    return `expo-template-bare-minimum@latest`;\n  }\n  return `expo-template-bare-minimum@sdk-${semver.major(sdkVersion)}`;\n}\n\nasync function getRepoInfo(url: any, examplePath?: string): Promise<RepoInfo | undefined> {\n  const [, username, name, t, _branch, ...file] = url.pathname.split('/');\n  const filePath = examplePath ? examplePath.replace(/^\\//, '') : file.join('/');\n\n  // Support repos whose entire purpose is to be an example, e.g.\n  // https://github.com/:username/:my-cool-example-repo-name.\n  if (t === undefined) {\n    const infoResponse = await fetchAsync(`https://api.github.com/repos/${username}/${name}`);\n    if (infoResponse.status !== 200) {\n      return;\n    }\n    const info: any = await infoResponse.json();\n    return { username, name, branch: info['default_branch'], filePath };\n  }\n\n  // If examplePath is available, the branch name takes the entire path\n  const branch = examplePath\n    ? `${_branch}/${file.join('/')}`.replace(new RegExp(`/${filePath}|/$`), '')\n    : _branch;\n\n  if (username && name && branch && t === 'tree') {\n    return { username, name, branch, filePath };\n  }\n  return undefined;\n}\n\nfunction hasRepo({ username, name, branch, filePath }: RepoInfo) {\n  const contentsUrl = `https://api.github.com/repos/${username}/${name}/contents`;\n  const packagePath = `${filePath ? `/${filePath}` : ''}/package.json`;\n\n  return isUrlOk(contentsUrl + packagePath + `?ref=${branch}`);\n}\n\nasync function downloadAndExtractRepoAsync(\n  { username, name, branch, filePath }: RepoInfo,\n  props: ExtractProps\n): Promise<string> {\n  const url = `https://codeload.github.com/${username}/${name}/tar.gz/${branch}`;\n\n  debug('Downloading tarball from:', url);\n\n  // Extract the (sub)directory into non-empty path segments\n  const directory = filePath.replace(/^\\//, '').split('/').filter(Boolean);\n  // Remove the (sub)directory paths, and the root folder added by GitHub\n  const strip = directory.length + 1;\n  // Only extract the relevant (sub)directories, ignoring irrelevant files\n  // The filder auto-ignores dotfiles, unless explicitly included\n  const filter = createGlobFilter(\n    !directory.length\n      ? ['*/**', '*/ios/.xcode.env']\n      : [`*/${directory.join('/')}/**`, `*/${directory.join('/')}/ios/.xcode.env`],\n    {\n      // Always ignore the `.xcworkspace` folder\n      ignore: ['**/ios/*.xcworkspace/**'],\n    }\n  );\n\n  return await extractNpmTarballFromUrlAsync(url, { ...props, strip, filter });\n}\n\nasync function resolveAndDownloadRepoTemplateAsync(\n  templateDirectory: string,\n  oraInstance: Ora,\n  appName: string,\n  template: string,\n  templatePath?: string\n) {\n  let repoUrl: URL | undefined;\n\n  try {\n    repoUrl = new URL(template);\n  } catch (error: any) {\n    if (error.code !== 'ERR_INVALID_URL') {\n      oraInstance.fail(error);\n      throw error;\n    }\n  }\n  if (!repoUrl) {\n    oraInstance.fail(`Invalid URL: ${chalk.red(`\"${template}\"`)}. Try again with a valid URL.`);\n    throw new AbortCommandError();\n  }\n\n  if (repoUrl.origin !== 'https://github.com') {\n    oraInstance.fail(\n      `Invalid URL: ${chalk.red(\n        `\"${template}\"`\n      )}. Only GitHub repositories are supported. Try again with a valid GitHub URL.`\n    );\n    throw new AbortCommandError();\n  }\n\n  const repoInfo = await getRepoInfo(repoUrl, templatePath);\n\n  if (!repoInfo) {\n    oraInstance.fail(\n      `Found invalid GitHub URL: ${chalk.red(`\"${template}\"`)}. Fix the URL and try again.`\n    );\n    throw new AbortCommandError();\n  }\n\n  const found = await hasRepo(repoInfo);\n\n  if (!found) {\n    oraInstance.fail(\n      `Could not locate the repository for ${chalk.red(\n        `\"${template}\"`\n      )}. Check that the repository exists and try again.`\n    );\n    throw new AbortCommandError();\n  }\n\n  oraInstance.text = chalk.bold(\n    `Downloading files from repo ${chalk.cyan(template)}. This might take a moment.`\n  );\n\n  return await downloadAndExtractRepoAsync(repoInfo, {\n    cwd: templateDirectory,\n    name: appName,\n  });\n}\n"], "names": ["cloneTemplateAsync", "debug", "require", "templateDirectory", "template", "exp", "ora", "appName", "name", "type", "uri", "extractLocalNpmTarballAsync", "cwd", "downloadAndExtractNpmModuleAsync", "resolveAndDownloadRepoTemplateAsync", "Error", "templatePackageName", "getTemplateNpmPackageName", "sdkVersion", "Log", "log", "semver", "major", "getRepoInfo", "url", "examplePath", "username", "t", "_branch", "file", "pathname", "split", "filePath", "replace", "join", "undefined", "infoResponse", "fetchAsync", "status", "info", "json", "branch", "RegExp", "hasRepo", "contentsUrl", "packagePath", "isUrlOk", "downloadAndExtractRepoAsync", "props", "directory", "filter", "Boolean", "strip", "length", "createGlobFilter", "ignore", "extractNpmTarballFromUrlAsync", "oraInstance", "templatePath", "repoUrl", "URL", "error", "code", "fail", "chalk", "red", "AbortCommandError", "origin", "repoInfo", "found", "text", "bold", "cyan"], "mappings": ";;;;+BA2BsBA;;;eAAAA;;;;gEA1BJ;;;;;;;gEAEC;;;;;;wBAGQ;6DACN;qCACY;wBACC;qBAM3B;qBACiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,MAAMC,QAAQC,QAAQ,SAAS;AASxB,eAAeF,mBAAmB,EACvCG,iBAAiB,EACjBC,QAAQ,EACRC,GAAG,EACHC,GAAG,EAMJ;IACC,IAAIF,UAAU;QACZ,MAAMG,UAAUF,IAAIG,IAAI;QACxB,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAE,GAAGN;QACtB,IAAIK,SAAS,QAAQ;YACnB,OAAO,MAAME,IAAAA,gCAA2B,EAACD,KAAK;gBAC5CE,KAAKT;gBACLK,MAAMD;YACR;QACF,OAAO,IAAIE,SAAS,OAAO;YACzB,OAAO,MAAMI,IAAAA,qCAAgC,EAACH,KAAK;gBACjDE,KAAKT;gBACLK,MAAMD;YACR;QACF,OAAO,IAAIE,SAAS,cAAc;YAChC,OAAO,MAAMK,oCAAoCX,mBAAmBG,KAAKC,SAASG;QACpF,OAAO;YACL,MAAM,IAAIK,MAAM,CAAC,uBAAuB,EAAEN,MAAM;QAClD;IACF,OAAO;QACL,MAAMO,sBAAsB,MAAMC,0BAA0BZ,IAAIa,UAAU;QAC1E,OAAO,MAAML,IAAAA,qCAAgC,EAACG,qBAAqB;YACjEJ,KAAKT;YACLK,MAAMH,IAAIG,IAAI;QAChB;IACF;AACF;AAEA,4HAA4H,GAC5H,SAASS,0BAA0BC,UAAmB;IACpD,4DAA4D;IAC5D,IAAI,CAACA,cAAcA,eAAe,eAAe;QAC/CC,KAAIC,GAAG,CAAC;QACR,OAAO,CAAC,iCAAiC,CAAC;IAC5C;IACA,OAAO,CAAC,+BAA+B,EAAEC,iBAAM,CAACC,KAAK,CAACJ,aAAa;AACrE;AAEA,eAAeK,YAAYC,GAAQ,EAAEC,WAAoB;IACvD,MAAM,GAAGC,UAAUlB,MAAMmB,GAAGC,SAAS,GAAGC,KAAK,GAAGL,IAAIM,QAAQ,CAACC,KAAK,CAAC;IACnE,MAAMC,WAAWP,cAAcA,YAAYQ,OAAO,CAAC,OAAO,MAAMJ,KAAKK,IAAI,CAAC;IAE1E,+DAA+D;IAC/D,2DAA2D;IAC3D,IAAIP,MAAMQ,WAAW;QACnB,MAAMC,eAAe,MAAMC,IAAAA,kBAAU,EAAC,CAAC,6BAA6B,EAAEX,SAAS,CAAC,EAAElB,MAAM;QACxF,IAAI4B,aAAaE,MAAM,KAAK,KAAK;YAC/B;QACF;QACA,MAAMC,OAAY,MAAMH,aAAaI,IAAI;QACzC,OAAO;YAAEd;YAAUlB;YAAMiC,QAAQF,IAAI,CAAC,iBAAiB;YAAEP;QAAS;IACpE;IAEA,qEAAqE;IACrE,MAAMS,SAAShB,cACX,GAAGG,QAAQ,CAAC,EAAEC,KAAKK,IAAI,CAAC,MAAM,CAACD,OAAO,CAAC,IAAIS,OAAO,CAAC,CAAC,EAAEV,SAAS,GAAG,CAAC,GAAG,MACtEJ;IAEJ,IAAIF,YAAYlB,QAAQiC,UAAUd,MAAM,QAAQ;QAC9C,OAAO;YAAED;YAAUlB;YAAMiC;YAAQT;QAAS;IAC5C;IACA,OAAOG;AACT;AAEA,SAASQ,QAAQ,EAAEjB,QAAQ,EAAElB,IAAI,EAAEiC,MAAM,EAAET,QAAQ,EAAY;IAC7D,MAAMY,cAAc,CAAC,6BAA6B,EAAElB,SAAS,CAAC,EAAElB,KAAK,SAAS,CAAC;IAC/E,MAAMqC,cAAc,GAAGb,WAAW,CAAC,CAAC,EAAEA,UAAU,GAAG,GAAG,aAAa,CAAC;IAEpE,OAAOc,IAAAA,YAAO,EAACF,cAAcC,cAAc,CAAC,KAAK,EAAEJ,QAAQ;AAC7D;AAEA,eAAeM,4BACb,EAAErB,QAAQ,EAAElB,IAAI,EAAEiC,MAAM,EAAET,QAAQ,EAAY,EAC9CgB,KAAmB;IAEnB,MAAMxB,MAAM,CAAC,4BAA4B,EAAEE,SAAS,CAAC,EAAElB,KAAK,QAAQ,EAAEiC,QAAQ;IAE9ExC,MAAM,6BAA6BuB;IAEnC,0DAA0D;IAC1D,MAAMyB,YAAYjB,SAASC,OAAO,CAAC,OAAO,IAAIF,KAAK,CAAC,KAAKmB,MAAM,CAACC;IAChE,uEAAuE;IACvE,MAAMC,QAAQH,UAAUI,MAAM,GAAG;IACjC,wEAAwE;IACxE,+DAA+D;IAC/D,MAAMH,SAASI,IAAAA,qCAAgB,EAC7B,CAACL,UAAUI,MAAM,GACb;QAAC;QAAQ;KAAmB,GAC5B;QAAC,CAAC,EAAE,EAAEJ,UAAUf,IAAI,CAAC,KAAK,GAAG,CAAC;QAAE,CAAC,EAAE,EAAEe,UAAUf,IAAI,CAAC,KAAK,eAAe,CAAC;KAAC,EAC9E;QACE,0CAA0C;QAC1CqB,QAAQ;YAAC;SAA0B;IACrC;IAGF,OAAO,MAAMC,IAAAA,kCAA6B,EAAChC,KAAK;QAAE,GAAGwB,KAAK;QAAEI;QAAOF;IAAO;AAC5E;AAEA,eAAepC,oCACbX,iBAAyB,EACzBsD,WAAgB,EAChBlD,OAAe,EACfH,QAAgB,EAChBsD,YAAqB;IAErB,IAAIC;IAEJ,IAAI;QACFA,UAAU,IAAIC,IAAIxD;IACpB,EAAE,OAAOyD,OAAY;QACnB,IAAIA,MAAMC,IAAI,KAAK,mBAAmB;YACpCL,YAAYM,IAAI,CAACF;YACjB,MAAMA;QACR;IACF;IACA,IAAI,CAACF,SAAS;QACZF,YAAYM,IAAI,CAAC,CAAC,aAAa,EAAEC,gBAAK,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE7D,SAAS,CAAC,CAAC,EAAE,6BAA6B,CAAC;QAC1F,MAAM,IAAI8D,yBAAiB;IAC7B;IAEA,IAAIP,QAAQQ,MAAM,KAAK,sBAAsB;QAC3CV,YAAYM,IAAI,CACd,CAAC,aAAa,EAAEC,gBAAK,CAACC,GAAG,CACvB,CAAC,CAAC,EAAE7D,SAAS,CAAC,CAAC,EACf,4EAA4E,CAAC;QAEjF,MAAM,IAAI8D,yBAAiB;IAC7B;IAEA,MAAME,WAAW,MAAM7C,YAAYoC,SAASD;IAE5C,IAAI,CAACU,UAAU;QACbX,YAAYM,IAAI,CACd,CAAC,0BAA0B,EAAEC,gBAAK,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE7D,SAAS,CAAC,CAAC,EAAE,4BAA4B,CAAC;QAEvF,MAAM,IAAI8D,yBAAiB;IAC7B;IAEA,MAAMG,QAAQ,MAAM1B,QAAQyB;IAE5B,IAAI,CAACC,OAAO;QACVZ,YAAYM,IAAI,CACd,CAAC,oCAAoC,EAAEC,gBAAK,CAACC,GAAG,CAC9C,CAAC,CAAC,EAAE7D,SAAS,CAAC,CAAC,EACf,iDAAiD,CAAC;QAEtD,MAAM,IAAI8D,yBAAiB;IAC7B;IAEAT,YAAYa,IAAI,GAAGN,gBAAK,CAACO,IAAI,CAC3B,CAAC,4BAA4B,EAAEP,gBAAK,CAACQ,IAAI,CAACpE,UAAU,2BAA2B,CAAC;IAGlF,OAAO,MAAM2C,4BAA4BqB,UAAU;QACjDxD,KAAKT;QACLK,MAAMD;IACR;AACF"}