{"version": 3, "sources": ["../../../src/export/writeContents.ts"], "sourcesContent": ["import { Asset } from './saveAssets';\n\nexport function createAssetMap({ assets }: { assets: Asset[] }) {\n  // Convert the assets array to a k/v pair where the asset hash is the key and the asset is the value.\n  return Object.fromEntries(assets.map((asset) => [asset.hash, asset]));\n}\n"], "names": ["createAssetMap", "assets", "Object", "fromEntries", "map", "asset", "hash"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,eAAe,EAAEC,MAAM,EAAuB;IAC5D,qGAAqG;IACrG,OAAOC,OAAOC,WAAW,CAACF,OAAOG,GAAG,CAAC,CAACC,QAAU;YAACA,MAAMC,IAAI;YAAED;SAAM;AACrE"}