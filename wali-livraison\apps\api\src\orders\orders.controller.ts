import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Param, 
  Body, 
  Query, 
  UseGuards 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { OrdersService, CreateOrderDto, UpdateOrderStatusDto } from './orders.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UserRole, OrderStatus, OrderType } from '@prisma/client';

@ApiTags('Orders')
@Controller('orders')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class OrdersController {
  constructor(private ordersService: OrdersService) {}

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.CLIENT)
  @ApiOperation({ summary: 'Créer une nouvelle commande' })
  @ApiResponse({ status: 201, description: 'Commande créée avec succès' })
  async create(
    @Body() createOrderDto: CreateOrderDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.create(createOrderDto, user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Récupérer les commandes' })
  @ApiQuery({ name: 'clientId', required: false })
  @ApiQuery({ name: 'driverId', required: false })
  @ApiQuery({ name: 'storeId', required: false })
  @ApiQuery({ name: 'status', required: false, enum: OrderStatus })
  @ApiQuery({ name: 'orderType', required: false, enum: OrderType })
  @ApiResponse({ status: 200, description: 'Liste des commandes' })
  async findAll(
    @Query('clientId') clientId?: string,
    @Query('driverId') driverId?: string,
    @Query('storeId') storeId?: string,
    @Query('status') status?: OrderStatus,
    @Query('orderType') orderType?: OrderType,
    @CurrentUser() user?: any,
  ) {
    // Filtrer selon le rôle de l'utilisateur
    if (user.role === UserRole.CLIENT) {
      clientId = user.id;
    } else if (user.role === UserRole.DRIVER) {
      // Pour les drivers, récupérer leurs commandes assignées
      const driverProfile = await this.ordersService['prisma'].driverProfile.findUnique({
        where: { userId: user.id },
      });
      if (driverProfile) {
        driverId = driverProfile.id;
      }
    }

    return this.ordersService.findAll(clientId, driverId, storeId, status, orderType);
  }

  @Get('available')
  @UseGuards(RolesGuard)
  @Roles(UserRole.DRIVER)
  @ApiOperation({ summary: 'Récupérer les commandes disponibles pour les drivers' })
  @ApiResponse({ status: 200, description: 'Commandes disponibles' })
  async getAvailableOrders(@CurrentUser() user: any) {
    // TODO: Récupérer la localisation du driver
    return this.ordersService.getAvailableOrders();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Récupérer une commande par ID' })
  @ApiResponse({ status: 200, description: 'Commande trouvée' })
  @ApiResponse({ status: 404, description: 'Commande non trouvée' })
  async findOne(@Param('id') id: string) {
    return this.ordersService.findOne(id);
  }

  @Put(':id/assign')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Assigner un driver à une commande (Admin seulement)' })
  @ApiResponse({ status: 200, description: 'Driver assigné' })
  @ApiResponse({ status: 404, description: 'Commande non trouvée' })
  async assignDriver(
    @Param('id') orderId: string,
    @Body() assignData: { driverId: string },
  ) {
    return this.ordersService.assignDriver(orderId, assignData.driverId);
  }

  @Put(':id/accept')
  @UseGuards(RolesGuard)
  @Roles(UserRole.DRIVER)
  @ApiOperation({ summary: 'Accepter une commande (Driver seulement)' })
  @ApiResponse({ status: 200, description: 'Commande acceptée' })
  @ApiResponse({ status: 403, description: 'Commande non disponible' })
  async acceptOrder(
    @Param('id') orderId: string,
    @CurrentUser() user: any,
  ) {
    // Récupérer le profil driver
    const driverProfile = await this.ordersService['prisma'].driverProfile.findUnique({
      where: { userId: user.id },
    });

    if (!driverProfile) {
      throw new Error('Profil driver non trouvé');
    }

    return this.ordersService.acceptOrder(orderId, driverProfile.id);
  }

  @Put(':id/status')
  @ApiOperation({ summary: 'Mettre à jour le statut d\'une commande' })
  @ApiResponse({ status: 200, description: 'Statut mis à jour' })
  @ApiResponse({ status: 403, description: 'Non autorisé' })
  @ApiResponse({ status: 404, description: 'Commande non trouvée' })
  async updateStatus(
    @Param('id') orderId: string,
    @Body() updateStatusDto: UpdateOrderStatusDto,
    @CurrentUser() user: any,
  ) {
    return this.ordersService.updateStatus(
      orderId, 
      updateStatusDto, 
      user.id, 
      user.role
    );
  }
}
