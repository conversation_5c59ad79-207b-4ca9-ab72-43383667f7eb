{"version": 3, "sources": ["../../../src/prebuild/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk';\n\nimport { Command } from '../../bin/cli';\nimport { assertArgs, getProjectRoot, printHelp } from '../utils/args';\n\nexport const expoPrebuild: Command = async (argv) => {\n  const args = assertArgs(\n    {\n      // Types\n      '--help': Boolean,\n      '--clean': Boolean,\n      '--npm': Boolean,\n      '--pnpm': Boolean,\n      '--yarn': Boolean,\n      '--bun': Boolean,\n      '--no-install': Boolean,\n      '--template': String,\n      '--platform': String,\n      '--skip-dependency-update': String,\n      // Aliases\n      '-h': '--help',\n      '-p': '--platform',\n      '-t': '--type',\n    },\n    argv\n  );\n\n  if (args['--help']) {\n    printHelp(\n      `Create native iOS and Android project files for building natively`,\n      chalk`npx expo prebuild {dim <dir>}`,\n      [\n        chalk`<dir>                                    Directory of the Expo project. {dim Default: Current working directory}`,\n        `--no-install                             Skip installing npm packages and CocoaPods`,\n        `--clean                                  Delete the native folders and regenerate them before applying changes`,\n        chalk`--npm                                    Use npm to install dependencies. {dim Default when package-lock.json exists}`,\n        chalk`--yarn                                   Use Yarn to install dependencies. {dim Default when yarn.lock exists}`,\n        chalk`--bun                                    Use bun to install dependencies. {dim Default when bun.lock or bun.lockb exists}`,\n        chalk`--pnpm                                   Use pnpm to install dependencies. {dim Default when pnpm-lock.yaml exists}`,\n        `--template <template>                    Project template to clone from. File path pointing to a local tar file, npm package or a github repo`,\n        chalk`-p, --platform <all|android|ios>         Platforms to sync: ios, android, all. {dim Default: all}`,\n        `--skip-dependency-update <dependencies>  Preserves versions of listed packages in package.json (comma separated list)`,\n        `-h, --help                               Usage info`,\n      ].join('\\n')\n    );\n  }\n\n  // Load modules after the help prompt so `npx expo prebuild -h` shows as fast as possible.\n  const [\n    // ./prebuildAsync\n    { prebuildAsync },\n    // ./resolveOptions\n    { resolvePlatformOption, resolvePackageManagerOptions, resolveSkipDependencyUpdate },\n    // ../utils/errors\n    { logCmdError },\n  ] = await Promise.all([\n    import('./prebuildAsync.js'),\n    import('./resolveOptions.js'),\n    import('../utils/errors.js'),\n  ]);\n\n  return (() => {\n    return prebuildAsync(getProjectRoot(args), {\n      // Parsed options\n      clean: args['--clean'],\n\n      packageManager: resolvePackageManagerOptions(args),\n      install: !args['--no-install'],\n      platforms: resolvePlatformOption(args['--platform']),\n      // TODO: Parse\n      skipDependencyUpdate: resolveSkipDependencyUpdate(args['--skip-dependency-update']),\n      template: args['--template'],\n    });\n  })().catch(logCmdError);\n};\n"], "names": ["expoPrebuild", "argv", "args", "assertArgs", "Boolean", "String", "printHelp", "chalk", "join", "prebuildAsync", "resolvePlatformOption", "resolvePackageManagerOptions", "resolveSkipDependencyUpdate", "logCmdError", "Promise", "all", "getProjectRoot", "clean", "packageManager", "install", "platforms", "skipDependencyUpdate", "template", "catch"], "mappings": ";;;;;+BAMaA;;;eAAAA;;;;gEALK;;;;;;sBAGoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMA,eAAwB,OAAOC;IAC1C,MAAMC,OAAOC,IAAAA,gBAAU,EACrB;QACE,QAAQ;QACR,UAAUC;QACV,WAAWA;QACX,SAASA;QACT,UAAUA;QACV,UAAUA;QACV,SAASA;QACT,gBAAgBA;QAChB,cAAcC;QACd,cAAcA;QACd,4BAA4BA;QAC5B,UAAU;QACV,MAAM;QACN,MAAM;QACN,MAAM;IACR,GACAJ;IAGF,IAAIC,IAAI,CAAC,SAAS,EAAE;QAClBI,IAAAA,eAAS,EACP,CAAC,iEAAiE,CAAC,EACnEC,IAAAA,gBAAK,CAAA,CAAC,6BAA6B,CAAC,EACpC;YACEA,IAAAA,gBAAK,CAAA,CAAC,gHAAgH,CAAC;YACvH,CAAC,mFAAmF,CAAC;YACrF,CAAC,8GAA8G,CAAC;YAChHA,IAAAA,gBAAK,CAAA,CAAC,qHAAqH,CAAC;YAC5HA,IAAAA,gBAAK,CAAA,CAAC,8GAA8G,CAAC;YACrHA,IAAAA,gBAAK,CAAA,CAAC,yHAAyH,CAAC;YAChIA,IAAAA,gBAAK,CAAA,CAAC,mHAAmH,CAAC;YAC1H,CAAC,6IAA6I,CAAC;YAC/IA,IAAAA,gBAAK,CAAA,CAAC,iGAAiG,CAAC;YACxG,CAAC,qHAAqH,CAAC;YACvH,CAAC,mDAAmD,CAAC;SACtD,CAACC,IAAI,CAAC;IAEX;IAEA,0FAA0F;IAC1F,MAAM,CACJ,kBAAkB;IAClB,EAAEC,aAAa,EAAE,EACjB,mBAAmB;IACnB,EAAEC,qBAAqB,EAAEC,4BAA4B,EAAEC,2BAA2B,EAAE,EACpF,kBAAkB;IAClB,EAAEC,WAAW,EAAE,CAChB,GAAG,MAAMC,QAAQC,GAAG,CAAC;QACpB,mEAAA,QAAO;QACP,mEAAA,QAAO;QACP,mEAAA,QAAO;KACR;IAED,OAAO,AAAC,CAAA;QACN,OAAON,cAAcO,IAAAA,oBAAc,EAACd,OAAO;YACzC,iBAAiB;YACjBe,OAAOf,IAAI,CAAC,UAAU;YAEtBgB,gBAAgBP,6BAA6BT;YAC7CiB,SAAS,CAACjB,IAAI,CAAC,eAAe;YAC9BkB,WAAWV,sBAAsBR,IAAI,CAAC,aAAa;YACnD,cAAc;YACdmB,sBAAsBT,4BAA4BV,IAAI,CAAC,2BAA2B;YAClFoB,UAAUpB,IAAI,CAAC,aAAa;QAC9B;IACF,CAAA,IAAKqB,KAAK,CAACV;AACb"}