import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import Stripe from 'stripe';
import axios from 'axios';
import { PaymentMethod, TransactionType } from '@prisma/client';

export interface CreatePaymentDto {
  orderId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  phoneNumber?: string; // Pour les paiements mobiles
  currency?: string;
}

export interface PaymentResponse {
  success: boolean;
  transactionId: string;
  providerId?: string;
  message?: string;
}

@Injectable()
export class PaymentsService {
  private readonly stripe: Stripe;

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {
    this.stripe = new Stripe(this.configService.get('STRIPE_SECRET_KEY'), {
      apiVersion: '2024-06-20',
    });
  }

  async processPayment(createPaymentDto: CreatePaymentDto, userId: string): Promise<PaymentResponse> {
    const { orderId, amount, paymentMethod, phoneNumber, currency = 'XOF' } = createPaymentDto;

    // Vérifier que la commande existe
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      throw new BadRequestException('Commande non trouvée');
    }

    let result: PaymentResponse;

    switch (paymentMethod) {
      case PaymentMethod.STRIPE:
        result = await this.processStripePayment(amount, currency);
        break;
      case PaymentMethod.ORANGE_MONEY:
        result = await this.processOrangeMoneyPayment(amount, phoneNumber);
        break;
      case PaymentMethod.MTN_MONEY:
        result = await this.processMTNMoneyPayment(amount, phoneNumber);
        break;
      case PaymentMethod.WAVE:
        result = await this.processWavePayment(amount, phoneNumber);
        break;
      case PaymentMethod.CASH:
        result = await this.processCashPayment();
        break;
      default:
        throw new BadRequestException('Méthode de paiement non supportée');
    }

    // Enregistrer la transaction
    await this.prisma.transaction.create({
      data: {
        userId,
        orderId,
        amount,
        type: TransactionType.ORDER_PAYMENT,
        paymentMethod,
        status: result.success ? 'COMPLETED' : 'FAILED',
        providerId: result.providerId,
      },
    });

    return result;
  }

  private async processStripePayment(amount: number, currency: string): Promise<PaymentResponse> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Stripe utilise les centimes
        currency: currency.toLowerCase(),
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return {
        success: true,
        transactionId: paymentIntent.id,
        providerId: paymentIntent.id,
      };
    } catch (error) {
      return {
        success: false,
        transactionId: '',
        message: error.message,
      };
    }
  }

  private async processOrangeMoneyPayment(amount: number, phoneNumber: string): Promise<PaymentResponse> {
    try {
      // Configuration Orange Money
      const apiUrl = this.configService.get('ORANGE_MONEY_API_URL');
      const clientId = this.configService.get('ORANGE_MONEY_CLIENT_ID');
      const clientSecret = this.configService.get('ORANGE_MONEY_CLIENT_SECRET');
      const merchantKey = this.configService.get('ORANGE_MONEY_MERCHANT_KEY');

      // 1. Obtenir le token d'accès
      const tokenResponse = await axios.post(`${apiUrl}/oauth/v3/token`, {
        grant_type: 'client_credentials',
      }, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      const accessToken = tokenResponse.data.access_token;

      // 2. Initier le paiement
      const paymentResponse = await axios.post(`${apiUrl}/webpayment/v1/transactioninit`, {
        merchant_key: merchantKey,
        currency: 'XOF',
        order_id: `ORDER_${Date.now()}`,
        amount: amount,
        return_url: `${this.configService.get('API_URL')}/payments/orange/callback`,
        cancel_url: `${this.configService.get('API_URL')}/payments/orange/cancel`,
        notif_url: `${this.configService.get('API_URL')}/payments/orange/notify`,
        lang: 'fr',
        reference: `REF_${Date.now()}`,
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      return {
        success: true,
        transactionId: paymentResponse.data.pay_token,
        providerId: paymentResponse.data.pay_token,
      };
    } catch (error) {
      return {
        success: false,
        transactionId: '',
        message: `Erreur Orange Money: ${error.message}`,
      };
    }
  }

  private async processMTNMoneyPayment(amount: number, phoneNumber: string): Promise<PaymentResponse> {
    try {
      // Configuration MTN Mobile Money
      const apiUrl = this.configService.get('MTN_MOMO_API_URL');
      const subscriptionKey = this.configService.get('MTN_MOMO_SUBSCRIPTION_KEY');
      const apiUser = this.configService.get('MTN_MOMO_API_USER');
      const apiKey = this.configService.get('MTN_MOMO_API_KEY');

      // Générer un UUID pour la transaction
      const transactionId = `mtn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Initier le paiement
      const paymentResponse = await axios.post(
        `${apiUrl}/collection/v1_0/requesttopay`,
        {
          amount: amount.toString(),
          currency: 'EUR', // MTN utilise EUR pour les tests
          externalId: transactionId,
          payer: {
            partyIdType: 'MSISDN',
            partyId: phoneNumber,
          },
          payerMessage: 'Paiement Wali Livraison',
          payeeNote: 'Commande Wali Livraison',
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'X-Reference-Id': transactionId,
            'X-Target-Environment': 'sandbox',
            'Ocp-Apim-Subscription-Key': subscriptionKey,
            'Content-Type': 'application/json',
          },
        }
      );

      return {
        success: true,
        transactionId: transactionId,
        providerId: transactionId,
      };
    } catch (error) {
      return {
        success: false,
        transactionId: '',
        message: `Erreur MTN Money: ${error.message}`,
      };
    }
  }

  private async processWavePayment(amount: number, phoneNumber: string): Promise<PaymentResponse> {
    try {
      // Configuration Wave
      const apiUrl = this.configService.get('WAVE_API_URL');
      const apiKey = this.configService.get('WAVE_API_KEY');
      const secretKey = this.configService.get('WAVE_SECRET_KEY');

      const transactionId = `wave_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Initier le paiement Wave
      const paymentResponse = await axios.post(
        `${apiUrl}/checkout/sessions`,
        {
          amount: amount,
          currency: 'XOF',
          error_url: `${this.configService.get('API_URL')}/payments/wave/error`,
          success_url: `${this.configService.get('API_URL')}/payments/wave/success`,
          client_reference: transactionId,
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return {
        success: true,
        transactionId: transactionId,
        providerId: paymentResponse.data.id,
      };
    } catch (error) {
      return {
        success: false,
        transactionId: '',
        message: `Erreur Wave: ${error.message}`,
      };
    }
  }

  private async processCashPayment(): Promise<PaymentResponse> {
    // Pour les paiements en espèces, on marque comme en attente
    return {
      success: true,
      transactionId: `cash_${Date.now()}`,
      message: 'Paiement en espèces - En attente de confirmation',
    };
  }

  async getPaymentStatus(transactionId: string, paymentMethod: PaymentMethod) {
    const transaction = await this.prisma.transaction.findFirst({
      where: { providerId: transactionId },
    });

    if (!transaction) {
      throw new BadRequestException('Transaction non trouvée');
    }

    return {
      transactionId,
      status: transaction.status,
      amount: transaction.amount,
      paymentMethod: transaction.paymentMethod,
      createdAt: transaction.createdAt,
    };
  }
}
