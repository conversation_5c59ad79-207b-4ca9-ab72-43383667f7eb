{"version": 3, "sources": ["../../../../../src/api/rest/cache/ResponseCache.ts"], "sourcesContent": ["import crypto from 'crypto';\nimport { ReadStream } from 'fs';\nimport type { Response, RequestInfo, RequestInit } from 'undici';\n\nconst GLOBAL_CACHE_VERSION = 4;\n\nexport type ResponseCacheEntry = {\n  body: import('stream/web').ReadableStream;\n  info: ReturnType<typeof getResponseInfo>;\n};\n\nexport interface ResponseCache {\n  /** Load the response info from cache, if any */\n  get(cacheKey: string): Promise<ResponseCacheEntry | undefined>;\n  /** Store the response info to cache, and return the cached info */\n  set(cacheKey: string, response: ResponseCacheEntry): Promise<ResponseCacheEntry | undefined>;\n  /** Remove a response entry from the cache */\n  remove(cacheKey: string): Promise<void>;\n}\n\nexport function getResponseInfo(response: Response) {\n  return {\n    url: response.url,\n    status: response.status,\n    statusText: response.statusText,\n    headers: Object.fromEntries(response.headers.entries()),\n  };\n}\n\nexport function getRequestCacheKey(info: RequestInfo, init?: RequestInit) {\n  const infoKeyData = getRequestInfoCacheData(info);\n  const initKeyData = { body: init?.body ? getRequestBodyCacheData(init.body) : undefined };\n\n  return crypto\n    .createHash('md5')\n    .update(JSON.stringify([infoKeyData, initKeyData, GLOBAL_CACHE_VERSION]))\n    .digest('hex');\n}\n\n/** @internal Exposed for testing */\nexport function getRequestInfoCacheData(info: RequestInfo) {\n  if (typeof info === 'string') {\n    return { url: info };\n  }\n\n  if (info instanceof URL) {\n    return { url: info.toString() };\n  }\n\n  if (info instanceof Request) {\n    return {\n      // cache: req.cache,\n      credentials: info.credentials.toString(),\n      destination: info.destination.toString(),\n      headers: Object.fromEntries(info.headers.entries()),\n      integrity: info.integrity,\n      method: info.method,\n      redirect: info.redirect,\n      referrer: info.referrer,\n      referrerPolicy: info.referrerPolicy,\n      url: info.url.toString(),\n      // body: // TODO\n    };\n  }\n\n  throw new Error('Unsupported request info type for caching: ' + typeof info);\n}\n\n/** @internal Exposed for testing */\nexport function getRequestBodyCacheData(body: RequestInit['body']) {\n  if (!body) {\n    return body;\n  }\n\n  if (typeof body === 'string') {\n    return body;\n  }\n\n  if (body instanceof URLSearchParams) {\n    return body.toString();\n  }\n\n  // Supported for legacy purposes because node-fetch uses fs.readStream\n  if (body instanceof ReadStream) {\n    return body.path;\n  }\n\n  if (body.toString && body.toString() === '[object FormData]') {\n    return new URLSearchParams(body as any).toString();\n  }\n\n  if (body instanceof Buffer) {\n    return body.toString();\n  }\n\n  throw new Error(`Unsupported request body type for caching: ${typeof body}`);\n}\n"], "names": ["getRequestBodyCacheData", "getRequestCacheKey", "getRequestInfoCacheData", "getResponseInfo", "GLOBAL_CACHE_VERSION", "response", "url", "status", "statusText", "headers", "Object", "fromEntries", "entries", "info", "init", "infoKeyData", "initKeyData", "body", "undefined", "crypto", "createHash", "update", "JSON", "stringify", "digest", "URL", "toString", "Request", "credentials", "destination", "integrity", "method", "redirect", "referrer", "referrerPolicy", "Error", "URLSearchParams", "ReadStream", "path", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;IAqEgBA,uBAAuB;eAAvBA;;IAxCAC,kBAAkB;eAAlBA;;IAWAC,uBAAuB;eAAvBA;;IApBAC,eAAe;eAAfA;;;;gEApBG;;;;;;;yBACQ;;;;;;;;;;;AAG3B,MAAMC,uBAAuB;AAgBtB,SAASD,gBAAgBE,QAAkB;IAChD,OAAO;QACLC,KAAKD,SAASC,GAAG;QACjBC,QAAQF,SAASE,MAAM;QACvBC,YAAYH,SAASG,UAAU;QAC/BC,SAASC,OAAOC,WAAW,CAACN,SAASI,OAAO,CAACG,OAAO;IACtD;AACF;AAEO,SAASX,mBAAmBY,IAAiB,EAAEC,IAAkB;IACtE,MAAMC,cAAcb,wBAAwBW;IAC5C,MAAMG,cAAc;QAAEC,MAAMH,CAAAA,wBAAAA,KAAMG,IAAI,IAAGjB,wBAAwBc,KAAKG,IAAI,IAAIC;IAAU;IAExF,OAAOC,iBAAM,CACVC,UAAU,CAAC,OACXC,MAAM,CAACC,KAAKC,SAAS,CAAC;QAACR;QAAaC;QAAaZ;KAAqB,GACtEoB,MAAM,CAAC;AACZ;AAGO,SAAStB,wBAAwBW,IAAiB;IACvD,IAAI,OAAOA,SAAS,UAAU;QAC5B,OAAO;YAAEP,KAAKO;QAAK;IACrB;IAEA,IAAIA,gBAAgBY,KAAK;QACvB,OAAO;YAAEnB,KAAKO,KAAKa,QAAQ;QAAG;IAChC;IAEA,IAAIb,gBAAgBc,SAAS;QAC3B,OAAO;YACL,oBAAoB;YACpBC,aAAaf,KAAKe,WAAW,CAACF,QAAQ;YACtCG,aAAahB,KAAKgB,WAAW,CAACH,QAAQ;YACtCjB,SAASC,OAAOC,WAAW,CAACE,KAAKJ,OAAO,CAACG,OAAO;YAChDkB,WAAWjB,KAAKiB,SAAS;YACzBC,QAAQlB,KAAKkB,MAAM;YACnBC,UAAUnB,KAAKmB,QAAQ;YACvBC,UAAUpB,KAAKoB,QAAQ;YACvBC,gBAAgBrB,KAAKqB,cAAc;YACnC5B,KAAKO,KAAKP,GAAG,CAACoB,QAAQ;QAExB;IACF;IAEA,MAAM,IAAIS,MAAM,gDAAgD,OAAOtB;AACzE;AAGO,SAASb,wBAAwBiB,IAAyB;IAC/D,IAAI,CAACA,MAAM;QACT,OAAOA;IACT;IAEA,IAAI,OAAOA,SAAS,UAAU;QAC5B,OAAOA;IACT;IAEA,IAAIA,gBAAgBmB,iBAAiB;QACnC,OAAOnB,KAAKS,QAAQ;IACtB;IAEA,sEAAsE;IACtE,IAAIT,gBAAgBoB,gBAAU,EAAE;QAC9B,OAAOpB,KAAKqB,IAAI;IAClB;IAEA,IAAIrB,KAAKS,QAAQ,IAAIT,KAAKS,QAAQ,OAAO,qBAAqB;QAC5D,OAAO,IAAIU,gBAAgBnB,MAAaS,QAAQ;IAClD;IAEA,IAAIT,gBAAgBsB,QAAQ;QAC1B,OAAOtB,KAAKS,QAAQ;IACtB;IAEA,MAAM,IAAIS,MAAM,CAAC,2CAA2C,EAAE,OAAOlB,MAAM;AAC7E"}