"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let OrdersService = class OrdersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createOrderDto, clientId) {
        const { orderType, pickupAddress, deliveryAddress, storeId, items, price, deliveryFee } = createOrderDto;
        const totalAmount = price + deliveryFee;
        const order = await this.prisma.order.create({
            data: {
                orderType,
                clientId,
                pickupAddress,
                deliveryAddress,
                storeId,
                price,
                deliveryFee,
                totalAmount,
            },
        });
        if (items && items.length > 0) {
            await Promise.all(items.map(item => this.prisma.orderItem.create({
                data: {
                    orderId: order.id,
                    productId: item.productId,
                    quantity: item.quantity,
                    price: 0,
                },
            })));
        }
        return this.findOne(order.id);
    }
    async findAll(clientId, driverId, storeId, status, orderType) {
        const where = {};
        if (clientId)
            where.clientId = clientId;
        if (driverId)
            where.driverId = driverId;
        if (storeId)
            where.storeId = storeId;
        if (status)
            where.status = status;
        if (orderType)
            where.orderType = orderType;
        return this.prisma.order.findMany({
            where,
            include: {
                client: {
                    select: {
                        id: true,
                        phone: true,
                        clientProfile: {
                            select: { fullName: true },
                        },
                    },
                },
                driver: {
                    select: {
                        id: true,
                        user: {
                            select: {
                                id: true,
                                phone: true,
                            },
                        },
                        vehicle: true,
                    },
                },
                store: {
                    select: {
                        id: true,
                        name: true,
                        address: true,
                    },
                },
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                name: true,
                                price: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async findOne(id) {
        const order = await this.prisma.order.findUnique({
            where: { id },
            include: {
                client: {
                    select: {
                        id: true,
                        phone: true,
                        clientProfile: {
                            select: { fullName: true },
                        },
                    },
                },
                driver: {
                    select: {
                        id: true,
                        user: {
                            select: {
                                id: true,
                                phone: true,
                            },
                        },
                        vehicle: true,
                        currentLocation: true,
                    },
                },
                store: {
                    select: {
                        id: true,
                        name: true,
                        address: true,
                    },
                },
                items: {
                    include: {
                        product: {
                            select: {
                                id: true,
                                name: true,
                                description: true,
                                price: true,
                                imageUrl: true,
                            },
                        },
                    },
                },
                transaction: true,
                rating: true,
            },
        });
        if (!order) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        return order;
    }
    async assignDriver(orderId, driverId) {
        const order = await this.prisma.order.findUnique({
            where: { id: orderId },
        });
        if (!order) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        if (order.status !== client_1.OrderStatus.PENDING) {
            throw new common_1.ForbiddenException('Cette commande ne peut plus être assignée');
        }
        const driver = await this.prisma.driverProfile.findUnique({
            where: { id: driverId },
        });
        if (!driver || !driver.isOnline) {
            throw new common_1.ForbiddenException('Driver non disponible');
        }
        return this.prisma.order.update({
            where: { id: orderId },
            data: {
                driverId,
                status: client_1.OrderStatus.ACCEPTED,
            },
        });
    }
    async updateStatus(orderId, updateStatusDto, userId, userRole) {
        const order = await this.prisma.order.findUnique({
            where: { id: orderId },
            include: {
                driver: {
                    select: {
                        userId: true,
                    },
                },
            },
        });
        if (!order) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        const canUpdate = userRole === client_1.UserRole.ADMIN ||
            (userRole === client_1.UserRole.DRIVER && order.driver?.userId === userId) ||
            (userRole === client_1.UserRole.PARTNER && order.storeId);
        if (!canUpdate) {
            throw new common_1.ForbiddenException('Vous n\'avez pas l\'autorisation de modifier cette commande');
        }
        return this.prisma.order.update({
            where: { id: orderId },
            data: {
                status: updateStatusDto.status,
                proofOfDelivery: updateStatusDto.proofOfDelivery,
            },
        });
    }
    async getAvailableOrders(driverLocation) {
        return this.prisma.order.findMany({
            where: {
                status: client_1.OrderStatus.PENDING,
                driverId: null,
            },
            include: {
                client: {
                    select: {
                        phone: true,
                        clientProfile: {
                            select: { fullName: true },
                        },
                    },
                },
                store: {
                    select: {
                        name: true,
                        address: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'asc',
            },
        });
    }
    async acceptOrder(orderId, driverId) {
        return this.assignDriver(orderId, driverId);
    }
};
exports.OrdersService = OrdersService;
exports.OrdersService = OrdersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrdersService);
//# sourceMappingURL=orders.service.js.map