import { Controller, Post, Body, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PaymentsService, CreatePaymentDto } from './payments.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { PaymentMethod } from '@prisma/client';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('process')
  @ApiOperation({ summary: 'Traiter un paiement' })
  @ApiResponse({ status: 201, description: 'Paiement traité avec succès' })
  @ApiResponse({ status: 400, description: 'Erreur de paiement' })
  async processPayment(
    @Body() createPaymentDto: CreatePaymentDto,
    @CurrentUser() user: any,
  ) {
    return this.paymentsService.processPayment(createPaymentDto, user.id);
  }

  @Get('status/:transactionId')
  @ApiOperation({ summary: 'Vérifier le statut d\'un paiement' })
  @ApiResponse({ status: 200, description: 'Statut du paiement' })
  @ApiResponse({ status: 404, description: 'Transaction non trouvée' })
  async getPaymentStatus(
    @Param('transactionId') transactionId: string,
    @Body() body: { paymentMethod: PaymentMethod },
  ) {
    return this.paymentsService.getPaymentStatus(transactionId, body.paymentMethod);
  }

  // Webhooks pour les différents providers
  @Post('stripe/webhook')
  @ApiOperation({ summary: 'Webhook Stripe' })
  async stripeWebhook(@Body() body: any) {
    // TODO: Implémenter la gestion des webhooks Stripe
    console.log('Stripe webhook received:', body);
    return { received: true };
  }

  @Post('orange/callback')
  @ApiOperation({ summary: 'Callback Orange Money' })
  async orangeCallback(@Body() body: any) {
    // TODO: Implémenter la gestion des callbacks Orange Money
    console.log('Orange Money callback received:', body);
    return { received: true };
  }

  @Post('mtn/callback')
  @ApiOperation({ summary: 'Callback MTN Money' })
  async mtnCallback(@Body() body: any) {
    // TODO: Implémenter la gestion des callbacks MTN Money
    console.log('MTN Money callback received:', body);
    return { received: true };
  }

  @Post('wave/callback')
  @ApiOperation({ summary: 'Callback Wave' })
  async waveCallback(@Body() body: any) {
    // TODO: Implémenter la gestion des callbacks Wave
    console.log('Wave callback received:', body);
    return { received: true };
  }
}
