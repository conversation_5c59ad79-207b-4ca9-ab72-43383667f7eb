import { PrismaService } from '../prisma/prisma.service';
import { OrderType, OrderStatus, UserRole } from '@prisma/client';
export interface CreateOrderDto {
    orderType: OrderType;
    pickupAddress?: any;
    deliveryAddress: any;
    storeId?: string;
    items?: Array<{
        productId: string;
        quantity: number;
    }>;
    price: number;
    deliveryFee: number;
}
export interface UpdateOrderStatusDto {
    status: OrderStatus;
    proofOfDelivery?: string;
}
export declare class OrdersService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createOrderDto: CreateOrderDto, clientId: string): Promise<{
        store: {
            id: string;
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        transaction: {
            orderId: string | null;
            amount: number;
            paymentMethod: import(".prisma/client").$Enums.PaymentMethod;
            id: string;
            status: string;
            createdAt: Date;
            userId: string;
            type: import(".prisma/client").$Enums.TransactionType;
            providerId: string | null;
        } | null;
        rating: {
            orderId: string;
            id: string;
            createdAt: Date;
            ratedById: string;
            ratedDriverId: string | null;
            ratedStoreId: string | null;
            score: number;
            comment: string | null;
        } | null;
        client: {
            clientProfile: {
                fullName: string;
            } | null;
            id: string;
            phone: string | null;
        };
        driver: {
            user: {
                id: string;
                phone: string | null;
            };
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            id: string;
            currentLocation: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        items: ({
            product: {
                id: string;
                price: number;
                name: string;
                description: string | null;
                imageUrl: string | null;
            };
        } & {
            orderId: string;
            id: string;
            price: number;
            productId: string;
            quantity: number;
        })[];
    } & {
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findAll(clientId?: string, driverId?: string, storeId?: string, status?: OrderStatus, orderType?: OrderType): Promise<({
        store: {
            id: string;
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        client: {
            clientProfile: {
                fullName: string;
            } | null;
            id: string;
            phone: string | null;
        };
        driver: {
            user: {
                id: string;
                phone: string | null;
            };
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            id: string;
        } | null;
        items: ({
            product: {
                id: string;
                price: number;
                name: string;
            };
        } & {
            orderId: string;
            id: string;
            price: number;
            productId: string;
            quantity: number;
        })[];
    } & {
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    findOne(id: string): Promise<{
        store: {
            id: string;
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        transaction: {
            orderId: string | null;
            amount: number;
            paymentMethod: import(".prisma/client").$Enums.PaymentMethod;
            id: string;
            status: string;
            createdAt: Date;
            userId: string;
            type: import(".prisma/client").$Enums.TransactionType;
            providerId: string | null;
        } | null;
        rating: {
            orderId: string;
            id: string;
            createdAt: Date;
            ratedById: string;
            ratedDriverId: string | null;
            ratedStoreId: string | null;
            score: number;
            comment: string | null;
        } | null;
        client: {
            clientProfile: {
                fullName: string;
            } | null;
            id: string;
            phone: string | null;
        };
        driver: {
            user: {
                id: string;
                phone: string | null;
            };
            vehicle: {
                id: string;
                driverId: string;
                type: string;
                licensePlate: string;
            } | null;
            id: string;
            currentLocation: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        items: ({
            product: {
                id: string;
                price: number;
                name: string;
                description: string | null;
                imageUrl: string | null;
            };
        } & {
            orderId: string;
            id: string;
            price: number;
            productId: string;
            quantity: number;
        })[];
    } & {
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    assignDriver(orderId: string, driverId: string): Promise<{
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    updateStatus(orderId: string, updateStatusDto: UpdateOrderStatusDto, userId: string, userRole: UserRole): Promise<{
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    getAvailableOrders(driverLocation?: any): Promise<({
        store: {
            name: string;
            address: import("@prisma/client/runtime/library").JsonValue;
        } | null;
        client: {
            clientProfile: {
                fullName: string;
            } | null;
            phone: string | null;
        };
    } & {
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    })[]>;
    acceptOrder(orderId: string, driverId: string): Promise<{
        id: string;
        orderType: import(".prisma/client").$Enums.OrderType;
        status: import(".prisma/client").$Enums.OrderStatus;
        clientId: string;
        driverId: string | null;
        storeId: string | null;
        pickupAddress: import("@prisma/client/runtime/library").JsonValue | null;
        deliveryAddress: import("@prisma/client/runtime/library").JsonValue;
        price: number;
        deliveryFee: number;
        totalAmount: number;
        proofOfDelivery: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
