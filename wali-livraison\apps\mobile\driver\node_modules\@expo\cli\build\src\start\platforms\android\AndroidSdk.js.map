{"version": 3, "sources": ["../../../../../src/start/platforms/android/AndroidSdk.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\n/**\n * The default Android SDK locations per platform.\n * @see https://developer.android.com/studio/run/emulator-commandline#filedir\n * @see https://developer.android.com/studio/intro/studio-config#optimize-studio-windows\n */\nconst ANDROID_DEFAULT_LOCATION: Readonly<Partial<Record<NodeJS.Platform, string>>> = {\n  darwin: path.join(os.homedir(), 'Library', 'Android', 'sdk'),\n  linux: path.join(os.homedir(), 'Android', 'sdk'),\n  win32: path.join(os.homedir(), 'AppData', 'Local', 'Android', 'Sdk'),\n};\n\n/**\n * Resolve and validate the root folder where the Android SDK has been installed.\n * This checks both `ANDROID_HOME`, `ANDROID_SDK_ROOT`, and the default path for the current platform.\n * @see https://developer.android.com/studio/command-line/variables\n */\nexport function assertSdkRoot() {\n  if (process.env.ANDROID_HOME) {\n    assert(\n      fs.existsSync(process.env.ANDROID_HOME),\n      `Failed to resolve the Android SDK path. ANDROID_HOME is set to a non-existing path: ${process.env.ANDROID_HOME}`\n    );\n    return process.env.ANDROID_HOME;\n  }\n\n  if (process.env.ANDROID_SDK_ROOT) {\n    assert(\n      fs.existsSync(process.env.ANDROID_SDK_ROOT),\n      `Failed to resolve the Android SDK path. Deprecated ANDROID_SDK_ROOT is set to a non-existing path: ${process.env.ANDROID_SDK_ROOT}. Use ANDROID_HOME instead.`\n    );\n    return process.env.ANDROID_SDK_ROOT;\n  }\n\n  const defaultLocation = ANDROID_DEFAULT_LOCATION[process.platform];\n  if (defaultLocation) {\n    assert(\n      fs.existsSync(defaultLocation),\n      `Failed to resolve the Android SDK path. Default install location not found: ${defaultLocation}. Use ANDROID_HOME to set the Android SDK location.`\n    );\n    return defaultLocation;\n  }\n\n  return null;\n}\n"], "names": ["assertSdkRoot", "ANDROID_DEFAULT_LOCATION", "darwin", "path", "join", "os", "homedir", "linux", "win32", "process", "env", "ANDROID_HOME", "assert", "fs", "existsSync", "ANDROID_SDK_ROOT", "defaultLocation", "platform"], "mappings": ";;;;+BAqBgBA;;;eAAAA;;;;gEArBG;;;;;;;gEACJ;;;;;;;gEACA;;;;;;;gEACE;;;;;;;;;;;AAEjB;;;;CAIC,GACD,MAAMC,2BAA+E;IACnFC,QAAQC,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,OAAO,IAAI,WAAW,WAAW;IACtDC,OAAOJ,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,OAAO,IAAI,WAAW;IAC1CE,OAAOL,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,OAAO,IAAI,WAAW,SAAS,WAAW;AAChE;AAOO,SAASN;IACd,IAAIS,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5BC,IAAAA,iBAAM,EACJC,aAAE,CAACC,UAAU,CAACL,QAAQC,GAAG,CAACC,YAAY,GACtC,CAAC,oFAAoF,EAAEF,QAAQC,GAAG,CAACC,YAAY,EAAE;QAEnH,OAAOF,QAAQC,GAAG,CAACC,YAAY;IACjC;IAEA,IAAIF,QAAQC,GAAG,CAACK,gBAAgB,EAAE;QAChCH,IAAAA,iBAAM,EACJC,aAAE,CAACC,UAAU,CAACL,QAAQC,GAAG,CAACK,gBAAgB,GAC1C,CAAC,mGAAmG,EAAEN,QAAQC,GAAG,CAACK,gBAAgB,CAAC,2BAA2B,CAAC;QAEjK,OAAON,QAAQC,GAAG,CAACK,gBAAgB;IACrC;IAEA,MAAMC,kBAAkBf,wBAAwB,CAACQ,QAAQQ,QAAQ,CAAC;IAClE,IAAID,iBAAiB;QACnBJ,IAAAA,iBAAM,EACJC,aAAE,CAACC,UAAU,CAACE,kBACd,CAAC,4EAA4E,EAAEA,gBAAgB,mDAAmD,CAAC;QAErJ,OAAOA;IACT;IAEA,OAAO;AACT"}