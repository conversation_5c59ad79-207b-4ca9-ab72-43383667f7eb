module.exports = {

"[project]/apps/web/postcss.config.js/transform.ts { CONFIG => \"[project]/apps/web/postcss.config.js_.loader.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/_5cb1b857._.js",
  "build/chunks/[root-of-the-server]__b8aa4066._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/apps/web/postcss.config.js/transform.ts { CONFIG => \"[project]/apps/web/postcss.config.js_.loader.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}}),

};